import 'dart:convert';
import 'dart:io';

void main() async {
  var startTime = DateTime.now();

  print('清空flutter缓存...\n');

  var processClean = await Process.start('flutter', ['clean']);

  printStdInfo(processClean);

  await processClean.exitCode;

  print('获取当前系统环境变量...\n');

  Map<String, String> envVars = Map.from(Platform.environment);

  List<String> config = [];

  if (envVars['ENV_NAME'] != null) {
    envVars.forEach((key, value) {
      config.add('--dart-define=$key=$value');
    });
  } else {
    // 本地打包调试，注入默认参数
    config = [
      '--dart-define=ENV_NAME=fat',
      '--dart-define=CDN_DOMAIN=https://jojopublicfat.jojoread.com',
      '--dart-define=CDN_PREFIX=/jojoread/flutter-accompany/',
      '--dart-define=ENV_BASE=/read/accompany/',
      '--dart-define=CI_TAG_NAME=v0.113.15',
      '--dart-define=PUB_HOSTED_URL=https://pub.flutter-io.cn',
      '--dart-define=HOME=/root',
      '--dart-define=CI_SERVICE_NAME=jojoread-flutter-accompany-fe',
      '--dart-define=FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn',
      '--dart-define=CI_PROJECT_ID=1827'
    ];
  }

  print(config);

  print('\n开始flutter构建...\n');

  var processBuild = await Process.start(
    'flutter',
    [
      'build',
      'web',
      '--web-renderer',
      'html',
      '--release',
      ...config,
    ],
  );

  printStdInfo(processBuild);

  var processBuildExitCode = await processBuild.exitCode;

  if (processBuildExitCode != 0) {
    exit(processBuildExitCode);
  }

  print('处理打包产物...\n');

  var res = await Process.run(
    'dart',
    [
      'scripts/modified_file.dart',
    ],
  );

  print(res.stdout);

  if (res.stderr.isNotEmpty) {
    print(res.stderr);
  }

  var endTime = DateTime.now();
  var time = endTime.difference(startTime);

  print('flutter构建总耗时：${time.inMinutes % 60}分${time.inSeconds % 60}秒\n');

  print('exit code: ${res.exitCode}');

  exit(res.exitCode);
}

void printStdInfo(Process process) {
  process.stdout.transform(utf8.decoder).listen((data) {
    print(data);
  });

  process.stderr.transform(utf8.decoder).listen((data) {
    stderr.write(data);
  });
}
