import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/{{fileName}}/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/{{fileName}}/view.dart';

import '../../widgets/common/base_page.dart';
import 'controller';

class {{pageName}}PageModel extends BasePage {
  final String? courseKey;

  final String? classId;

  const {{pageName}}PageModel({Key? key, this.courseKey, this.classId}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _{{pageName}}PageModelState();
}

class _{{pageName}}PageModelState extends BaseState<{{pageName}}PageModel> with BasicInitPage {
  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => {{pageName}}Ctrl(classId: widget.classId ?? '', courseKey: widget.courseKey ?? ''),
      child: BlocBuilder<{{pageName}}Ctrl, {{pageName}}State>(builder: (context, state) {
        return {{pageName}}View(state: state);
      }),
    );
  }
}
