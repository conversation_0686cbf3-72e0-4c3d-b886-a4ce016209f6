import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:jojo_flutter_plan_pkg/pages/{{pageName}}/model/common_type.dart';
import 'package:jojo_flutter_plan_pkg/pages/{{pageName}}/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/{{pageName}}/widgets/example.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';

import 'package:jojo_flutter_plan_pkg/widgets/common/page_loading.dart';

import '../../utils/pt.dart';
import '../../widgets/app_bars/custom_app_bar.dart';
import 'controller';

class {{pageName}}View extends HookWidget {
  final {{pageName}}State state;

  const {{pageName}}View({Key? key, required this.state}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final pageStatus = state.pageStatus;
    return Scaffold(
      backgroundColor:  Colors.white,
      appBar: JoJoAppBar(title: '页面名称', backgroundColor: Colors.transparent, centerTitle: true),
      body: pageLoading(
        status: state.pageStatus,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(
                left: pt(20),
                right: pt(20),
                top: pt(10),
              ),
              child: Column(
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      '111',
                      style: TextStyle(
                        fontSize: pt(20),
                        color: colors[3],
                        fontWeight: FontWeight.w500,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  SizedBox(height: pt(16)),
              ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
