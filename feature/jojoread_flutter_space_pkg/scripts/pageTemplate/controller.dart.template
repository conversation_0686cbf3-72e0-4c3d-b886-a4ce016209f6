
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/sensors/sensors.dart';
import 'package:jojo_flutter_plan_pkg/pages/{{fileName}}/model/common_type.dart';
import 'package:jojo_flutter_plan_pkg/pages/{{fileName}}/state.dart';
import 'package:jojo_flutter_plan_pkg/service/{{fileName}}_api.dart';
import 'package:jojo_flutter_plan_pkg/widgets/common/page_loading.dart';


class {{pageName}}Ctrl extends Cubit<{{pageName}}State> {
  final String courseKey;

  final String classId;

  {{pageName}}Ctrl({required this.classId, required this.courseKey})
      : super(
          {{pageName}}State(
            {{pageName}}: const {{pageName}}(),
          ),
        ) {
    initState();
    log('pageInit: $classId courseKey: $courseKey');
  }

  changeTab(int index) {
    emit(state.copyWith()..activeTabIndex = index);
  }

  initState() async {
    // CommonData commonData = await {{pageName}}Service.getCourseInfoCommonData(
    //   classId: classId,
    //   courseKey: courseKey,
    // );

    RunEnv.sensorsTrack(
      'ElementView',
      {'c_element_name': ''},
    );

    final newState = state.copyWith()
    emit(newState);
  }







}
