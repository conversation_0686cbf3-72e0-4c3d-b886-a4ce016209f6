import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/brige.dart';
import 'package:jojo_flutter_plan_pkg/widgets/common/empty.dart';
import 'package:jojo_popup/jojo_popup.dart';

import '../../../common/sensors/sensors.dart';


Widget ExampleView(BuildContext context, {{pageName}}State state) {

  return Expanded(
    child: Container(
      color: Colors.white,
      child: const Text('aaaaa')
    ),
  );
}
