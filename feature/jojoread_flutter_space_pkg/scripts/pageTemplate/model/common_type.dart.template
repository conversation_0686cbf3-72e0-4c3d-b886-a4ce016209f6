import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part '{{fileName}}_data.freezed.dart';
part '{{fileName}}_data.g.dart';


@freezed
class {{pageName}}Data with _${{pageName}}Data {
  const factory {{pageName}}Data({
    int? courseChildType,
    String? classKey,
    int? classId,
    int? subjectType,
    String? courseKey,
    String? courseName,
    String? courseLabel,
    String? courseTypeIcon,
    int? teacherId,
    String? teacherName,
    int? activateStatus,
    int? currentSegmentId,
    bool? isLogisticsTab,
    RollLabelInfo? rollLabelInfo,
    List<TabList>? tabList,
  }) = _{{pageName}};
  factory {{pageName}}.fromJson(Map<String, dynamic> json) => _${{pageName}}FromJson(json);
}





