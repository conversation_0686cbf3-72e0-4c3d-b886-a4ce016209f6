import 'package:jojo_flutter_plan_pkg/pages/{{fileName}}/model/logistis.dart';
import 'package:jojo_flutter_plan_pkg/widgets/common/page_loading.dart';

import 'model/common_type.dart';

class {{pageName}}State {
  int tabIndex;

  PageStatus pageStatus;


  bool inited;

  {{pageName}}State({
    required this.inited, 
    required this.tabIndex,
    required this.pageStatus,
  });

  {{pageName}}State copyWith() {
    return {{pageName}}State(
      inited: inited,
      tabIndex: tabIndex, 
      required this.pageStatus
      );
  }
}
