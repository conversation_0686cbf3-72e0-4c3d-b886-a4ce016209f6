#!/usr/bin/env bash

GIT_DIR=$(git rev-parse --git-dir)
if [ $? -ne 0 ]; then
    echo "不是git项目，安装失败！"
    exit 1
fi
cd $GIT_DIR/..

gitUrl="***************:apppublic/flutter/jojo-devops/entry_guard.git"
gitBranch="develop"
folderTmp="_target_guard_tmp_"
folderScripts="scripts"
preCommitFile="pre-commit.sh"
preCommitMsgFile="prepare-commit-msg"
ciFile=".gitlab-ci.yml"
checkFile="analysis_options.yaml"
installFile="guard-install.sh"

echo "installing..."

if [ ! -d $folderScripts ]; then
  mkdir $folderScripts
fi

if [ ! -d $GIT_DIR/hooks ]; then
  mkdir $GIT_DIR/hooks
fi

mkdir $folderTmp

cd $folderTmp

git clone --branch $gitBranch $gitUrl .
if [ $? -ne 0 ]; then
  echo "门禁安装失败，需要申请git权限 $gitUrl"
  cd ..
  rm -rf $folderTmp
  exit 1
fi

cd ..

cp -r $folderTmp/$ciFile .
cp -r $folderTmp/$checkFile .

cp -r $folderTmp/$preCommitFile $folderScripts
chmod +x $folderScripts/$preCommitFile

cp -r $folderTmp/$preCommitMsgFile $GIT_DIR/hooks
chmod +x $GIT_DIR/hooks/$preCommitMsgFile

if [ -f $GIT_DIR/hooks/pre-commit ]; then
  rm -f $GIT_DIR/hooks/pre-commit
fi

ln -s ../../$folderScripts/$preCommitFile $GIT_DIR/hooks/pre-commit


rm -rf $folderTmp
echo "安装完成!"

# rm -rf $installFile
