stages:
  - publish_next
  - publish_release
  - dev
  - fat
  - uat
  - pro
  - check

publish_next:
  stage: publish_next
  script:
    - "/usr/bin/env bash ~/Desktop/flutter/ci/publish_package_next.sh $CI_COMMIT_REF_NAME release"
  when: manual
  only:
    refs:
      - /^v\d+(\.\d+){1,2}(-\w+(\.\d+)?)?$/
  except:
    - branches
  tags:
    - flutter

publish_release:
  stage: publish_release
  script:
    - "/usr/bin/env bash ~/Desktop/flutter/ci/publish_package.sh $CI_COMMIT_REF_NAME release"
  when: manual
  only:
    refs:
      - /^v\d+\.\d+\.\d+$/
  except:
    - branches
  tags:
    - flutter


sonar:
  stage: check
  script:
    - "/usr/bin/env bash $CI_BUILDS_DIR/ci/qa-flutter.sh"
  only:
    - merge_requests
    - master
  tags:
    - flutter-office
  interruptible: true


dev:
  stage: dev
  script:
    - "/usr/bin/env bash $CI_BUILDS_DIR/ci/build.sh"
  when: manual
  only:
    refs:
      - /^[t|v]\d+\.\d+\.\d+$/
  except:
    - branches
  tags:
    - flutter-office

fat:
  stage: fat
  script:
    - "/usr/bin/env bash $CI_BUILDS_DIR/ci/build.sh"
  when: manual
  only:
    refs:
      - /^[t|v]\d+\.\d+\.\d+$/
  except:
    - branches
  tags:
    - flutter-office

uat:
  stage: uat
  script:
    - "/usr/bin/env bash $CI_BUILDS_DIR/ci/build.sh"
  when: manual
  only:
    refs:
      - /^v\d+\.\d+\.\d+$/
  except:
    - branches
  tags:
    - flutter-office

pro:
  stage: pro
  script:
    - "/usr/bin/env bash $CI_BUILDS_DIR/ci/build.sh"
  when: manual
  only:
    refs:
      - /^v\d+\.\d+\.\d+$/
  except:
    - branches
  tags:
    - flutter-office

unit_test_coverage:
  stage: check
  script:
    - sh ~/Desktop/flutter/ci/unit_test.sh
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: on_success
  tags:
    - flutter
  interruptible: true