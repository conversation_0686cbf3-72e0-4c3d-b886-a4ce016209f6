# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/
.vscode/
# IntelliJ related
*.iml
*.ipr
*.iws
.idea/
.fvm/
.fvmrc
# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
# Libraries should not include pubspec.lock, per https://dart.dev/guides/libraries/private-files#pubspeclock.
/pubspec.lock
**/doc/api/
.dart_tool/
.packages
build/
.flutter-plugins*
linux/
macos/
.flutter-plugins-dependencies

# 单测覆盖率报告不上传
coverage
.flutter-plugins
pubspec.lock
