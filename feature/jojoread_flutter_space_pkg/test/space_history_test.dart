// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojoread_flutter_space_pkg/pages/history_list/controller.dart';

void main() {
  late SpaceHistoryPageCtrl controller;
  setUp(() {
    controller = SpaceHistoryPageCtrl();
  });
  group("SpaceHistoryPageCtrl Tests", () {
    WidgetsFlutterBinding.ensureInitialized();
    test("SpaceHistoryPageCtrl refresh", () {
      controller.onRefresh();
    });

    test("SpaceHistoryPageCtrl dispose", () {
      controller.dispose();
    });
  });
}
