// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'dart:io';

import 'package:archive/archive.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/models/exception_data.dart';
import 'package:jojoread_flutter_space_pkg/common/bridge/mock_brige.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/controller.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/component_switching_state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_group_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_resource_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_team_data.dart';
import 'package:mockito/mockito.dart';
import 'package:jojoread_flutter_space_pkg/service/space_home_api.dart';

class MockJoJoResourceManager extends Mock implements JoJoResourceManager {
  @override
  Future<void> downloadUrl(List<String> urlList,
      {Function(double)? progressListener,
      Function(Map<String, String>)? successListener,
      Function(UnifiedExceptionData)? failListener,
      bool isNeedCancel = false}) async {
    progressListener?.call(1.0);
    File file = File("memberBones.zip");
    file.createSync();
    File file2 = File("test.png");
    file2.createSync();
    successListener?.call({
      "memberBonesZipUrl": file.path,
      "ruleZipUrl": file.path,
      "spineSkinUrl": file2.path
    });
    failListener?.call(UnifiedExceptionData(code: UnifiedExceptionData.kCodeGenericError, message: "", reason: ""));
  }
}

class MockSpaceHomeApiService extends Mock implements SpaceHomeApi {
  @override
  Future<SpaceHomeResourceData> requestSpaceHomeResourceInfo() {
    return Future.value(const SpaceHomeResourceData(
        memberBonesZipUrl: "memberBonesZipUrl",
        ruleZipUrl: "ruleZipUrl",
        learnButtonInfo: ButtonInfo(
          buttonName: "",
          buttonUrl: "",
          iconUrl: "",
        ),
        backGroundZipUrl: "backGroundZipUrl",
        defaultSkinList: [DefaultSkinList(resourcesZipUrl: "spineSkinUrl")]));
  }

  @override
  Future<SpaceHomeTeamData> requestUserTeamsInfo() {
    return Future.value(SpaceHomeTeamData());
  }

  @override
  Future<SpaceHomeGroupData> requestSpaceHomeGroupInfo(int team_id) {
    return Future.value(SpaceHomeGroupData(
        teamMembers: [TeamMember(userId: 1, userName: "userName1")]));
  }

  @override
  Future<SpaceHomePopData> requestPopupsInfo(String scene) {
    return Future.value(SpaceHomePopData(popups: [
      Popup(
        type: 'MULTI_USER_LEARN_SEASON_START',
        order: 1,
        extend: Extend(),
      ),
      Popup(
        type: 'MULTI_USER_LEARN_DAY_RESULT',
        order: 2,
        extend: Extend(),
      )
    ]));
  }
}

class MockSpaceHomeFailApiService extends Mock implements SpaceHomeApi {
  @override
  Future<SpaceHomeResourceData> requestSpaceHomeResourceInfo() {
    return Future.value(const SpaceHomeResourceData(
        memberBonesZipUrl: "memberBonesZipUrl",
        ruleZipUrl: "ruleZipUrl",
        learnButtonInfo: ButtonInfo(
          buttonName: "",
          buttonUrl: "",
          iconUrl: "",
        ),
        backGroundZipUrl: "backGroundZipUrl",
        defaultSkinList: null));
  }

  @override
  Future<SpaceHomeTeamData> requestUserTeamsInfo() {
    return Future.value(SpaceHomeTeamData());
  }

  @override
  Future<SpaceHomeGroupData> requestSpaceHomeGroupInfo(int team_id) {
    throw Exception("Failed to get resource data");
  }
}

void main() {
  late SpaceHomePageCtrl controller;
  late MockJoJoResourceManager mockResourceManager;
  late MockSpaceHomeApiService mockSpaceHomeApiService;

  late SpaceHomePageCtrl failController;
  late MockSpaceHomeFailApiService mockSpaceHomeFailApiService;
  setUp(() {
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMocker());
    mockResourceManager = MockJoJoResourceManager();
    mockSpaceHomeApiService = MockSpaceHomeApiService();
    mockSpaceHomeFailApiService = MockSpaceHomeFailApiService();
    controller = SpaceHomePageCtrl(
        manager: mockResourceManager, homeApiService: mockSpaceHomeApiService);
    failController = SpaceHomePageCtrl(
        manager: mockResourceManager,
        homeApiService: mockSpaceHomeFailApiService);
  });
  group("SpaceHomePageCtrl Tests", () {
    WidgetsFlutterBinding.ensureInitialized();
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMocker());
    mockResourceManager = MockJoJoResourceManager();
    mockSpaceHomeApiService = MockSpaceHomeApiService();
    mockSpaceHomeFailApiService = MockSpaceHomeFailApiService();
    controller = SpaceHomePageCtrl(
        manager: mockResourceManager, homeApiService: mockSpaceHomeApiService);
    failController = SpaceHomePageCtrl(
        manager: mockResourceManager,
        homeApiService: mockSpaceHomeFailApiService);

    Future<Map> getMap(bool isFullName) async {
      File("memberBones.zip").createSync();
      File rule1 = File("rule1.png");
      rule1.createSync();

      File zipFile = File("memberBones.zip");
      zipFile.createSync();

      if (isFullName) {
        final archive = Archive();
        final bytes = await rule1.readAsBytes();
        archive.addFile(ArchiveFile("rule1.png", bytes.length, bytes));
        archive.addFile(ArchiveFile("rule2.png", bytes.length, bytes));
        archive.addFile(ArchiveFile("rule3.png", bytes.length, bytes));
        archive.addFile(ArchiveFile("test.atlas", bytes.length, bytes));
        archive.addFile(ArchiveFile("test.skel", bytes.length, bytes));
        archive.addFile(ArchiveFile("bgimage.png", bytes.length, bytes));
        archive.addFile(ArchiveFile("bgm.mp3", bytes.length, bytes));
        archive.addFile(ArchiveFile("stagebg.png", bytes.length, bytes));
        zipFile.writeAsBytes(ZipEncoder().encode(archive)!);
      }

      return {
        "memberBonesZipUrl": zipFile.path,
        "ruleZipUrl": zipFile.path,
        "spineSkinUrl": zipFile.path,
        "backGroundZipUrl": zipFile.path,
      };
    }

    test("SpaceHomePageCtrl refresh1", () {
      controller.onRefresh();
    });

    test("SpaceHomePageCtrl refresh2", () {
      failController.onRefresh();
    });

    test("SpaceHomePageCtrl downloadResource", () async {
      controller.downloadResource();
    });

    test("SpaceHomePageCtrl handlePersonSpineData", () async {
      controller.handlePersonSpineData(await getMap(true));
      controller.handlePersonSpineData(await getMap(false));
    });

    test("SpaceHomePageCtrl handleGuideRuleData", () async {
      controller.state.resourceData =
          const SpaceHomeResourceData(ruleZipUrl: "ruleZipUrl");
      controller.handleGuideRuleData(await getMap(true));
      controller.handleGuideRuleData(await getMap(false));
    });

    test("SpaceHomePageCtrl handleCustomSpineData", () async {
      controller.handleCustomSpineData("memberBones.zip");
      controller.getPopUpData();
    });

    test("SpaceHomePageCtrl handleBackgroundData", () async {
      controller.state.resourceData =
          const SpaceHomeResourceData(backGroundZipUrl: "backGroundZipUrl");
      controller.handleBackgroundData(await getMap(true));
      controller.handleBackgroundData(await getMap(false));
      controller.handleBackgroundData({});
    });

    test("SpaceHomePageCtrl dispose", () {
      controller.dispose();
    });

    test("saveSkin", () async {
      JoJoNativeBridge.registerMocker(JoJoBridgeCommonMocker());
      controller.saveSkin([1], ["1"], GlobalKey());
    });

    test("SpaceHomePageCtrl _setupAudioPlayer", () {
      controller.playBackground(true);
    });

    test("SpaceHomePageCtrl changePage", () {
      controller.changePage(FeatureSection.home);
      controller.changePage(FeatureSection.rules);
    });

    test("SpaceHomePageCtrl showHomePage", () {
      controller.showHomePage();
    });

    test("SpaceHomePageCtrl handleDealPopupData", () {
      controller.handleDealPopupData({}, SpaceHomePopData());
    });

    test(
      "SpaceHomePageCtrl getDealPopUpData",
      () {
        controller.getAchInfoUrl(const SpaceHomePopData(popups: [
          Popup(
            type: 'MULTI_USER_LEARN_SEASON_START',
            order: 1,
            extend: Extend(),
          ),
          Popup(
            type: 'MULTI_USER_LEARN_DAY_RESULT',
            order: 2,
            extend: Extend(),
          )
        ]));

        controller.getAchInfoUrl(const SpaceHomePopData());
      },
    );

    test("SpaceHomePageCtrl getPopUpData", () {
      controller.getPopUpData();
    });

    test("SpaceHomePageCtrl handleDownloadAchInfo", () {
      failController.handleDownloadAchInfo(
          'https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/748923303860145153/1735197226040rw3ep3.zip?checksumV2=md5Hex%3D76d4d560fac392543c65c0d33fcdeb0f',
          SpaceHomePopData());
    });
  });
}
