// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojoread_flutter_space_pkg/pages/detail_list/controller.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_contributions_data.dart';
import 'package:jojoread_flutter_space_pkg/service/space_home_api.dart';
import 'package:mockito/mockito.dart';

class MockSpaceHomeApiService extends Mock implements SpaceHomeApi {
  @override
  Future<SpaceHomeContributionsData> requestTeamContributionsInfo(int team_id) {
    return Future.value(const SpaceHomeContributionsData(
      teamId: 2,
      classId: 2,
      courseId: 2,
      contributions: [
        ContributionDetail(
            userId: 1,
            userName: 'deyanzhan',
            contributionType: 1,
            contributionVal: 10,
            contributionDesc: '',
            contributionDate: 2022 - 01 - 10)
      ],
    ));
  }
}

void main() {
  late SpaceTeamDetailsPageCtrl controller;
  late MockSpaceHomeApiService mockSpaceHomeApiService;

  setUp(() {
    mockSpaceHomeApiService = MockSpaceHomeApiService();
    controller = SpaceTeamDetailsPageCtrl(
        teamId: 2, homeApiService: mockSpaceHomeApiService);
  });
  group("SpaceTeamDetailsPageCtrl Tests", () {
    WidgetsFlutterBinding.ensureInitialized();
    test("SpaceTeamDetailsPageCtrl refresh", () {
      controller.onRefresh();
    });

    test("SpaceTeamDetailsPageCtrl dispose", () {
      controller.dispose();
    });
  });
}
