name: jojoread_flutter_space_pkg
description: A new Flutter project.
version: 0.0.9
homepage:
environment:
  sdk: ">=2.17.6 <4.0.0"
  flutter: ">=1.17.0"

scripts:
  # 启动web开发环境
  start:dev: dart run jojo_flutter_base:start_web ENV_NAME=dev ENV_BASE=/read/plan/
  start:fat: dart run jojo_flutter_base:start_web ENV_NAME=fat ENV_BASE=/read/plan/
  # 打包web环境
  build: dart run jojo_flutter_base:build_web
  # 本地测试打包web环境
  debugBuild: dart run jojo_flutter_base:build_web ENV_NAME=dev ENV_BASE=/read/plan/ CDN_DOMAIN=https://jojopublicfat.jojoread.com CDN_PREFIX=/jojoread/flutter-accompany/ CI_TAG_NAME=v0.113.15
  # 生成静态资源路径
  assets: dart run jojo_flutter_base:gen_assets
  # 生成model代码
  json2model: flutter packages pub run build_runner build --delete-conflicting-outputs

dependencies:
  jojo_flutter_base:
    path: ../../core/jojo_flutter_base
  spine_flutter:
    hosted: https://pub.xjjj.co/
    version: ^4.2.36
  flutter:
    sdk: flutter
  shared_preferences: ^2.2.2
  archive: ^3.4.2
  path:
  path_provider:

dev_dependencies:
  analyzer:
  build_runner:
  json_serializable:
  flutter_lints: ^2.0.0
  freezed:
  # crypto: 3.0.2
  retrofit_generator: 5.0.0+1
  flutter_test:
    sdk: flutter
  mockito: ^5.3.0

dependency_overrides:
  # video_player:
  #   hosted: https://pub.dev/
  # jojo_flutter_base:
  #   path: ../jojo_flutter_base_jiaojiao  


flutter:
  uses-material-design: true

  assets:
    - assets/img/
    - assets/svg/
    - assets/img/my_dress/
    - assets/audio/

  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   # - family: MohrRounded
  #   #   fonts:
  #   #     - asset: /packages/jojo_flutter_design/assets/fonts/MohrRounded-Black.ttf
  #   #     - asset: /packages/jojo_flutter_design/assets/fonts/MohrRounded-BlackIt.ttf
  #   #       style: normal
  #   #       weight: 700
  #   - family: NotoEmoji
  #     fonts:
  #       - asset: assets/fonts/NotoColorEmoji-Regular.ttf
  #         weight: 400
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true
  main_locale: zh_Hans
