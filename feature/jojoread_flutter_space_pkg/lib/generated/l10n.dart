// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `合计：`
  String get buyCarTotal {
    return Intl.message(
      '合计：',
      name: 'buyCarTotal',
      desc: '',
      args: [],
    );
  }

  /// `结算`
  String get checkout {
    return Intl.message(
      '结算',
      name: 'checkout',
      desc: '',
      args: [],
    );
  }

  /// `购买确认`
  String get confirmBuying {
    return Intl.message(
      '购买确认',
      name: 'confirmBuying',
      desc: '',
      args: [],
    );
  }

  /// `确认使用{price}{currency}购买当前装扮吗？`
  String myDressCheckoutTip(Object price, Object currency) {
    return Intl.message(
      '确认使用$price$currency购买当前装扮吗？',
      name: 'myDressCheckoutTip',
      desc: '',
      args: [price, currency],
    );
  }

  /// `（虚拟商品不支持退货）`
  String get myDressCheckoutSubTip {
    return Intl.message(
      '（虚拟商品不支持退货）',
      name: 'myDressCheckoutSubTip',
      desc: '',
      args: [],
    );
  }

  /// `请稍候`
  String get pleaseWait {
    return Intl.message(
      '请稍候',
      name: 'pleaseWait',
      desc: '',
      args: [],
    );
  }

  /// `确认`
  String get confirm {
    return Intl.message(
      '确认',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `再想想`
  String get thinkAgain {
    return Intl.message(
      '再想想',
      name: 'thinkAgain',
      desc: '',
      args: [],
    );
  }

  /// `宝石`
  String get gemstone {
    return Intl.message(
      '宝石',
      name: 'gemstone',
      desc: '',
      args: [],
    );
  }

  /// `成长豆`
  String get beans {
    return Intl.message(
      '成长豆',
      name: 'beans',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hans'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
