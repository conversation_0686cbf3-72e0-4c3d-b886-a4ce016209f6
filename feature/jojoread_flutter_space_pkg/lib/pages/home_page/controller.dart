import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_skin_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojoread_flutter_space_pkg/pages/eventBus/space_home_custom_eventbus.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/component_switching_state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/settlement_dialog_widget.dart';
import 'package:jojoread_flutter_space_pkg/utils/file_util.dart';

import '../../common/host_env/host_env.dart';
import '../../service/space_home_api.dart';
import '../../static/audio.dart';
import '../../utils/snap_shoot_util.dart';
import '../my_dress/state.dart';
import 'model/space_home_currentskin_data.dart';
import 'model/space_home_group_data.dart';
import 'model/space_home_resource_data.dart';
import 'model/space_home_team_data.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';

import 'model/space_mall_resource_data.dart';

class SpaceHomePageCtrl extends Cubit<SpaceHomePageState> {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  late final JoJoResourceManager? _resourceManager;
  late final JoJoResourceManager? _popupResourceManager;
  late final SpaceHomeApi _homeApiService;
  AudioPlayer? _audioPlayer;
  bool isInBackStage = false;
  //是否进入到换肤界面
  bool isEnterSkins = false;
  bool hasPlayedGudiTolearn = false; //是否播放过引导音频
  AudioPlayer? _guideToLearnAudioPlayer; //引导音频播放器

  SpaceHomePageCtrl({
    JoJoResourceManager? manager,
    SpaceHomeApi? homeApiService,
  }) : super(
          SpaceHomePageState(
            pageStatus: PageStatus.loading,
            resourceData: const SpaceHomeResourceData(),
            groupData: const SpaceHomeGroupData(),
            teamData: const SpaceHomeTeamData(),
            skinData: SpaceHomeCurrentskinData(),
            stageData: StageData(),
            guideRuleData: NewGuideRuleData(),
            guideSkinData: NewGuideSkinData(),
            backgroundData: BackgroundData(),
            featureSection: FeatureSection.rules,
            popData: const SpaceHomePopData(),
            isFirstSeasonStarted: false,
            isPopupUrlDownloadCompleted: false,
            hasEnteredRuleConfig: true,
            isNeedChangeSkin: false,
          ),
        ) {
    _resourceManager = manager ?? JoJoResourceManager();
    _popupResourceManager = manager ?? JoJoResourceManager();

    _homeApiService = homeApiService ?? spaceHomeApiService;
  }

  // 刷新页面
  // silent 是否静默刷新
  onRefresh({bool silent = false}) async {
    //请求接口
    try {
      if (!silent) {
        final newState = state.copyWith()..pageStatus = PageStatus.loading;
        emit(newState);
      }

      l.i(logTag, "用户开始请求时间：${DateTime.now()}");

      SpaceHomeTeamData? spaceHomeTeamData;
      SpaceHomeResourceData? spaceHomeResourceData;
      SpaceHomeGroupData? spaceHomeGroupData;
      SpaceHomeCurrentskinData? spaceHomeCurrentskinData;
      SpaceMallResourceData? spaceMallResourceData =
          const SpaceMallResourceData();

      //用户所在分组
      spaceHomeTeamData = await _homeApiService.requestUserTeamsInfo();
      //首页资源
      spaceHomeResourceData =
          await _homeApiService.requestSpaceHomeResourceInfo();
      //请求动画列表
      spaceMallResourceData = await _homeApiService.requestMallResourceInfo();
      // JSON字符串
      // String jsonString =
      //     '{"animationList":[{"type":"daily","animationgName":"idle"},{"type":"daily","animationgName":"pose1"},{"type":"activity","animationgName":"pose2"}]}';
      String jsonString = spaceMallResourceData.dressImgGenerateConfig ?? '';
      List<AnimationData> animationList = [];
      if (jsonString.isNotEmpty) {
        final Map<String, dynamic> jsonData = jsonDecode(jsonString);
        // 将List<dynamic>转换为List<Animation>
        animationList = (jsonData['animationList'] as List)
            .map((item) => AnimationData.fromJson(item))
            .toList();
      } //获取默认第一个分组信息
      List<Team>? teams = spaceHomeTeamData.teams ?? [];
      if (teams.isNotEmpty) {
        Team deafultTeam = teams.first;
        //请求分组详情
        spaceHomeGroupData = await _homeApiService
            .requestSpaceHomeGroupInfo(deafultTeam.teamId ?? 0);
      } else {
        //请求当前皮肤信息
        spaceHomeCurrentskinData =
            await _homeApiService.requestCurrentSkinInfo();
      }

      //组装数据
      state.teamData = spaceHomeTeamData;
      state.resourceData = spaceHomeResourceData;
      state.groupData = spaceHomeGroupData;
      state.skinData = spaceHomeCurrentskinData;
      state.animationList = animationList;
      //下载资源
      downloadResource();
    } catch (e) {
      //请求失败
      var exception = Exception("请求接口失败,$e");
      if (e is Exception) {
        exception = e;
      }
      final newState = state.copyWith()
        ..pageStatus = PageStatus.error
        ..exception = exception;
      l.e(logTag, "请求接口失败,$e");
      // 释放音频播放资源，重试会重新加载
      if (_audioPlayer != null) {
        await _audioPlayer?.stop();
        await _audioPlayer?.release();
      }
      print(
          '💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣-报错了$e');
      emit(newState);
    }
  }

  // 下载所有资源
  downloadResource() {
    List<String> urls = [];

    urls.addListsIfNotEmpty([
      state.resourceData?.ruleZipUrl,
      state.resourceData?.memberBonesZipUrl,
      state.resourceData?.backGroundZipUrl,
      state.resourceData?.likeZipUrl,
      state.resourceData?.prickZipUrl,
      state.resourceData?.boxZipUrl,
      state.resourceData?.flowerZipUrl,
    ]);

    state.resourceData?.ownAndDefaultSkinList?.forEach((element) {
      urls.addIfNotEmpty(element.resourcesZipUrl);
    });

    // 战队所有人皮肤
    state.groupData?.teamMembers?.forEach((element) {
      element.dressList?.forEach((e) {
        urls.addIfNotEmpty(e.resourcesZipUrl);
      });
    });

    //  我的皮肤
    state.skinData?.dressUps?.forEach((element) {
      urls.addIfNotEmpty(element.resourcesZipUrl);
    });

    _resourceManager?.downloadUrl(urls, progressListener: (progress) {
      //下载进度
      print("下载进度:$progress");
    }, successListener: (map) async {
      try {
        handleGuideSkinData(map);
        await handleGuideRuleData(map);
        await handlePersonSpineData(map);
        await handleOtherResource(map);
        await handleBackgroundData(map);

        /// 获取弹窗数据
        if (!isEnterSkins &&
            !SmartDialog.config.isExist &&
            state.featureSection != FeatureSection.buddyFinder) {
          await getPopUpData();
        }

        await showHomePage();
        if ((state.featureSection == FeatureSection.home) && isEnterSkins) {
          isEnterSkins = false;
        }

        l.i(logTag, "用户下载数据完成时间：${DateTime.now()}==${state.featureSection}");
      } catch (e) {
        final newState = state.copyWith()..pageStatus = PageStatus.error;
        emit(newState);
        l.e(logTag, "资源处理失败$e");
      }
    }, failListener: (_url) {
      //下载失败
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      l.e(logTag, "资源下载失败 $_url");
    });
  }

  //资源处理完毕,显示页面
  showHomePage() async {
    if (state.pageStatus == PageStatus.loading) {
      state.pageStatus = PageStatus.success;

      /// spaceHomeGroupData 中有战队数据并且 userType==1 的皮肤状态是否有数据，如果有直接进入主页，没有进入规则页面
      /// spaceHomeGroupData中 没有战队 就去查看spaceHomeCurrentskinData是否有数据 有就进入主页，没有进入规则页面
      var finishSection = FeatureSection.rules;
      if (state.groupData != null &&
          state.groupData!.teamMembers != null &&
          state.groupData!.teamMembers!.isNotEmpty) {
        final _teamMember = state.groupData?.teamMembers?.firstWhere(
          (element) => element.userType == 1,
          orElse: () => const TeamMember(),
        );
        if (_teamMember?.dressList != null &&
            _teamMember!.dressList!.isNotEmpty) {
          /// 如果当前弹窗有数据并且寻找学伴是第一个就需要先展示寻找学伴组件
          finishSection = state.isFirstSeasonStarted
              ? FeatureSection.buddyFinder
              : FeatureSection.home;
        }
      } else {
        if (state.skinData != null &&
            state.skinData!.dressUps != null &&
            state.skinData!.dressUps!.isNotEmpty) {
          finishSection = state.isFirstSeasonStarted
              ? FeatureSection.buddyFinder
              : FeatureSection.home;
        }
      }

      changePage(finishSection);
    } else if (state.pageStatus == PageStatus.success) {
      /// 处理刷新过后弹窗中第一个数据是寻找学伴 需要改变组件状态为寻找学伴，如果不是就正常展示弹窗数据
      if (state.isFirstSeasonStarted == true &&
          !isEnterSkins &&
          !SmartDialog.config.isExist) {
        changePage(FeatureSection.buddyFinder);
      } else if (state.popData.popups?.isNotEmpty == true && !isEnterSkins) {
        onShowPopup(
          SpaceHomePopData(popups: state.popData.popups),
        );
        emit(state.copyWith());
      } else {
        emit(state.copyWith());
      }
    } else {
      emit(state.copyWith());
    }
  }

  ///处理背景数据
  handleBackgroundData(Map map) async {
    var bgZipUrl = map[state.resourceData?.backGroundZipUrl];
    if (bgZipUrl != null) {
      var unzipFile = await unzip(bgZipUrl);
      var bgImage = findFilesByName(unzipFile, "bgimage.png");
      var bgMusic = findFilesByName(unzipFile, "bgm.mp3");
      var stageBg = findFilesByName(unzipFile, "stagebg.png");
      var atlasFile = findFilesByExtension(unzipFile, "atlas");
      var skelFile = findFilesByExtension(unzipFile, "skel");
      skelFile ??= findFilesByExtension(unzipFile, "json");
      if (atlasFile != null && skelFile != null) {
        state.backgroundData = BackgroundData(
          bgImageUrl: bgImage?.path,
          bgMusicUrl: bgMusic?.path,
          stageBgImageUrl: stageBg?.path,
        );
        state.stageData.stageSpineData = StageSpineData(
          skelFile: skelFile.path,
          atlasFile: atlasFile.path,
        );
      } else {
        final newState = state.copyWith()..pageStatus = PageStatus.error;
        emit(newState);
        l.e(logTag,
            "handleBackgroundData 找不到spine文件 ${state.resourceData?.backGroundZipUrl}");
      }
    } else {
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      l.e(logTag,
          "handleBackgroundData 找不到zip,${state.resourceData?.backGroundZipUrl}");
    }
  }

  ///处理人物模型数据
  handlePersonSpineData(Map map) async {
    var memberBonesZipUrl = map[state.resourceData?.memberBonesZipUrl];
    if (memberBonesZipUrl != null) {
      var unzipFile = await unzip(memberBonesZipUrl);
      var skinZipFile = findFilesByName(unzipFile, "defaultSkin.zip");
      var atlasFile = findFilesByExtensionByDic(unzipFile, "atlas");
      var skelFile = findFilesByExtensionByDic(unzipFile, "skel");
      skelFile ??= findFilesByExtensionByDic(unzipFile, "json");
      if (atlasFile != null && skelFile != null) {
        //找到atlas文件和skel文件
        final stageData = StageData();
        var stageOtherList = <StagePersonData>[];

        if (state.groupData != null) {
          //有战队的情况
          //先判断自己是否有皮肤变化
          var newSkinList = state.groupData?.teamMembers
              ?.firstWhere((element) => element.userType == 1,
                  orElse: () => const TeamMember())
              .dressList;
          var isChangeSkin = areSkinListsEqual(newSkinList,
              state.stageData.mineSpineData?.spineData?.skinList, map);
          state.isNeedChangeSkin = isChangeSkin;

          //遍历战队成员
          state.groupData?.teamMembers?.forEach((member) {
            List<SkinModel> skinList = [];
            skinList.addAll(member.dressList
                    ?.map((e) => SkinModel(
                        id: e.dressUpCategoryId,
                        zipLocalFile: map[e.resourcesZipUrl]))
                    .toList() ??
                []);
            if (skinZipFile != null) {
              skinList.insert(
                  0, SkinModel(id: 0, zipLocalFile: skinZipFile.path));
            }

            //获取老皮肤信息
            var oldSkinList = state.stageData.mineSpineData?.spineData;
            if (member.userType != 1) {
              oldSkinList = state.stageData.otherPersonSpineData
                  ?.firstWhere(
                      (element) => element.teamMember?.userId == member.userId,
                      orElse: () => StagePersonData())
                  .spineData;
            }

            ///判断是否需要更换皮肤
            var stageInfo = StagePersonData(
                teamMember: member,
                contributionUrl: member.userContribution
                    ?.firstWhere(
                      (element) => element.contributionType == 1,
                      orElse: () => const UserContribution(0, ""),
                    )
                    .titlePicUrl,
                spineData: isChangeSkin || oldSkinList == null
                    ? StageSpineData(
                        skelFile: skelFile!.path,
                        atlasFile: atlasFile.path,
                        skinList: skinList)
                    : oldSkinList);
            if (member.userType == 1) {
              //是自己的信息
              stageData.mineSpineData = stageInfo;
              var saveList = skinList.map((e) => e.zipLocalFile ?? "").toList();
              jojoNativeBridge.operationNativeValueSet(
                  key: skinValueKey,
                  value: CustomEventSyncUserDress(saveList).toJson());
            } else {
              stageOtherList.add(stageInfo);
            }
          });
          stageData.otherPersonSpineData = stageOtherList;
        } else {
          //没有战队的情况

          //先判断自己是否有皮肤变化
          var newSkinList = state.skinData?.dressUps;
          var isChangeSkin = areMineSkinListsEqual(newSkinList,
              state.stageData.mineSpineData?.spineData?.skinList, map);
          state.isNeedChangeSkin = isChangeSkin;

          List<SkinModel> skinList = [];
          skinList.addAll(state.skinData?.dressUps
                  ?.map((e) => SkinModel(
                      id: e.dressUpCategoryId,
                      zipLocalFile: map[e.resourcesZipUrl]))
                  .toList() ??
              []);
          if (skinZipFile != null) {
            skinList.insert(
                0, SkinModel(id: 0, zipLocalFile: skinZipFile.path));
          }
          var stageInfo = StagePersonData(
              teamMember: TeamMember(
                userName: state.skinData?.userName ?? '',
              ),
              contributionUrl: "",
              spineData: isChangeSkin
                  ? StageSpineData(
                      skelFile: skelFile.path,
                      atlasFile: atlasFile.path,
                      skinList: skinList)
                  : state.stageData.mineSpineData?.spineData);
          stageData.mineSpineData = stageInfo;

          var saveList = skinList.map((e) => e.zipLocalFile ?? "").toList();
          jojoNativeBridge.operationNativeValueSet(
              key: skinValueKey,
              value: CustomEventSyncUserDress(saveList).toJson());
        }

        state.guideSkinData.spineData = StageSpineData(
          skelFile: skelFile.path,
          atlasFile: atlasFile.path,
        );

        state.stageData = stageData;
      } else {
        final newState = state.copyWith()..pageStatus = PageStatus.error;
        emit(newState);
        l.e(logTag, "handlePersonSpineData 找不到文件,$memberBonesZipUrl");
      }
    } else {
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      l.e(logTag, "handlePersonSpineData 找不到压缩包,$memberBonesZipUrl");
    }
  }

  ///处理引导规则数据
  handleGuideRuleData(Map map) async {
    var rulesZipUrl = map[state.resourceData?.ruleZipUrl];
    if (rulesZipUrl != null) {
      var unzipFile = await unzip(rulesZipUrl);
      var rule1 = findFilesByName(unzipFile, "rule1.png");
      var rule2 = findFilesByName(unzipFile, "rule2.png");
      var rule3 = findFilesByName(unzipFile, "rule3.png");
      var atlasFile = findFilesByExtension(unzipFile, "atlas");
      var skelFile = findFilesByExtension(unzipFile, "skel");
      skelFile ??= findFilesByExtension(unzipFile, "json");
      if (atlasFile != null && skelFile != null) {
        //找到atlas文件和skel文件
        state.guideRuleData.ruleImageList = [
          rule1?.path,
          rule2?.path,
          rule3?.path
        ];
        state.guideRuleData.spineData = StageSpineData(
          skelFile: skelFile.path,
          atlasFile: atlasFile.path,
        );
      } else {
        final newState = state.copyWith()..pageStatus = PageStatus.error;
        emit(newState);
        l.e(logTag, "handleGuideRuleData 找不到文件,$rulesZipUrl");
      }
    } else {
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      l.e(logTag, "handleGuideRuleData error,$rulesZipUrl");
    }
  }

  //处理剩余的一些资源
  handleOtherResource(Map map) async {
    //点赞动画
    var likeSpine =
        await handleCustomSpineData(map[state.resourceData?.likeZipUrl]);
    state.stageData.likeSpineData = likeSpine;
    //戳一戳动画
    var prickSpine =
        await handleCustomSpineData(map[state.resourceData?.prickZipUrl]);
    state.stageData.prickSpineData = prickSpine;

    final boxSpine =
        await handleCustomSpineData(map[state.resourceData?.boxZipUrl]);
    state.stageData.boxSpineData = boxSpine;

    final flowerSpine =
        await handleCustomSpineData(map[state.resourceData?.flowerZipUrl]);
    state.stageData.flowerSpineData = flowerSpine;
  }

  ///处理通用spine模型数据
  Future<StageSpineData?> handleCustomSpineData(
      String? zipLocalFilePath) async {
    if (zipLocalFilePath != null) {
      var unzipFile = await unzip(zipLocalFilePath);
      var atlasFile = findFilesByExtension(unzipFile, "atlas");
      var skelFile = findFilesByExtension(unzipFile, "skel");
      skelFile ??= findFilesByExtension(unzipFile, "json");
      if (atlasFile != null && skelFile != null) {
        //找到atlas文件和skel文件
        return StageSpineData(
          skelFile: skelFile.path,
          atlasFile: atlasFile.path,
        );
      } else {
        final newState = state.copyWith()..pageStatus = PageStatus.error;
        emit(newState);
        l.e(logTag, "handleCustomSpineData找不到文件 $zipLocalFilePath");
      }
    } else {
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      l.e(logTag, "handleCustomSpineData找不到压缩包 $zipLocalFilePath");
    }
    return null;
  }

  ///处理引导换装数据
  handleGuideSkinData(Map map) {
    if (state.resourceData?.ownAndDefaultSkinList == null ||
        state.resourceData?.ownAndDefaultSkinList?.isEmpty == true) {
      //没有默认皮肤数据,显示加载失败
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      return;
    }
    state.guideSkinData.defaultSkinList = [];
    state.resourceData?.ownAndDefaultSkinList?.forEach((element) {
      state.guideSkinData.defaultSkinList?.add(DefaultSkinList(
        skinId: element.skinId,
        categoryShowCode: element.categoryShowCode,
        categoryShowName: element.categoryShowName,
        resourcesZipUrl: map[element.resourcesZipUrl],
        dressShowUrl: element.dressShowUrl,
      ));
    });
  }

  /// 切换页面功能
  void changePage(FeatureSection status) {
    print(
        "changePage $status, 当前状态${state.featureSection},isEnterSkins=$isEnterSkins");
    if (status == FeatureSection.skins || status == FeatureSection.rules) {
      //如果跳转到换肤页面了,记录下值,方便后面更新数据
      isEnterSkins = true;
    }
    if (status == FeatureSection.home && isEnterSkins) {
      //从换肤到主页,需要刷新数据
      onRefresh(silent: true);
    }
    // 寻找学伴到主页需要弹弹窗
    if (status == FeatureSection.home &&
        (state.featureSection == FeatureSection.buddyFinder ||
            state.featureSection == FeatureSection.skins) &&
        state.popData.popups?.isNotEmpty == true) {
      onShowPopup(
        SpaceHomePopData(popups: state.popData.popups),
        isFlag: true,
      );
    }
    // 如果开始页面状态是规则变为主页，需要弹弹窗
    if (status == FeatureSection.home &&
        state.featureSection == FeatureSection.rules &&
        state.popData.popups?.isNotEmpty == true) {
      onShowPopup(
        SpaceHomePopData(popups: state.popData.popups),
        isFlag: true,
      );
    }
    final newState = state.copyWith()..featureSection = status;
    if (status == FeatureSection.home) {
      _setupAudioPlayer(state.backgroundData?.bgMusicUrl ?? '', play: true);
    }

    emit(newState);
  }

  void _setupAudioPlayer(String? bgmPath, {bool play = true}) async {
    if (TextUtil.isNotEmpty(bgmPath)) {
      var url = bgmPath!;
      // 停止并释放之前的音频播放器
      if (_audioPlayer != null) {
        await _audioPlayer?.stop();
        await _audioPlayer?.release();
      }
      var player = AudioPlayer();
      await player.setSourceDeviceFile(url);
      await player.setReleaseMode(ReleaseMode.loop);
      _audioPlayer = player;

      _guideToLearnAudioPlayer = AudioPlayer();
      // 添加播放状态监听器
      _guideToLearnAudioPlayer?.onPlayerComplete.listen((event) {
        if (_guideToLearnAudioPlayer?.state == PlayerState.completed) {
          hasPlayedGudiTolearn = true;
        }
      });
      AudioPlayer.global.setGlobalAudioContext(
        const AudioContext(
          iOS: AudioContextIOS(
            category: AVAudioSessionCategory.playback,
            options: [
              AVAudioSessionOptions.mixWithOthers,
            ],
          ),
        ),
      );
      if (play && isInBackStage == false) {
        l.d('背景音播放排查',
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻_setupAudioPlayer调用playBackground isplay = true');
        playBackground(true);
      }
    }
  }

  void playBackground(bool isPlay) {
    if (state.pageStatus == PageStatus.error) {
      return;
    }
    l.d('背景音播放排查',
        '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻playBackground被调用SmartDialog.config.isExist = ${SmartDialog.config.isExist},isPlay = $isPlay');
    // 当前有弹窗显示在页面中 不播放背景音乐
    if (SmartDialog.config.isExist && isPlay == true) {
      return;
    }
    bool isNeedGoStudy = state.groupData?.teamMembers
            ?.firstWhere((element) => element.userType == 1,
                orElse: () => const TeamMember())
            .userStatus ==
        0; //是否有去学习
    l.d('背景音播放排查',
        '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻playBackground被调用hasPlayedGudiTolearn = $hasPlayedGudiTolearn,isNeedGoStudy = $isNeedGoStudy,_guideToLearnAudioPlayer = $_guideToLearnAudioPlayer');
    if (!hasPlayedGudiTolearn && isNeedGoStudy) {
      //只有未播放过引导音频且有学习按钮才播放引导
      if (isPlay) {
        l.d('背景音播放排查',
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻playBackground被调用_guideToLearnAudioPlayer = $_guideToLearnAudioPlayer,_guideToLearnAudioPlayer?.state = ${_guideToLearnAudioPlayer?.state}');
        if (_guideToLearnAudioPlayer?.state == PlayerState.paused) {
          _guideToLearnAudioPlayer?.resume();
        }
        if (_guideToLearnAudioPlayer?.state == PlayerState.stopped) {
          String? package = RunEnv.package;
          String path = AssetsAudio.GUIDETOLEARN;
          String keyName = package == null ? path : 'packages/$package/$path';
          _guideToLearnAudioPlayer?.audioCache.prefix = '';
          _guideToLearnAudioPlayer?.play(AssetSource(keyName));
        }
      } else {
        if (_guideToLearnAudioPlayer?.state == PlayerState.playing) {
          _guideToLearnAudioPlayer?.pause();
        }
      }
    }
    if (isPlay) {
      l.d('背景音播放排查',
          '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻playBackground被调用 _audioPlayer?.resume');
      _audioPlayer?.resume();
    } else {
      l.d('背景音播放排查',
          '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻playBackground被调用 _audioPlayer?.pause');
      _audioPlayer?.pause();
    }
  }

  Future<void> _snapshot(List<int> skins) async {
    //开始人物形象快照，并上传图片获取oss地址
    Completer<List<Map<String, dynamic>>>? completer = Completer();
    jojoEventBus.fire(MainPageDefaultSnapshootnEvent(
      state.animationList,
      completer: completer,
    ));
    List<Map<String, dynamic>> resultMap = await completer.future.timeout(
      const Duration(seconds: 5),
      onTimeout: () {
        print("等待超时");
        return [{}]; // 返回一个空的结果
      },
    );
    // 找出本地默认的截图
    String? ossPath = resultMap.firstWhere(
      (map) => map['type'] == 'daily',
      orElse: () => {},
    )['dressImg'] as String?;
    await _homeApiService.saveSkinInfo(skins, ossPath ?? '', resultMap);
  }

  /// 保存换肤
  void saveSkin(List<int> skins, List<String> skinLocalFiles,
      GlobalKey? repaintBoundaryKey) async {
    try {
      JoJoLoading.show();
      //开始人物形象快照，并上传图片获取oss地址
      await _snapshot(skins);
      if (state.stageData.mineSpineData?.spineData?.skelFile != null) {
        //如果模型已经赋值过了,则把新的皮肤换上
        state.isNeedChangeSkin = true;
        var newSkinList =
            skinLocalFiles.map((e) => SkinModel(zipLocalFile: e)).toList();
        state.stageData.mineSpineData = state.stageData.mineSpineData
            ?.copyWith()
          ?..spineData = StageSpineData(
              skelFile:
                  state.stageData.mineSpineData?.spineData?.skelFile ?? "",
              atlasFile:
                  state.stageData.mineSpineData?.spineData?.atlasFile ?? "",
              skinList: newSkinList);
      }

      // 切换状态
      JoJoLoading.dismiss();
      changePage(state.isFirstSeasonStarted
          ? FeatureSection.buddyFinder
          : FeatureSection.home);
    } catch (e) {
      deleteSnapshotWhenError();
      JoJoLoading.dismiss();
      changePage(state.isFirstSeasonStarted
          ? FeatureSection.buddyFinder
          : FeatureSection.home);
      l.e(logTag, '保存皮肤错误：$e');
    }
  }

  /// 获取弹窗数据
  Future<void> getPopUpData() async {
    try {
      state.isPopupUrlDownloadCompleted = false;
      l.i(logTag, '弹窗数据开始加载时间：${DateTime.now()}');
      final res =
          await spaceHomeApiService.requestPopupsInfo('MULTI_USER_LEARN');
      state.popData = res;

      /// 弹窗下载是否需要等待
      final _key = getAchInfoUrl(res);
      if (_key != null) {
        await handleDownloadAchInfo(_key, res);
      } else {
        state.isPopupUrlDownloadCompleted = true;
      }
      var _isFirstSeasonStarted = false;
      // 弹窗spine数据下载
      // 弹窗展示数据处理，如果弹窗第一个数据是寻找学伴,并且这个时候需要展示换肤数据，需要在皮肤装扮后展示寻找学伴页面，
      // 在展示主页，主页再弹出剩下弹窗
      // 如果寻找学伴不是第一个，就按照正常顺序弹出弹窗
      if (res.popups != null && res.popups!.isNotEmpty) {
        Popup? popup = res.popups!.firstWhere(
          (item) => item.order == 1,
          orElse: () => const Popup(),
        );

        /// 说明现在第一个数据是赛季开始
        if (popup.type == DialogType.startPopup.value) {
          _isFirstSeasonStarted = true;
        }
      }
      state.isFirstSeasonStarted = _isFirstSeasonStarted;
    } catch (e) {
      state.isPopupUrlDownloadCompleted = false;
      l.e(logTag, "获取弹窗数据失败,$e");
    }
  }

  /// 处理弹窗徽章数据，下载spine数据
  String? getAchInfoUrl(SpaceHomePopData res) {
    final popups = res.popups;
    String? _downloadUrls;
    if (popups != null && popups.isNotEmpty) {
      Popup? popup = popups.firstWhere(
        (item) => item.type == DialogType.weeklyPopup.value,
        orElse: () => const Popup(),
      );
      if (popup.type == DialogType.weeklyPopup.value) {
        _downloadUrls = popup.extend?.multiUserLearn?.achInfo?.resource;
      }
    }
    return _downloadUrls;
  }

  /// 下载弹窗中 成就spine数据
  Future<void> handleDownloadAchInfo(String key, SpaceHomePopData data) async {
    final Completer<void> downloadCompleter = Completer<void>();
    List<String> urls = [key];
    _popupResourceManager?.downloadUrl(urls, progressListener: (progress) {
      //下载进度
      print("弹窗资源下载进度:$progress");
    }, successListener: (map) async {
      try {
        state.isPopupUrlDownloadCompleted = true;
        await handleDealPopupData(map, data);
        downloadCompleter.complete(); // 标记下载成功
      } catch (e) {
        downloadCompleter.completeError(e); // 标记下载失败
      }
    }, failListener: (_url) {
      //下载失败
      l.e(logTag, "弹窗成就资源下载失败,$_url");
      state.isPopupUrlDownloadCompleted = false;
      downloadCompleter.completeError("下载失败: $_url"); // 标记下载失败
    });
    // 等待下载完成
    try {
      await downloadCompleter.future;
    } catch (e) {
      rethrow; // 如果需要上层处理，可以重新抛出异常
    }
  }

  handleDealPopupData(Map map, SpaceHomePopData popData) async {
    final _key = getAchInfoUrl(popData);

    if (_key != null) {
      final popupZipUrl = map[_key];
      if (popupZipUrl != null) {
        var unzipFile = await unzip(popupZipUrl);
        var atlasFile = findFilesByExtension(unzipFile, "atlas");
        var skelFile = findFilesByExtension(unzipFile, "skel");
        skelFile ??= findFilesByExtension(unzipFile, "json");
        if (atlasFile != null && skelFile != null) {
          //找到atlas文件和skel文件
          final _popups = popData.popups?.map((item) {
            if (item.type == DialogType.weeklyPopup.value) {
              final newResourceSpineData = ResourceSpineData(
                skelFile: skelFile!.path,
                atlasFile: atlasFile.path,
              );

              item = item.copyWith(
                extend: item.extend?.copyWith(
                  multiUserLearn: item.extend?.multiUserLearn?.copyWith(
                    achInfo: item.extend?.multiUserLearn?.achInfo?.copyWith(
                      resourceSpineData: newResourceSpineData,
                    ),
                  ),
                ),
              );

              return item;
            }

            return item;
          }).toList();
          final newState = state.copyWith()
            ..popData = SpaceHomePopData(popups: _popups);
          emit(newState);
          l.i(logTag, '弹窗开始弹出：${DateTime.now()}');
        } else {
          l.i(logTag, '没有找到弹窗资源下载数据');
        }
      } else {
        l.i(logTag, '没有找到弹窗资源下载数据');
      }
    }
  }

  // 显示弹窗
  onShowPopup(SpaceHomePopData data, {bool? isFlag}) {
    if ((isFlag == true || state.featureSection == FeatureSection.home) &&
        !SmartDialog.config.isExist) {
      PopupDialog().showAwardDialog(
        popData: data,
        isFirstSeasonStarted: state.isFirstSeasonStarted,
        stageData: state.stageData,
        hasEnteredRuleConfig: state.hasEnteredRuleConfig ?? false,
        sensorParams: commonSensorParams(),
        handlePlay: (bool isPlay) {
          print('');
          l.d('背景音播放排查',
              '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻显示弹窗调用playBackground isplay = $isPlay}');
          playBackground(isPlay);
        },
      );
    }
  }

  // 监听皮肤变化
  listenSkinChange() async {
    try {
      var skinListJson =
          (await jojoNativeBridge.operationNativeValueGet(key: skinValueKey))
                  .data
                  ?.value ??
              "";
      var skinEvent = CustomEventSyncUserDress.fromJson(skinListJson);
      var isChangeSkin = areChangeSkinListsEqual(
          skinEvent.dress, state.stageData.mineSpineData?.spineData?.skinList);
      state.isNeedChangeSkin = isChangeSkin;
      if (isChangeSkin) {
        var newSkinList =
            skinEvent.dress.map((e) => SkinModel(zipLocalFile: e)).toList();
        state.stageData.mineSpineData = state.stageData.mineSpineData
            ?.copyWith()
          ?..spineData = StageSpineData(
              skelFile:
                  state.stageData.mineSpineData?.spineData?.skelFile ?? "",
              atlasFile:
                  state.stageData.mineSpineData?.spineData?.atlasFile ?? "",
              skinList: newSkinList);
        emit(state.copyWith());
      }
    } catch (e) {
      l.e(logTag, "获取本地皮肤数据失败$e");
    }
  }

  onPause() {
    isInBackStage = true;
    l.d('背景音播放排查',
        '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻onPause调用playBackground isplay = false}');
    playBackground(false);
  }

  onResume() {
    listenSkinChange();
    isInBackStage = false;
    onRefresh(silent: true);
    l.d('背景音播放排查',
        '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻onResume调用playBackground isplay = true}');
    playBackground(true);
  }

  void dispose() {
    _resourceManager?.cancelDownload();
    _popupResourceManager?.cancelDownload();
    _audioPlayer?.stop();
    _audioPlayer?.dispose();
    _guideToLearnAudioPlayer?.stop();
    _guideToLearnAudioPlayer?.dispose();
  }

  Map<String, dynamic> commonSensorParams() {
    final _team = state.teamData?.teams?.first;
    return {
      'course_key': _team?.courseInfo?.courseKey,
      'class_key': _team?.classInfo?.classKey,
      'class_id': _team?.classInfo?.classId,
    };
  }
}

const logTag = "space_home_ctrl";
const skinValueKey = "custom_user_dress_up";
