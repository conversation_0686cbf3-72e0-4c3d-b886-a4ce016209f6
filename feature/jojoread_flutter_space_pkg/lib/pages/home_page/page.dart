import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/view.dart';

import 'controller.dart';

class SpaceHomePageModel extends BasePage {
  const SpaceHomePageModel({
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _SpacePageModelState();
}

class _SpacePageModelState extends BaseState<SpaceHomePageModel>
    with BasicInitPage {
  late final SpaceHomePageCtrl _controller = SpaceHomePageCtrl();
  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => _controller,
      child: BlocBuilder<SpaceHomePageCtrl, SpaceHomePageState>(
          builder: (context, state) {
        return SpaceHomePageView(state: state);
      }),
    );
  }

  @override
  void onResume() {
    super.onResume();
    _controller.onResume();
    l.i(tag, "onResume");
  }

  @override
  void onPause() {
    super.onPause();
    _controller.onPause();
    l.i(tag, "onPause");
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
    l.i(tag, "dispose");
  }
}

const tag = "SpaceHomePageModel";
