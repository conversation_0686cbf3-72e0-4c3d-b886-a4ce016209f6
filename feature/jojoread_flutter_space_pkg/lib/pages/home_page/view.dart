import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_lce.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/component_switching_state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/back_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/character_customization_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/find_study_buddy/find_study_buddy_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/rule_intro_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_info_widget.dart';
import 'package:jojoread_flutter_space_pkg/utils/file_util.dart';
import '../../common/host_env/host_env.dart';
import 'controller.dart';
import 'widget/space_home_stage_widget.dart';

class SpaceHomePageView extends StatefulHookWidget {
  final SpaceHomePageState state;
  const SpaceHomePageView({Key? key, required this.state}) : super(key: key);
  @override
  PlanHomeViewState createState() => PlanHomeViewState();
}

class PlanHomeViewState extends State<SpaceHomePageView> {
  final double containerWidth = screen.screenWidth;
  final double containerHeight = screen.screenHeight;
  //舞台高度
  var stageHeight = 460 / 375 * screen.screenWidth;
  //内容高度
  var contentHeight = screen.screenHeight - (460 / 375 * screen.screenWidth);
  bool isShowPopupFlag = false;

  @override
  void initState() {
    super.initState();
    if (isPadPortrait()) {
      stageHeight = screen.screenWidth;
      contentHeight = screen.screenHeight - stageHeight;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  bool validateInput(String? input) {
    return input?.isNotEmpty ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return JoJoPageLecLoading(
        status: widget.state.pageStatus,
        exception: widget.state.exception,
        emptyText: '暂无数据',
        retry: () {
          context.read<SpaceHomePageCtrl>().onRefresh();
        },
        backWidget: const JoJoAppBar(backgroundColor: Colors.transparent,),
        child: Scaffold(
          body: VisibilityObserve(
            onShow: () {
              RunEnv.sensorsTrack('ElementView', {
                'c_element_name': "多人学主界面曝光",
                ...?pageController?.commonSensorParams()
              });
            },
            child: widget.state.featureSection != FeatureSection.home
                ? widget.state.pageStatus == PageStatus.success
                    ? _buildWidget(widget.state.featureSection,
                        context.read<SpaceHomePageCtrl>())
                    : const SizedBox()
                : Container(
                    color: Colors.white,
                    child: Stack(
                      alignment: Alignment.topCenter,
                      clipBehavior: Clip.none, //不被裁剪
                      children: [
                        if (widget.state.backgroundData?.bgImageUrl != null)
                          Image.file(
                              File(widget.state.backgroundData?.bgImageUrl ??
                                  ''),
                              fit: BoxFit.fill,
                              width: containerWidth,
                              height: containerHeight, errorBuilder:
                                  (BuildContext context, Object exception,
                                      StackTrace? stackTrace) {
                            return Container(
                              color: HexColor('#FFF6E0'),
                            );
                          } //背景
                              ),
                        SpaceHomeStageWidget(
                          bgFile: widget.state.backgroundData?.stageBgImageUrl,
                          bgHeight: stageHeight,
                          stageData: widget.state.stageData,
                          groupData: widget.state.groupData,
                          isLoading: widget.state.isNeedChangeSkin ?? false,
                        ), //小组形象
                        // Positioned(
                        //   bottom: MediaQuery.of(context).padding.bottom,
                        //   child: const SpaceHomeInfoLogoWidget(),
                        // ), //小组形象
                        SpaceHomeInfoWidget(
                          state: widget.state,
                          miniHeight: contentHeight,
                        ),
                        const JoJoAppBar(
                            title: '',
                            backgroundColor: Colors.transparent,
                            centerTitle: true),
                      ],
                    ),
                  )),
        ),
      );
  }

  Widget _buildWidget(FeatureSection types, SpaceHomePageCtrl ctr) {
    if (types == FeatureSection.rules) {
      return const RuleIntroWidget();
    } else if (types == FeatureSection.skins) {
      return const CharacterCustomizationWidget();
    } else if (types == FeatureSection.buddyFinder &&
        ctr.state.popData.popups?.isNotEmpty == true) {
      return FindStudyBuddyWidget(
        extendData: ctr.state.popData.popups?[0].extend,
        onFindStudyBuddyClick: () {
          ctr.changePage(FeatureSection.home);
          // ctr.playBackground(true);
        },
        closeAudio: () {
          ctr.playBackground(false);
        },
      );
    } else {
      return const SizedBox();
    }
  }

  SpaceHomePageCtrl? get pageController =>
      mounted ? context.read<SpaceHomePageCtrl>() : null;
}
