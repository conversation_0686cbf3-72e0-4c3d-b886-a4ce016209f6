import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_skin_widget.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/component_switching_state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_group_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';

import '../my_dress/state.dart';
import 'model/space_home_currentskin_data.dart';
import 'model/space_home_resource_data.dart';
import 'model/space_home_team_data.dart';

class SpaceHomePageState {
  PageStatus pageStatus;
  Exception? exception;
  SpaceHomeResourceData? resourceData;
  SpaceHomeGroupData? groupData;
  SpaceHomeTeamData? teamData;
  SpaceHomeCurrentskinData? skinData;
  StageData stageData; //舞台数据
  NewGuideRuleData guideRuleData; //引导规则弹窗数据
  NewGuideSkinData guideSkinData; //引导换肤弹窗数据
  BackgroundData? backgroundData; //背景数据
  FeatureSection featureSection; // 页面功能切换
  SpaceHomePopData popData; //弹窗数据
  bool isFirstSeasonStarted; //弹窗第一个数据也是否是赛季开始
  bool? isPopupUrlDownloadCompleted; // 弹窗资源是否下载完成
  bool? hasEnteredRuleConfig; // 是否进入规则页面
  bool? isNeedChangeSkin; // 是否需要换肤
  List<AnimationData>? animationList;

  /// 页面功能切换

  SpaceHomePageState({
    this.exception,
    required this.pageStatus,
    required this.resourceData,
    required this.groupData,
    required this.teamData,
    required this.skinData,
    required this.stageData,
    required this.featureSection,
    required this.guideRuleData,
    required this.guideSkinData,
    required this.backgroundData,
    required this.popData,
    required this.isFirstSeasonStarted,
    required this.isPopupUrlDownloadCompleted,
    required this.hasEnteredRuleConfig,
    required this.isNeedChangeSkin,
    this.animationList,
  });

  SpaceHomePageState copyWith() {
    return SpaceHomePageState(
        pageStatus: pageStatus,
        resourceData: resourceData,
        groupData: groupData,
        teamData: teamData,
        skinData: skinData,
        stageData: stageData,
        featureSection: featureSection,
        guideRuleData: guideRuleData,
        guideSkinData: guideSkinData,
        backgroundData: backgroundData,
        popData: popData,
        isFirstSeasonStarted: isFirstSeasonStarted,
        isPopupUrlDownloadCompleted: isPopupUrlDownloadCompleted,
        hasEnteredRuleConfig: hasEnteredRuleConfig,
        isNeedChangeSkin: isNeedChangeSkin,
        animationList: animationList);
  }
}

//舞台数据
class StageData {
  //舞台动画数据
  StageSpineData? stageSpineData;
  //我的人物动画
  StagePersonData? mineSpineData;
  //点赞
  StageSpineData? likeSpineData;
  //戳一戳
  StageSpineData? prickSpineData;
  //其他两个人物动画
  List<StagePersonData>? otherPersonSpineData;

  StageSpineData? boxSpineData; // 箱子动画数据

  StageSpineData? flowerSpineData; // 彩带资源

  StageData({
    this.stageSpineData,
    this.mineSpineData,
    this.otherPersonSpineData,
  });
}

//背景数据
class BackgroundData {
  String? bgImageUrl; //背景图片
  String? bgMusicUrl; //背景音乐
  String? stageBgImageUrl; //舞台背景图片

  BackgroundData({this.bgImageUrl, this.bgMusicUrl, this.stageBgImageUrl});
}

//舞台小人
class StagePersonData {
  StageSpineData? spineData; // 人物模型
  TeamMember? teamMember; // 团队成员信息
  String? contributionUrl; // 1早鸟之王

  StagePersonData({
    this.spineData,
    this.teamMember,
    this.contributionUrl,
  });

  StagePersonData copyWith() {
    return StagePersonData(
      spineData: spineData,
      teamMember: teamMember,
      contributionUrl: contributionUrl,
    );
  }
}

//舞台动画数据
class StageSpineData {
  //  skel本地文件
  String skelFile;
  //  atlas本地文件
  String atlasFile;
  // 当前皮肤列表
  List<SkinModel>? skinList;

  StageSpineData({
    required this.skelFile,
    required this.atlasFile,
    this.skinList,
  });

  StageSpineData copyWith() {
    return StageSpineData(
      skelFile: skelFile,
      atlasFile: atlasFile,
      skinList: skinList,
    );
  }
}

/// 皮肤装扮缓存key
const cacheSkinKey = 'CACHE_SKIN_KEY';

/// 新手规则弹窗
class NewGuideRuleData {
  StageSpineData? spineData;
  List<String?>? ruleImageList;

  NewGuideRuleData({
    this.spineData,
    this.ruleImageList,
  });
}

/// 新手换肤弹窗
class NewGuideSkinData {
  StageSpineData? spineData;
  List<DefaultSkinList>? defaultSkinList;

  NewGuideSkinData({this.spineData, this.defaultSkinList});
}

enum DialogType {
  /// 日结算弹窗
  @JsonValue('MULTI_USER_LEARN_DAY_RESULT')
  dailyPopup('MULTI_USER_LEARN_DAY_RESULT'),

  /// 周（赛季）结算弹窗
  @JsonValue('MULTI_USER_LEARN_SEASON_RESULT')
  weeklyPopup('MULTI_USER_LEARN_SEASON_RESULT'),

  /// 赛季开始
  @JsonValue('MULTI_USER_LEARN_SEASON_START')
  startPopup('MULTI_USER_LEARN_SEASON_START');

  const DialogType(this.value);
  final String value;
}
