// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_history_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceHistoryData _$SpaceHistoryDataFromJson(Map<String, dynamic> json) {
  return _HistoryRecord.fromJson(json);
}

/// @nodoc
mixin _$SpaceHistoryData {
  List<HistoryPerformance>? get historyPerformances =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceHistoryDataCopyWith<SpaceHistoryData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceHistoryDataCopyWith<$Res> {
  factory $SpaceHistoryDataCopyWith(
          SpaceHistoryData value, $Res Function(SpaceHistoryData) then) =
      _$SpaceHistoryDataCopyWithImpl<$Res, SpaceHistoryData>;
  @useResult
  $Res call({List<HistoryPerformance>? historyPerformances});
}

/// @nodoc
class _$SpaceHistoryDataCopyWithImpl<$Res, $Val extends SpaceHistoryData>
    implements $SpaceHistoryDataCopyWith<$Res> {
  _$SpaceHistoryDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? historyPerformances = freezed,
  }) {
    return _then(_value.copyWith(
      historyPerformances: freezed == historyPerformances
          ? _value.historyPerformances
          : historyPerformances // ignore: cast_nullable_to_non_nullable
              as List<HistoryPerformance>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_HistoryRecordCopyWith<$Res>
    implements $SpaceHistoryDataCopyWith<$Res> {
  factory _$$_HistoryRecordCopyWith(
          _$_HistoryRecord value, $Res Function(_$_HistoryRecord) then) =
      __$$_HistoryRecordCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<HistoryPerformance>? historyPerformances});
}

/// @nodoc
class __$$_HistoryRecordCopyWithImpl<$Res>
    extends _$SpaceHistoryDataCopyWithImpl<$Res, _$_HistoryRecord>
    implements _$$_HistoryRecordCopyWith<$Res> {
  __$$_HistoryRecordCopyWithImpl(
      _$_HistoryRecord _value, $Res Function(_$_HistoryRecord) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? historyPerformances = freezed,
  }) {
    return _then(_$_HistoryRecord(
      historyPerformances: freezed == historyPerformances
          ? _value._historyPerformances
          : historyPerformances // ignore: cast_nullable_to_non_nullable
              as List<HistoryPerformance>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_HistoryRecord implements _HistoryRecord {
  const _$_HistoryRecord({final List<HistoryPerformance>? historyPerformances})
      : _historyPerformances = historyPerformances;

  factory _$_HistoryRecord.fromJson(Map<String, dynamic> json) =>
      _$$_HistoryRecordFromJson(json);

  final List<HistoryPerformance>? _historyPerformances;
  @override
  List<HistoryPerformance>? get historyPerformances {
    final value = _historyPerformances;
    if (value == null) return null;
    if (_historyPerformances is EqualUnmodifiableListView)
      return _historyPerformances;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SpaceHistoryData(historyPerformances: $historyPerformances)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_HistoryRecord &&
            const DeepCollectionEquality()
                .equals(other._historyPerformances, _historyPerformances));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_historyPerformances));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_HistoryRecordCopyWith<_$_HistoryRecord> get copyWith =>
      __$$_HistoryRecordCopyWithImpl<_$_HistoryRecord>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_HistoryRecordToJson(
      this,
    );
  }
}

abstract class _HistoryRecord implements SpaceHistoryData {
  const factory _HistoryRecord(
      {final List<HistoryPerformance>? historyPerformances}) = _$_HistoryRecord;

  factory _HistoryRecord.fromJson(Map<String, dynamic> json) =
      _$_HistoryRecord.fromJson;

  @override
  List<HistoryPerformance>? get historyPerformances;
  @override
  @JsonKey(ignore: true)
  _$$_HistoryRecordCopyWith<_$_HistoryRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

HistoryPerformance _$HistoryPerformanceFromJson(Map<String, dynamic> json) {
  return _HistoryPerformance.fromJson(json);
}

/// @nodoc
mixin _$HistoryPerformance {
  int? get order => throw _privateConstructorUsedError;
  String? get teamResUrl => throw _privateConstructorUsedError;
  int? get teamStatus => throw _privateConstructorUsedError;
  String? get teamStatusDesc => throw _privateConstructorUsedError;
  List<InfoTag>? get infoTags => throw _privateConstructorUsedError;
  List<AwardList>? get awardInfos => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistoryPerformanceCopyWith<HistoryPerformance> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryPerformanceCopyWith<$Res> {
  factory $HistoryPerformanceCopyWith(
          HistoryPerformance value, $Res Function(HistoryPerformance) then) =
      _$HistoryPerformanceCopyWithImpl<$Res, HistoryPerformance>;
  @useResult
  $Res call(
      {int? order,
      String? teamResUrl,
      int? teamStatus,
      String? teamStatusDesc,
      List<InfoTag>? infoTags,
      List<AwardList>? awardInfos});
}

/// @nodoc
class _$HistoryPerformanceCopyWithImpl<$Res, $Val extends HistoryPerformance>
    implements $HistoryPerformanceCopyWith<$Res> {
  _$HistoryPerformanceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? order = freezed,
    Object? teamResUrl = freezed,
    Object? teamStatus = freezed,
    Object? teamStatusDesc = freezed,
    Object? infoTags = freezed,
    Object? awardInfos = freezed,
  }) {
    return _then(_value.copyWith(
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      teamResUrl: freezed == teamResUrl
          ? _value.teamResUrl
          : teamResUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStatus: freezed == teamStatus
          ? _value.teamStatus
          : teamStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      teamStatusDesc: freezed == teamStatusDesc
          ? _value.teamStatusDesc
          : teamStatusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      infoTags: freezed == infoTags
          ? _value.infoTags
          : infoTags // ignore: cast_nullable_to_non_nullable
              as List<InfoTag>?,
      awardInfos: freezed == awardInfos
          ? _value.awardInfos
          : awardInfos // ignore: cast_nullable_to_non_nullable
              as List<AwardList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_HistoryPerformanceCopyWith<$Res>
    implements $HistoryPerformanceCopyWith<$Res> {
  factory _$$_HistoryPerformanceCopyWith(_$_HistoryPerformance value,
          $Res Function(_$_HistoryPerformance) then) =
      __$$_HistoryPerformanceCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? order,
      String? teamResUrl,
      int? teamStatus,
      String? teamStatusDesc,
      List<InfoTag>? infoTags,
      List<AwardList>? awardInfos});
}

/// @nodoc
class __$$_HistoryPerformanceCopyWithImpl<$Res>
    extends _$HistoryPerformanceCopyWithImpl<$Res, _$_HistoryPerformance>
    implements _$$_HistoryPerformanceCopyWith<$Res> {
  __$$_HistoryPerformanceCopyWithImpl(
      _$_HistoryPerformance _value, $Res Function(_$_HistoryPerformance) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? order = freezed,
    Object? teamResUrl = freezed,
    Object? teamStatus = freezed,
    Object? teamStatusDesc = freezed,
    Object? infoTags = freezed,
    Object? awardInfos = freezed,
  }) {
    return _then(_$_HistoryPerformance(
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      teamResUrl: freezed == teamResUrl
          ? _value.teamResUrl
          : teamResUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStatus: freezed == teamStatus
          ? _value.teamStatus
          : teamStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      teamStatusDesc: freezed == teamStatusDesc
          ? _value.teamStatusDesc
          : teamStatusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      infoTags: freezed == infoTags
          ? _value._infoTags
          : infoTags // ignore: cast_nullable_to_non_nullable
              as List<InfoTag>?,
      awardInfos: freezed == awardInfos
          ? _value._awardInfos
          : awardInfos // ignore: cast_nullable_to_non_nullable
              as List<AwardList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_HistoryPerformance implements _HistoryPerformance {
  const _$_HistoryPerformance(
      {this.order,
      this.teamResUrl,
      this.teamStatus,
      this.teamStatusDesc,
      final List<InfoTag>? infoTags,
      final List<AwardList>? awardInfos})
      : _infoTags = infoTags,
        _awardInfos = awardInfos;

  factory _$_HistoryPerformance.fromJson(Map<String, dynamic> json) =>
      _$$_HistoryPerformanceFromJson(json);

  @override
  final int? order;
  @override
  final String? teamResUrl;
  @override
  final int? teamStatus;
  @override
  final String? teamStatusDesc;
  final List<InfoTag>? _infoTags;
  @override
  List<InfoTag>? get infoTags {
    final value = _infoTags;
    if (value == null) return null;
    if (_infoTags is EqualUnmodifiableListView) return _infoTags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<AwardList>? _awardInfos;
  @override
  List<AwardList>? get awardInfos {
    final value = _awardInfos;
    if (value == null) return null;
    if (_awardInfos is EqualUnmodifiableListView) return _awardInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'HistoryPerformance(order: $order, teamResUrl: $teamResUrl, teamStatus: $teamStatus, teamStatusDesc: $teamStatusDesc, infoTags: $infoTags, awardInfos: $awardInfos)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_HistoryPerformance &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.teamResUrl, teamResUrl) ||
                other.teamResUrl == teamResUrl) &&
            (identical(other.teamStatus, teamStatus) ||
                other.teamStatus == teamStatus) &&
            (identical(other.teamStatusDesc, teamStatusDesc) ||
                other.teamStatusDesc == teamStatusDesc) &&
            const DeepCollectionEquality().equals(other._infoTags, _infoTags) &&
            const DeepCollectionEquality()
                .equals(other._awardInfos, _awardInfos));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      order,
      teamResUrl,
      teamStatus,
      teamStatusDesc,
      const DeepCollectionEquality().hash(_infoTags),
      const DeepCollectionEquality().hash(_awardInfos));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_HistoryPerformanceCopyWith<_$_HistoryPerformance> get copyWith =>
      __$$_HistoryPerformanceCopyWithImpl<_$_HistoryPerformance>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_HistoryPerformanceToJson(
      this,
    );
  }
}

abstract class _HistoryPerformance implements HistoryPerformance {
  const factory _HistoryPerformance(
      {final int? order,
      final String? teamResUrl,
      final int? teamStatus,
      final String? teamStatusDesc,
      final List<InfoTag>? infoTags,
      final List<AwardList>? awardInfos}) = _$_HistoryPerformance;

  factory _HistoryPerformance.fromJson(Map<String, dynamic> json) =
      _$_HistoryPerformance.fromJson;

  @override
  int? get order;
  @override
  String? get teamResUrl;
  @override
  int? get teamStatus;
  @override
  String? get teamStatusDesc;
  @override
  List<InfoTag>? get infoTags;
  @override
  List<AwardList>? get awardInfos;
  @override
  @JsonKey(ignore: true)
  _$$_HistoryPerformanceCopyWith<_$_HistoryPerformance> get copyWith =>
      throw _privateConstructorUsedError;
}

AwardList _$AwardListFromJson(Map<String, dynamic> json) {
  return _AwardList.fromJson(json);
}

/// @nodoc
mixin _$AwardList {
  String? get awardCover => throw _privateConstructorUsedError;
  String? get awardName => throw _privateConstructorUsedError;
  int? get awardCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AwardListCopyWith<AwardList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AwardListCopyWith<$Res> {
  factory $AwardListCopyWith(AwardList value, $Res Function(AwardList) then) =
      _$AwardListCopyWithImpl<$Res, AwardList>;
  @useResult
  $Res call({String? awardCover, String? awardName, int? awardCount});
}

/// @nodoc
class _$AwardListCopyWithImpl<$Res, $Val extends AwardList>
    implements $AwardListCopyWith<$Res> {
  _$AwardListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? awardCover = freezed,
    Object? awardName = freezed,
    Object? awardCount = freezed,
  }) {
    return _then(_value.copyWith(
      awardCover: freezed == awardCover
          ? _value.awardCover
          : awardCover // ignore: cast_nullable_to_non_nullable
              as String?,
      awardName: freezed == awardName
          ? _value.awardName
          : awardName // ignore: cast_nullable_to_non_nullable
              as String?,
      awardCount: freezed == awardCount
          ? _value.awardCount
          : awardCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AwardListCopyWith<$Res> implements $AwardListCopyWith<$Res> {
  factory _$$_AwardListCopyWith(
          _$_AwardList value, $Res Function(_$_AwardList) then) =
      __$$_AwardListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? awardCover, String? awardName, int? awardCount});
}

/// @nodoc
class __$$_AwardListCopyWithImpl<$Res>
    extends _$AwardListCopyWithImpl<$Res, _$_AwardList>
    implements _$$_AwardListCopyWith<$Res> {
  __$$_AwardListCopyWithImpl(
      _$_AwardList _value, $Res Function(_$_AwardList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? awardCover = freezed,
    Object? awardName = freezed,
    Object? awardCount = freezed,
  }) {
    return _then(_$_AwardList(
      awardCover: freezed == awardCover
          ? _value.awardCover
          : awardCover // ignore: cast_nullable_to_non_nullable
              as String?,
      awardName: freezed == awardName
          ? _value.awardName
          : awardName // ignore: cast_nullable_to_non_nullable
              as String?,
      awardCount: freezed == awardCount
          ? _value.awardCount
          : awardCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AwardList implements _AwardList {
  const _$_AwardList({this.awardCover, this.awardName, this.awardCount});

  factory _$_AwardList.fromJson(Map<String, dynamic> json) =>
      _$$_AwardListFromJson(json);

  @override
  final String? awardCover;
  @override
  final String? awardName;
  @override
  final int? awardCount;

  @override
  String toString() {
    return 'AwardList(awardCover: $awardCover, awardName: $awardName, awardCount: $awardCount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AwardList &&
            (identical(other.awardCover, awardCover) ||
                other.awardCover == awardCover) &&
            (identical(other.awardName, awardName) ||
                other.awardName == awardName) &&
            (identical(other.awardCount, awardCount) ||
                other.awardCount == awardCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, awardCover, awardName, awardCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AwardListCopyWith<_$_AwardList> get copyWith =>
      __$$_AwardListCopyWithImpl<_$_AwardList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AwardListToJson(
      this,
    );
  }
}

abstract class _AwardList implements AwardList {
  const factory _AwardList(
      {final String? awardCover,
      final String? awardName,
      final int? awardCount}) = _$_AwardList;

  factory _AwardList.fromJson(Map<String, dynamic> json) =
      _$_AwardList.fromJson;

  @override
  String? get awardCover;
  @override
  String? get awardName;
  @override
  int? get awardCount;
  @override
  @JsonKey(ignore: true)
  _$$_AwardListCopyWith<_$_AwardList> get copyWith =>
      throw _privateConstructorUsedError;
}

InfoTag _$InfoTagFromJson(Map<String, dynamic> json) {
  return _InfoTag.fromJson(json);
}

/// @nodoc
mixin _$InfoTag {
  int? get type => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $InfoTagCopyWith<InfoTag> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InfoTagCopyWith<$Res> {
  factory $InfoTagCopyWith(InfoTag value, $Res Function(InfoTag) then) =
      _$InfoTagCopyWithImpl<$Res, InfoTag>;
  @useResult
  $Res call({int? type, String? title, String? content});
}

/// @nodoc
class _$InfoTagCopyWithImpl<$Res, $Val extends InfoTag>
    implements $InfoTagCopyWith<$Res> {
  _$InfoTagCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? title = freezed,
    Object? content = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InfoTagCopyWith<$Res> implements $InfoTagCopyWith<$Res> {
  factory _$$_InfoTagCopyWith(
          _$_InfoTag value, $Res Function(_$_InfoTag) then) =
      __$$_InfoTagCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? type, String? title, String? content});
}

/// @nodoc
class __$$_InfoTagCopyWithImpl<$Res>
    extends _$InfoTagCopyWithImpl<$Res, _$_InfoTag>
    implements _$$_InfoTagCopyWith<$Res> {
  __$$_InfoTagCopyWithImpl(_$_InfoTag _value, $Res Function(_$_InfoTag) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? title = freezed,
    Object? content = freezed,
  }) {
    return _then(_$_InfoTag(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_InfoTag implements _InfoTag {
  const _$_InfoTag({this.type, this.title, this.content});

  factory _$_InfoTag.fromJson(Map<String, dynamic> json) =>
      _$$_InfoTagFromJson(json);

  @override
  final int? type;
  @override
  final String? title;
  @override
  final String? content;

  @override
  String toString() {
    return 'InfoTag(type: $type, title: $title, content: $content)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InfoTag &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, title, content);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InfoTagCopyWith<_$_InfoTag> get copyWith =>
      __$$_InfoTagCopyWithImpl<_$_InfoTag>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_InfoTagToJson(
      this,
    );
  }
}

abstract class _InfoTag implements InfoTag {
  const factory _InfoTag(
      {final int? type,
      final String? title,
      final String? content}) = _$_InfoTag;

  factory _InfoTag.fromJson(Map<String, dynamic> json) = _$_InfoTag.fromJson;

  @override
  int? get type;
  @override
  String? get title;
  @override
  String? get content;
  @override
  @JsonKey(ignore: true)
  _$$_InfoTagCopyWith<_$_InfoTag> get copyWith =>
      throw _privateConstructorUsedError;
}
