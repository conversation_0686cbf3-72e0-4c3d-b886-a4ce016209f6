// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_history_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_HistoryRecord _$$_HistoryRecordFromJson(Map<String, dynamic> json) =>
    _$_HistoryRecord(
      historyPerformances: (json['historyPerformances'] as List<dynamic>?)
          ?.map((e) => HistoryPerformance.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_HistoryRecordToJson(_$_HistoryRecord instance) =>
    <String, dynamic>{
      'historyPerformances': instance.historyPerformances,
    };

_$_HistoryPerformance _$$_HistoryPerformanceFromJson(
        Map<String, dynamic> json) =>
    _$_HistoryPerformance(
      order: json['order'] as int?,
      teamResUrl: json['teamResUrl'] as String?,
      teamStatus: json['teamStatus'] as int?,
      teamStatusDesc: json['teamStatusDesc'] as String?,
      infoTags: (json['infoTags'] as List<dynamic>?)
          ?.map((e) => InfoTag.fromJson(e as Map<String, dynamic>))
          .toList(),
      awardInfos: (json['awardInfos'] as List<dynamic>?)
          ?.map((e) => AwardList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_HistoryPerformanceToJson(
        _$_HistoryPerformance instance) =>
    <String, dynamic>{
      'order': instance.order,
      'teamResUrl': instance.teamResUrl,
      'teamStatus': instance.teamStatus,
      'teamStatusDesc': instance.teamStatusDesc,
      'infoTags': instance.infoTags,
      'awardInfos': instance.awardInfos,
    };

_$_AwardList _$$_AwardListFromJson(Map<String, dynamic> json) => _$_AwardList(
      awardCover: json['awardCover'] as String?,
      awardName: json['awardName'] as String?,
      awardCount: json['awardCount'] as int?,
    );

Map<String, dynamic> _$$_AwardListToJson(_$_AwardList instance) =>
    <String, dynamic>{
      'awardCover': instance.awardCover,
      'awardName': instance.awardName,
      'awardCount': instance.awardCount,
    };

_$_InfoTag _$$_InfoTagFromJson(Map<String, dynamic> json) => _$_InfoTag(
      type: json['type'] as int?,
      title: json['title'] as String?,
      content: json['content'] as String?,
    );

Map<String, dynamic> _$$_InfoTagToJson(_$_InfoTag instance) =>
    <String, dynamic>{
      'type': instance.type,
      'title': instance.title,
      'content': instance.content,
    };
