import 'package:jojo_flutter_base/base.dart';
part 'space_history_data.freezed.dart';
part 'space_history_data.g.dart';

@freezed
class SpaceHistoryData with _$SpaceHistoryData {
  const factory SpaceHistoryData({
    List<HistoryPerformance>? historyPerformances,
  }) = _HistoryRecord;

  factory SpaceHistoryData.fromJson(Map<String, dynamic> json) =>
      _$SpaceHistoryDataFromJson(json);
}

@freezed
class HistoryPerformance with _$HistoryPerformance {
  const factory HistoryPerformance({
    int? order,
    String? teamResUrl,
    int? teamStatus,
    String? teamStatusDesc,
    List<InfoTag>? infoTags,
    List<AwardList>? awardInfos,
  }) = _HistoryPerformance;

  factory HistoryPerformance.fromJson(Map<String, dynamic> json) =>
      _$HistoryPerformanceFromJson(json);
}

@freezed
class AwardList with _$AwardList {
  const factory AwardList({
    String? awardCover,
    String? awardName,
    int? awardCount,
  }) = _AwardList;

  factory AwardList.fromJson(Map<String, dynamic> json) =>
      _$AwardListFromJson(json);
}

@freezed
class InfoTag with _$InfoTag {
  const factory InfoTag({
    int? type,
    String? title,
    String? content,
  }) = _InfoTag;

  factory InfoTag.fromJson(Map<String, dynamic> json) =>
      _$InfoTagFromJson(json);
}
