import 'package:jojo_flutter_base/base.dart';

part 'space_mall_resource_data.freezed.dart';
part 'space_mall_resource_data.g.dart';

@freezed
class SpaceMallResourceData with _$SpaceMallResourceData {
  const factory SpaceMallResourceData({
    String? memberBonesZipUrl,
    String? backGroundZipUrl,
    List<CategoryButton>? categoryButtons,
    String? dressImgGenerateConfig,
    Map<String, String?>? dressUpRarityBackImgConfig,
    int? bestContinuousLearnDays,
    int? purchaseSwitch, //购买开关(1开启0关闭)
  }) = _SpaceMallResourceData;

  factory SpaceMallResourceData.fromJson(Map<String, dynamic> json) =>
      _$SpaceMallResourceDataFromJson(json);
}

@freezed
class CategoryButton with _$CategoryButton {
  const factory CategoryButton({
    String? iconUrl,
    String? buttonName,
    String? buttonSelectUrl,
    String? buttonUnSelectUrl,
    int? categoryId,
    String? spineSkinCategoryName,
    bool? reachBuyThreshold,
    String? buyGoodsTips,
  }) = _CategoryButton;

  factory CategoryButton.fromJson(Map<String, dynamic> json) =>
      _$CategoryButtonFromJson(json);
}

extension SpaceMallResourceDataExtension on SpaceMallResourceData {
  /// 根据 dressUpLevel 获取对应的背景图片 URL
  String? getBackgroundImageUrl(String? dressUpLevel) {
    if (dressUpLevel == null || dressUpRarityBackImgConfig == null) {
      return null;
    }
    return dressUpRarityBackImgConfig![dressUpLevel];
  }
}
