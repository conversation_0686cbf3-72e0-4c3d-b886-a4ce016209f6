import 'package:jojo_flutter_base/base.dart';
part 'space_home_team_data.freezed.dart';
part 'space_home_team_data.g.dart';

@freezed
class SpaceHomeTeamData with _$SpaceHomeTeamData {
  const factory SpaceHomeTeamData({
    String? notTeamsTip,
    List<Team>? teams,
  }) = _SpaceHomeTeamData;

  factory SpaceHomeTeamData.fromJson(Map<String, dynamic> json) =>
      _$SpaceHomeTeamDataFromJson(json);
}

@freezed
class Team with _$Team {
  const factory Team({
    String? className,
    int? teamId,
    CourseInfo? courseInfo,
    ClassInfo? classInfo,
  }) = _Team;

  factory Team.fromJson(Map<String, dynamic> json) => _$TeamFromJson(json);
}

@freezed
class CourseInfo with _$CourseInfo {
  const factory CourseInfo({
    int? courseId,
    String? courseKey,
    String? courseName,
  }) = _CourseInfo;

  factory CourseInfo.fromJson(Map<String, dynamic> json) =>
      _$CourseInfoFromJson(json);
}

@freezed
class ClassInfo with _$ClassInfo {
  const factory ClassInfo({
    int? classId,
    String? classKey,
    String? className,
  }) = _ClassInfo;

  factory ClassInfo.fromJson(Map<String, dynamic> json) =>
      _$ClassInfoFromJson(json);
}
