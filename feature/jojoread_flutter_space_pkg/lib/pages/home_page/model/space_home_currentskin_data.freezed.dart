// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_home_currentskin_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceHomeCurrentskinData _$SpaceHomeCurrentskinDataFromJson(
    Map<String, dynamic> json) {
  return _SpaceHomeCurrentskinData.fromJson(json);
}

/// @nodoc
mixin _$SpaceHomeCurrentskinData {
  int? get userId => throw _privateConstructorUsedError;
  set userId(int? value) => throw _privateConstructorUsedError;
  String? get userName => throw _privateConstructorUsedError;
  set userName(String? value) => throw _privateConstructorUsedError;
  List<CurrentUserDressUps>? get dressUps => throw _privateConstructorUsedError;
  set dressUps(List<CurrentUserDressUps>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceHomeCurrentskinDataCopyWith<SpaceHomeCurrentskinData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceHomeCurrentskinDataCopyWith<$Res> {
  factory $SpaceHomeCurrentskinDataCopyWith(SpaceHomeCurrentskinData value,
          $Res Function(SpaceHomeCurrentskinData) then) =
      _$SpaceHomeCurrentskinDataCopyWithImpl<$Res, SpaceHomeCurrentskinData>;
  @useResult
  $Res call(
      {int? userId, String? userName, List<CurrentUserDressUps>? dressUps});
}

/// @nodoc
class _$SpaceHomeCurrentskinDataCopyWithImpl<$Res,
        $Val extends SpaceHomeCurrentskinData>
    implements $SpaceHomeCurrentskinDataCopyWith<$Res> {
  _$SpaceHomeCurrentskinDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? userName = freezed,
    Object? dressUps = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
      dressUps: freezed == dressUps
          ? _value.dressUps
          : dressUps // ignore: cast_nullable_to_non_nullable
              as List<CurrentUserDressUps>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SpaceHomeCurrentskinDataCopyWith<$Res>
    implements $SpaceHomeCurrentskinDataCopyWith<$Res> {
  factory _$$_SpaceHomeCurrentskinDataCopyWith(
          _$_SpaceHomeCurrentskinData value,
          $Res Function(_$_SpaceHomeCurrentskinData) then) =
      __$$_SpaceHomeCurrentskinDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? userId, String? userName, List<CurrentUserDressUps>? dressUps});
}

/// @nodoc
class __$$_SpaceHomeCurrentskinDataCopyWithImpl<$Res>
    extends _$SpaceHomeCurrentskinDataCopyWithImpl<$Res,
        _$_SpaceHomeCurrentskinData>
    implements _$$_SpaceHomeCurrentskinDataCopyWith<$Res> {
  __$$_SpaceHomeCurrentskinDataCopyWithImpl(_$_SpaceHomeCurrentskinData _value,
      $Res Function(_$_SpaceHomeCurrentskinData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? userName = freezed,
    Object? dressUps = freezed,
  }) {
    return _then(_$_SpaceHomeCurrentskinData(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
      dressUps: freezed == dressUps
          ? _value.dressUps
          : dressUps // ignore: cast_nullable_to_non_nullable
              as List<CurrentUserDressUps>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpaceHomeCurrentskinData implements _SpaceHomeCurrentskinData {
  _$_SpaceHomeCurrentskinData({this.userId, this.userName, this.dressUps});

  factory _$_SpaceHomeCurrentskinData.fromJson(Map<String, dynamic> json) =>
      _$$_SpaceHomeCurrentskinDataFromJson(json);

  @override
  int? userId;
  @override
  String? userName;
  @override
  List<CurrentUserDressUps>? dressUps;

  @override
  String toString() {
    return 'SpaceHomeCurrentskinData(userId: $userId, userName: $userName, dressUps: $dressUps)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpaceHomeCurrentskinDataCopyWith<_$_SpaceHomeCurrentskinData>
      get copyWith => __$$_SpaceHomeCurrentskinDataCopyWithImpl<
          _$_SpaceHomeCurrentskinData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpaceHomeCurrentskinDataToJson(
      this,
    );
  }
}

abstract class _SpaceHomeCurrentskinData implements SpaceHomeCurrentskinData {
  factory _SpaceHomeCurrentskinData(
      {int? userId,
      String? userName,
      List<CurrentUserDressUps>? dressUps}) = _$_SpaceHomeCurrentskinData;

  factory _SpaceHomeCurrentskinData.fromJson(Map<String, dynamic> json) =
      _$_SpaceHomeCurrentskinData.fromJson;

  @override
  int? get userId;
  set userId(int? value);
  @override
  String? get userName;
  set userName(String? value);
  @override
  List<CurrentUserDressUps>? get dressUps;
  set dressUps(List<CurrentUserDressUps>? value);
  @override
  @JsonKey(ignore: true)
  _$$_SpaceHomeCurrentskinDataCopyWith<_$_SpaceHomeCurrentskinData>
      get copyWith => throw _privateConstructorUsedError;
}

CurrentUserDressUps _$CurrentUserDressUpsFromJson(Map<String, dynamic> json) {
  return _CurrentUserDressUps.fromJson(json);
}

/// @nodoc
mixin _$CurrentUserDressUps {
  int? get dressUpId => throw _privateConstructorUsedError;
  int? get dressUpCategoryId => throw _privateConstructorUsedError;
  String? get spineDressUpCategoryName => throw _privateConstructorUsedError;
  String? get dressShowUrl => throw _privateConstructorUsedError;
  String? get resourcesZipUrl => throw _privateConstructorUsedError;
  String? get price => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  List<CurrentUserDressUps?>? get children =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CurrentUserDressUpsCopyWith<CurrentUserDressUps> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CurrentUserDressUpsCopyWith<$Res> {
  factory $CurrentUserDressUpsCopyWith(
          CurrentUserDressUps value, $Res Function(CurrentUserDressUps) then) =
      _$CurrentUserDressUpsCopyWithImpl<$Res, CurrentUserDressUps>;
  @useResult
  $Res call(
      {int? dressUpId,
      int? dressUpCategoryId,
      String? spineDressUpCategoryName,
      String? dressShowUrl,
      String? resourcesZipUrl,
      String? price,
      int? status,
      List<CurrentUserDressUps?>? children});
}

/// @nodoc
class _$CurrentUserDressUpsCopyWithImpl<$Res, $Val extends CurrentUserDressUps>
    implements $CurrentUserDressUpsCopyWith<$Res> {
  _$CurrentUserDressUpsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dressUpId = freezed,
    Object? dressUpCategoryId = freezed,
    Object? spineDressUpCategoryName = freezed,
    Object? dressShowUrl = freezed,
    Object? resourcesZipUrl = freezed,
    Object? price = freezed,
    Object? status = freezed,
    Object? children = freezed,
  }) {
    return _then(_value.copyWith(
      dressUpId: freezed == dressUpId
          ? _value.dressUpId
          : dressUpId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressUpCategoryId: freezed == dressUpCategoryId
          ? _value.dressUpCategoryId
          : dressUpCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      spineDressUpCategoryName: freezed == spineDressUpCategoryName
          ? _value.spineDressUpCategoryName
          : spineDressUpCategoryName // ignore: cast_nullable_to_non_nullable
              as String?,
      dressShowUrl: freezed == dressShowUrl
          ? _value.dressShowUrl
          : dressShowUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      resourcesZipUrl: freezed == resourcesZipUrl
          ? _value.resourcesZipUrl
          : resourcesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      children: freezed == children
          ? _value.children
          : children // ignore: cast_nullable_to_non_nullable
              as List<CurrentUserDressUps?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CurrentUserDressUpsCopyWith<$Res>
    implements $CurrentUserDressUpsCopyWith<$Res> {
  factory _$$_CurrentUserDressUpsCopyWith(_$_CurrentUserDressUps value,
          $Res Function(_$_CurrentUserDressUps) then) =
      __$$_CurrentUserDressUpsCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? dressUpId,
      int? dressUpCategoryId,
      String? spineDressUpCategoryName,
      String? dressShowUrl,
      String? resourcesZipUrl,
      String? price,
      int? status,
      List<CurrentUserDressUps?>? children});
}

/// @nodoc
class __$$_CurrentUserDressUpsCopyWithImpl<$Res>
    extends _$CurrentUserDressUpsCopyWithImpl<$Res, _$_CurrentUserDressUps>
    implements _$$_CurrentUserDressUpsCopyWith<$Res> {
  __$$_CurrentUserDressUpsCopyWithImpl(_$_CurrentUserDressUps _value,
      $Res Function(_$_CurrentUserDressUps) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dressUpId = freezed,
    Object? dressUpCategoryId = freezed,
    Object? spineDressUpCategoryName = freezed,
    Object? dressShowUrl = freezed,
    Object? resourcesZipUrl = freezed,
    Object? price = freezed,
    Object? status = freezed,
    Object? children = freezed,
  }) {
    return _then(_$_CurrentUserDressUps(
      dressUpId: freezed == dressUpId
          ? _value.dressUpId
          : dressUpId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressUpCategoryId: freezed == dressUpCategoryId
          ? _value.dressUpCategoryId
          : dressUpCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      spineDressUpCategoryName: freezed == spineDressUpCategoryName
          ? _value.spineDressUpCategoryName
          : spineDressUpCategoryName // ignore: cast_nullable_to_non_nullable
              as String?,
      dressShowUrl: freezed == dressShowUrl
          ? _value.dressShowUrl
          : dressShowUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      resourcesZipUrl: freezed == resourcesZipUrl
          ? _value.resourcesZipUrl
          : resourcesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      children: freezed == children
          ? _value._children
          : children // ignore: cast_nullable_to_non_nullable
              as List<CurrentUserDressUps?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CurrentUserDressUps implements _CurrentUserDressUps {
  const _$_CurrentUserDressUps(
      {this.dressUpId,
      this.dressUpCategoryId,
      this.spineDressUpCategoryName,
      this.dressShowUrl,
      this.resourcesZipUrl,
      this.price,
      this.status,
      final List<CurrentUserDressUps?>? children})
      : _children = children;

  factory _$_CurrentUserDressUps.fromJson(Map<String, dynamic> json) =>
      _$$_CurrentUserDressUpsFromJson(json);

  @override
  final int? dressUpId;
  @override
  final int? dressUpCategoryId;
  @override
  final String? spineDressUpCategoryName;
  @override
  final String? dressShowUrl;
  @override
  final String? resourcesZipUrl;
  @override
  final String? price;
  @override
  final int? status;
  final List<CurrentUserDressUps?>? _children;
  @override
  List<CurrentUserDressUps?>? get children {
    final value = _children;
    if (value == null) return null;
    if (_children is EqualUnmodifiableListView) return _children;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CurrentUserDressUps(dressUpId: $dressUpId, dressUpCategoryId: $dressUpCategoryId, spineDressUpCategoryName: $spineDressUpCategoryName, dressShowUrl: $dressShowUrl, resourcesZipUrl: $resourcesZipUrl, price: $price, status: $status, children: $children)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CurrentUserDressUps &&
            (identical(other.dressUpId, dressUpId) ||
                other.dressUpId == dressUpId) &&
            (identical(other.dressUpCategoryId, dressUpCategoryId) ||
                other.dressUpCategoryId == dressUpCategoryId) &&
            (identical(
                    other.spineDressUpCategoryName, spineDressUpCategoryName) ||
                other.spineDressUpCategoryName == spineDressUpCategoryName) &&
            (identical(other.dressShowUrl, dressShowUrl) ||
                other.dressShowUrl == dressShowUrl) &&
            (identical(other.resourcesZipUrl, resourcesZipUrl) ||
                other.resourcesZipUrl == resourcesZipUrl) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._children, _children));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      dressUpId,
      dressUpCategoryId,
      spineDressUpCategoryName,
      dressShowUrl,
      resourcesZipUrl,
      price,
      status,
      const DeepCollectionEquality().hash(_children));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CurrentUserDressUpsCopyWith<_$_CurrentUserDressUps> get copyWith =>
      __$$_CurrentUserDressUpsCopyWithImpl<_$_CurrentUserDressUps>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CurrentUserDressUpsToJson(
      this,
    );
  }
}

abstract class _CurrentUserDressUps implements CurrentUserDressUps {
  const factory _CurrentUserDressUps(
      {final int? dressUpId,
      final int? dressUpCategoryId,
      final String? spineDressUpCategoryName,
      final String? dressShowUrl,
      final String? resourcesZipUrl,
      final String? price,
      final int? status,
      final List<CurrentUserDressUps?>? children}) = _$_CurrentUserDressUps;

  factory _CurrentUserDressUps.fromJson(Map<String, dynamic> json) =
      _$_CurrentUserDressUps.fromJson;

  @override
  int? get dressUpId;
  @override
  int? get dressUpCategoryId;
  @override
  String? get spineDressUpCategoryName;
  @override
  String? get dressShowUrl;
  @override
  String? get resourcesZipUrl;
  @override
  String? get price;
  @override
  int? get status;
  @override
  List<CurrentUserDressUps?>? get children;
  @override
  @JsonKey(ignore: true)
  _$$_CurrentUserDressUpsCopyWith<_$_CurrentUserDressUps> get copyWith =>
      throw _privateConstructorUsedError;
}
