// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_home_pop_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceHomePopData _$SpaceHomePopDataFromJson(Map<String, dynamic> json) {
  return _SpaceHomePopData.fromJson(json);
}

/// @nodoc
mixin _$SpaceHomePopData {
  List<Popup>? get popups => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceHomePopDataCopyWith<SpaceHomePopData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceHomePopDataCopyWith<$Res> {
  factory $SpaceHomePopDataCopyWith(
          SpaceHomePopData value, $Res Function(SpaceHomePopData) then) =
      _$SpaceHomePopDataCopyWithImpl<$Res, SpaceHomePopData>;
  @useResult
  $Res call({List<Popup>? popups});
}

/// @nodoc
class _$SpaceHomePopDataCopyWithImpl<$Res, $Val extends SpaceHomePopData>
    implements $SpaceHomePopDataCopyWith<$Res> {
  _$SpaceHomePopDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popups = freezed,
  }) {
    return _then(_value.copyWith(
      popups: freezed == popups
          ? _value.popups
          : popups // ignore: cast_nullable_to_non_nullable
              as List<Popup>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SpaceHomePopDataCopyWith<$Res>
    implements $SpaceHomePopDataCopyWith<$Res> {
  factory _$$_SpaceHomePopDataCopyWith(
          _$_SpaceHomePopData value, $Res Function(_$_SpaceHomePopData) then) =
      __$$_SpaceHomePopDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<Popup>? popups});
}

/// @nodoc
class __$$_SpaceHomePopDataCopyWithImpl<$Res>
    extends _$SpaceHomePopDataCopyWithImpl<$Res, _$_SpaceHomePopData>
    implements _$$_SpaceHomePopDataCopyWith<$Res> {
  __$$_SpaceHomePopDataCopyWithImpl(
      _$_SpaceHomePopData _value, $Res Function(_$_SpaceHomePopData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popups = freezed,
  }) {
    return _then(_$_SpaceHomePopData(
      popups: freezed == popups
          ? _value._popups
          : popups // ignore: cast_nullable_to_non_nullable
              as List<Popup>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpaceHomePopData implements _SpaceHomePopData {
  const _$_SpaceHomePopData({final List<Popup>? popups}) : _popups = popups;

  factory _$_SpaceHomePopData.fromJson(Map<String, dynamic> json) =>
      _$$_SpaceHomePopDataFromJson(json);

  final List<Popup>? _popups;
  @override
  List<Popup>? get popups {
    final value = _popups;
    if (value == null) return null;
    if (_popups is EqualUnmodifiableListView) return _popups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SpaceHomePopData(popups: $popups)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SpaceHomePopData &&
            const DeepCollectionEquality().equals(other._popups, _popups));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_popups));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpaceHomePopDataCopyWith<_$_SpaceHomePopData> get copyWith =>
      __$$_SpaceHomePopDataCopyWithImpl<_$_SpaceHomePopData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpaceHomePopDataToJson(
      this,
    );
  }
}

abstract class _SpaceHomePopData implements SpaceHomePopData {
  const factory _SpaceHomePopData({final List<Popup>? popups}) =
      _$_SpaceHomePopData;

  factory _SpaceHomePopData.fromJson(Map<String, dynamic> json) =
      _$_SpaceHomePopData.fromJson;

  @override
  List<Popup>? get popups;
  @override
  @JsonKey(ignore: true)
  _$$_SpaceHomePopDataCopyWith<_$_SpaceHomePopData> get copyWith =>
      throw _privateConstructorUsedError;
}

Popup _$PopupFromJson(Map<String, dynamic> json) {
  return _Popup.fromJson(json);
}

/// @nodoc
mixin _$Popup {
  Extend? get extend => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get order => throw _privateConstructorUsedError;
  Res? get res => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PopupCopyWith<Popup> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PopupCopyWith<$Res> {
  factory $PopupCopyWith(Popup value, $Res Function(Popup) then) =
      _$PopupCopyWithImpl<$Res, Popup>;
  @useResult
  $Res call(
      {Extend? extend,
      String? name,
      int? order,
      Res? res,
      String? route,
      String? type});

  $ExtendCopyWith<$Res>? get extend;
  $ResCopyWith<$Res>? get res;
}

/// @nodoc
class _$PopupCopyWithImpl<$Res, $Val extends Popup>
    implements $PopupCopyWith<$Res> {
  _$PopupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? extend = freezed,
    Object? name = freezed,
    Object? order = freezed,
    Object? res = freezed,
    Object? route = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as Extend?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      res: freezed == res
          ? _value.res
          : res // ignore: cast_nullable_to_non_nullable
              as Res?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ExtendCopyWith<$Res>? get extend {
    if (_value.extend == null) {
      return null;
    }

    return $ExtendCopyWith<$Res>(_value.extend!, (value) {
      return _then(_value.copyWith(extend: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ResCopyWith<$Res>? get res {
    if (_value.res == null) {
      return null;
    }

    return $ResCopyWith<$Res>(_value.res!, (value) {
      return _then(_value.copyWith(res: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_PopupCopyWith<$Res> implements $PopupCopyWith<$Res> {
  factory _$$_PopupCopyWith(_$_Popup value, $Res Function(_$_Popup) then) =
      __$$_PopupCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Extend? extend,
      String? name,
      int? order,
      Res? res,
      String? route,
      String? type});

  @override
  $ExtendCopyWith<$Res>? get extend;
  @override
  $ResCopyWith<$Res>? get res;
}

/// @nodoc
class __$$_PopupCopyWithImpl<$Res> extends _$PopupCopyWithImpl<$Res, _$_Popup>
    implements _$$_PopupCopyWith<$Res> {
  __$$_PopupCopyWithImpl(_$_Popup _value, $Res Function(_$_Popup) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? extend = freezed,
    Object? name = freezed,
    Object? order = freezed,
    Object? res = freezed,
    Object? route = freezed,
    Object? type = freezed,
  }) {
    return _then(_$_Popup(
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as Extend?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      res: freezed == res
          ? _value.res
          : res // ignore: cast_nullable_to_non_nullable
              as Res?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Popup implements _Popup {
  const _$_Popup(
      {this.extend, this.name, this.order, this.res, this.route, this.type});

  factory _$_Popup.fromJson(Map<String, dynamic> json) =>
      _$$_PopupFromJson(json);

  @override
  final Extend? extend;
  @override
  final String? name;
  @override
  final int? order;
  @override
  final Res? res;
  @override
  final String? route;
  @override
  final String? type;

  @override
  String toString() {
    return 'Popup(extend: $extend, name: $name, order: $order, res: $res, route: $route, type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Popup &&
            (identical(other.extend, extend) || other.extend == extend) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.res, res) || other.res == res) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, extend, name, order, res, route, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PopupCopyWith<_$_Popup> get copyWith =>
      __$$_PopupCopyWithImpl<_$_Popup>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PopupToJson(
      this,
    );
  }
}

abstract class _Popup implements Popup {
  const factory _Popup(
      {final Extend? extend,
      final String? name,
      final int? order,
      final Res? res,
      final String? route,
      final String? type}) = _$_Popup;

  factory _Popup.fromJson(Map<String, dynamic> json) = _$_Popup.fromJson;

  @override
  Extend? get extend;
  @override
  String? get name;
  @override
  int? get order;
  @override
  Res? get res;
  @override
  String? get route;
  @override
  String? get type;
  @override
  @JsonKey(ignore: true)
  _$$_PopupCopyWith<_$_Popup> get copyWith =>
      throw _privateConstructorUsedError;
}

Extend _$ExtendFromJson(Map<String, dynamic> json) {
  return _Extend.fromJson(json);
}

/// @nodoc
mixin _$Extend {
  BurialResources? get burialResources => throw _privateConstructorUsedError;
  String? get changeImg => throw _privateConstructorUsedError;
  String? get guideUrl => throw _privateConstructorUsedError;
  String? get titleImg => throw _privateConstructorUsedError;
  MultiUserLearn? get multiUserLearn => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtendCopyWith<Extend> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtendCopyWith<$Res> {
  factory $ExtendCopyWith(Extend value, $Res Function(Extend) then) =
      _$ExtendCopyWithImpl<$Res, Extend>;
  @useResult
  $Res call(
      {BurialResources? burialResources,
      String? changeImg,
      String? guideUrl,
      String? titleImg,
      MultiUserLearn? multiUserLearn});

  $BurialResourcesCopyWith<$Res>? get burialResources;
  $MultiUserLearnCopyWith<$Res>? get multiUserLearn;
}

/// @nodoc
class _$ExtendCopyWithImpl<$Res, $Val extends Extend>
    implements $ExtendCopyWith<$Res> {
  _$ExtendCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? burialResources = freezed,
    Object? changeImg = freezed,
    Object? guideUrl = freezed,
    Object? titleImg = freezed,
    Object? multiUserLearn = freezed,
  }) {
    return _then(_value.copyWith(
      burialResources: freezed == burialResources
          ? _value.burialResources
          : burialResources // ignore: cast_nullable_to_non_nullable
              as BurialResources?,
      changeImg: freezed == changeImg
          ? _value.changeImg
          : changeImg // ignore: cast_nullable_to_non_nullable
              as String?,
      guideUrl: freezed == guideUrl
          ? _value.guideUrl
          : guideUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      titleImg: freezed == titleImg
          ? _value.titleImg
          : titleImg // ignore: cast_nullable_to_non_nullable
              as String?,
      multiUserLearn: freezed == multiUserLearn
          ? _value.multiUserLearn
          : multiUserLearn // ignore: cast_nullable_to_non_nullable
              as MultiUserLearn?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $BurialResourcesCopyWith<$Res>? get burialResources {
    if (_value.burialResources == null) {
      return null;
    }

    return $BurialResourcesCopyWith<$Res>(_value.burialResources!, (value) {
      return _then(_value.copyWith(burialResources: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MultiUserLearnCopyWith<$Res>? get multiUserLearn {
    if (_value.multiUserLearn == null) {
      return null;
    }

    return $MultiUserLearnCopyWith<$Res>(_value.multiUserLearn!, (value) {
      return _then(_value.copyWith(multiUserLearn: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ExtendCopyWith<$Res> implements $ExtendCopyWith<$Res> {
  factory _$$_ExtendCopyWith(_$_Extend value, $Res Function(_$_Extend) then) =
      __$$_ExtendCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {BurialResources? burialResources,
      String? changeImg,
      String? guideUrl,
      String? titleImg,
      MultiUserLearn? multiUserLearn});

  @override
  $BurialResourcesCopyWith<$Res>? get burialResources;
  @override
  $MultiUserLearnCopyWith<$Res>? get multiUserLearn;
}

/// @nodoc
class __$$_ExtendCopyWithImpl<$Res>
    extends _$ExtendCopyWithImpl<$Res, _$_Extend>
    implements _$$_ExtendCopyWith<$Res> {
  __$$_ExtendCopyWithImpl(_$_Extend _value, $Res Function(_$_Extend) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? burialResources = freezed,
    Object? changeImg = freezed,
    Object? guideUrl = freezed,
    Object? titleImg = freezed,
    Object? multiUserLearn = freezed,
  }) {
    return _then(_$_Extend(
      burialResources: freezed == burialResources
          ? _value.burialResources
          : burialResources // ignore: cast_nullable_to_non_nullable
              as BurialResources?,
      changeImg: freezed == changeImg
          ? _value.changeImg
          : changeImg // ignore: cast_nullable_to_non_nullable
              as String?,
      guideUrl: freezed == guideUrl
          ? _value.guideUrl
          : guideUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      titleImg: freezed == titleImg
          ? _value.titleImg
          : titleImg // ignore: cast_nullable_to_non_nullable
              as String?,
      multiUserLearn: freezed == multiUserLearn
          ? _value.multiUserLearn
          : multiUserLearn // ignore: cast_nullable_to_non_nullable
              as MultiUserLearn?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Extend implements _Extend {
  const _$_Extend(
      {this.burialResources,
      this.changeImg,
      this.guideUrl,
      this.titleImg,
      this.multiUserLearn});

  factory _$_Extend.fromJson(Map<String, dynamic> json) =>
      _$$_ExtendFromJson(json);

  @override
  final BurialResources? burialResources;
  @override
  final String? changeImg;
  @override
  final String? guideUrl;
  @override
  final String? titleImg;
  @override
  final MultiUserLearn? multiUserLearn;

  @override
  String toString() {
    return 'Extend(burialResources: $burialResources, changeImg: $changeImg, guideUrl: $guideUrl, titleImg: $titleImg, multiUserLearn: $multiUserLearn)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Extend &&
            (identical(other.burialResources, burialResources) ||
                other.burialResources == burialResources) &&
            (identical(other.changeImg, changeImg) ||
                other.changeImg == changeImg) &&
            (identical(other.guideUrl, guideUrl) ||
                other.guideUrl == guideUrl) &&
            (identical(other.titleImg, titleImg) ||
                other.titleImg == titleImg) &&
            (identical(other.multiUserLearn, multiUserLearn) ||
                other.multiUserLearn == multiUserLearn));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, burialResources, changeImg,
      guideUrl, titleImg, multiUserLearn);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExtendCopyWith<_$_Extend> get copyWith =>
      __$$_ExtendCopyWithImpl<_$_Extend>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExtendToJson(
      this,
    );
  }
}

abstract class _Extend implements Extend {
  const factory _Extend(
      {final BurialResources? burialResources,
      final String? changeImg,
      final String? guideUrl,
      final String? titleImg,
      final MultiUserLearn? multiUserLearn}) = _$_Extend;

  factory _Extend.fromJson(Map<String, dynamic> json) = _$_Extend.fromJson;

  @override
  BurialResources? get burialResources;
  @override
  String? get changeImg;
  @override
  String? get guideUrl;
  @override
  String? get titleImg;
  @override
  MultiUserLearn? get multiUserLearn;
  @override
  @JsonKey(ignore: true)
  _$$_ExtendCopyWith<_$_Extend> get copyWith =>
      throw _privateConstructorUsedError;
}

BurialResources _$BurialResourcesFromJson(Map<String, dynamic> json) {
  return _BurialResources.fromJson(json);
}

/// @nodoc
mixin _$BurialResources {
  String? get classId => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;
  String? get lessonKey => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BurialResourcesCopyWith<BurialResources> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BurialResourcesCopyWith<$Res> {
  factory $BurialResourcesCopyWith(
          BurialResources value, $Res Function(BurialResources) then) =
      _$BurialResourcesCopyWithImpl<$Res, BurialResources>;
  @useResult
  $Res call(
      {String? classId,
      String? classKey,
      String? courseKey,
      String? courseName,
      String? lessonKey,
      String? lessonName});
}

/// @nodoc
class _$BurialResourcesCopyWithImpl<$Res, $Val extends BurialResources>
    implements $BurialResourcesCopyWith<$Res> {
  _$BurialResourcesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? lessonKey = freezed,
    Object? lessonName = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as String?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BurialResourcesCopyWith<$Res>
    implements $BurialResourcesCopyWith<$Res> {
  factory _$$_BurialResourcesCopyWith(
          _$_BurialResources value, $Res Function(_$_BurialResources) then) =
      __$$_BurialResourcesCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? classId,
      String? classKey,
      String? courseKey,
      String? courseName,
      String? lessonKey,
      String? lessonName});
}

/// @nodoc
class __$$_BurialResourcesCopyWithImpl<$Res>
    extends _$BurialResourcesCopyWithImpl<$Res, _$_BurialResources>
    implements _$$_BurialResourcesCopyWith<$Res> {
  __$$_BurialResourcesCopyWithImpl(
      _$_BurialResources _value, $Res Function(_$_BurialResources) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? lessonKey = freezed,
    Object? lessonName = freezed,
  }) {
    return _then(_$_BurialResources(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as String?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BurialResources implements _BurialResources {
  const _$_BurialResources(
      {this.classId,
      this.classKey,
      this.courseKey,
      this.courseName,
      this.lessonKey,
      this.lessonName});

  factory _$_BurialResources.fromJson(Map<String, dynamic> json) =>
      _$$_BurialResourcesFromJson(json);

  @override
  final String? classId;
  @override
  final String? classKey;
  @override
  final String? courseKey;
  @override
  final String? courseName;
  @override
  final String? lessonKey;
  @override
  final String? lessonName;

  @override
  String toString() {
    return 'BurialResources(classId: $classId, classKey: $classKey, courseKey: $courseKey, courseName: $courseName, lessonKey: $lessonKey, lessonName: $lessonName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BurialResources &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.lessonKey, lessonKey) ||
                other.lessonKey == lessonKey) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, classId, classKey, courseKey,
      courseName, lessonKey, lessonName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BurialResourcesCopyWith<_$_BurialResources> get copyWith =>
      __$$_BurialResourcesCopyWithImpl<_$_BurialResources>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BurialResourcesToJson(
      this,
    );
  }
}

abstract class _BurialResources implements BurialResources {
  const factory _BurialResources(
      {final String? classId,
      final String? classKey,
      final String? courseKey,
      final String? courseName,
      final String? lessonKey,
      final String? lessonName}) = _$_BurialResources;

  factory _BurialResources.fromJson(Map<String, dynamic> json) =
      _$_BurialResources.fromJson;

  @override
  String? get classId;
  @override
  String? get classKey;
  @override
  String? get courseKey;
  @override
  String? get courseName;
  @override
  String? get lessonKey;
  @override
  String? get lessonName;
  @override
  @JsonKey(ignore: true)
  _$$_BurialResourcesCopyWith<_$_BurialResources> get copyWith =>
      throw _privateConstructorUsedError;
}

MultiUserLearn _$MultiUserLearnFromJson(Map<String, dynamic> json) {
  return _MultiUserLearn.fromJson(json);
}

/// @nodoc
mixin _$MultiUserLearn {
  AchInfo? get achInfo => throw _privateConstructorUsedError;
  AwardInfo? get awardInfo => throw _privateConstructorUsedError;
  List<FinishTaskList>? get finishTaskList =>
      throw _privateConstructorUsedError;
  List<MemberList>? get memberList => throw _privateConstructorUsedError;
  PreInfo? get preInfo => throw _privateConstructorUsedError;
  int? get totalScore => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MultiUserLearnCopyWith<MultiUserLearn> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MultiUserLearnCopyWith<$Res> {
  factory $MultiUserLearnCopyWith(
          MultiUserLearn value, $Res Function(MultiUserLearn) then) =
      _$MultiUserLearnCopyWithImpl<$Res, MultiUserLearn>;
  @useResult
  $Res call(
      {AchInfo? achInfo,
      AwardInfo? awardInfo,
      List<FinishTaskList>? finishTaskList,
      List<MemberList>? memberList,
      PreInfo? preInfo,
      int? totalScore});

  $AchInfoCopyWith<$Res>? get achInfo;
  $AwardInfoCopyWith<$Res>? get awardInfo;
  $PreInfoCopyWith<$Res>? get preInfo;
}

/// @nodoc
class _$MultiUserLearnCopyWithImpl<$Res, $Val extends MultiUserLearn>
    implements $MultiUserLearnCopyWith<$Res> {
  _$MultiUserLearnCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? achInfo = freezed,
    Object? awardInfo = freezed,
    Object? finishTaskList = freezed,
    Object? memberList = freezed,
    Object? preInfo = freezed,
    Object? totalScore = freezed,
  }) {
    return _then(_value.copyWith(
      achInfo: freezed == achInfo
          ? _value.achInfo
          : achInfo // ignore: cast_nullable_to_non_nullable
              as AchInfo?,
      awardInfo: freezed == awardInfo
          ? _value.awardInfo
          : awardInfo // ignore: cast_nullable_to_non_nullable
              as AwardInfo?,
      finishTaskList: freezed == finishTaskList
          ? _value.finishTaskList
          : finishTaskList // ignore: cast_nullable_to_non_nullable
              as List<FinishTaskList>?,
      memberList: freezed == memberList
          ? _value.memberList
          : memberList // ignore: cast_nullable_to_non_nullable
              as List<MemberList>?,
      preInfo: freezed == preInfo
          ? _value.preInfo
          : preInfo // ignore: cast_nullable_to_non_nullable
              as PreInfo?,
      totalScore: freezed == totalScore
          ? _value.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AchInfoCopyWith<$Res>? get achInfo {
    if (_value.achInfo == null) {
      return null;
    }

    return $AchInfoCopyWith<$Res>(_value.achInfo!, (value) {
      return _then(_value.copyWith(achInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AwardInfoCopyWith<$Res>? get awardInfo {
    if (_value.awardInfo == null) {
      return null;
    }

    return $AwardInfoCopyWith<$Res>(_value.awardInfo!, (value) {
      return _then(_value.copyWith(awardInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PreInfoCopyWith<$Res>? get preInfo {
    if (_value.preInfo == null) {
      return null;
    }

    return $PreInfoCopyWith<$Res>(_value.preInfo!, (value) {
      return _then(_value.copyWith(preInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MultiUserLearnCopyWith<$Res>
    implements $MultiUserLearnCopyWith<$Res> {
  factory _$$_MultiUserLearnCopyWith(
          _$_MultiUserLearn value, $Res Function(_$_MultiUserLearn) then) =
      __$$_MultiUserLearnCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AchInfo? achInfo,
      AwardInfo? awardInfo,
      List<FinishTaskList>? finishTaskList,
      List<MemberList>? memberList,
      PreInfo? preInfo,
      int? totalScore});

  @override
  $AchInfoCopyWith<$Res>? get achInfo;
  @override
  $AwardInfoCopyWith<$Res>? get awardInfo;
  @override
  $PreInfoCopyWith<$Res>? get preInfo;
}

/// @nodoc
class __$$_MultiUserLearnCopyWithImpl<$Res>
    extends _$MultiUserLearnCopyWithImpl<$Res, _$_MultiUserLearn>
    implements _$$_MultiUserLearnCopyWith<$Res> {
  __$$_MultiUserLearnCopyWithImpl(
      _$_MultiUserLearn _value, $Res Function(_$_MultiUserLearn) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? achInfo = freezed,
    Object? awardInfo = freezed,
    Object? finishTaskList = freezed,
    Object? memberList = freezed,
    Object? preInfo = freezed,
    Object? totalScore = freezed,
  }) {
    return _then(_$_MultiUserLearn(
      achInfo: freezed == achInfo
          ? _value.achInfo
          : achInfo // ignore: cast_nullable_to_non_nullable
              as AchInfo?,
      awardInfo: freezed == awardInfo
          ? _value.awardInfo
          : awardInfo // ignore: cast_nullable_to_non_nullable
              as AwardInfo?,
      finishTaskList: freezed == finishTaskList
          ? _value._finishTaskList
          : finishTaskList // ignore: cast_nullable_to_non_nullable
              as List<FinishTaskList>?,
      memberList: freezed == memberList
          ? _value._memberList
          : memberList // ignore: cast_nullable_to_non_nullable
              as List<MemberList>?,
      preInfo: freezed == preInfo
          ? _value.preInfo
          : preInfo // ignore: cast_nullable_to_non_nullable
              as PreInfo?,
      totalScore: freezed == totalScore
          ? _value.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MultiUserLearn implements _MultiUserLearn {
  const _$_MultiUserLearn(
      {this.achInfo,
      this.awardInfo,
      final List<FinishTaskList>? finishTaskList,
      final List<MemberList>? memberList,
      this.preInfo,
      this.totalScore})
      : _finishTaskList = finishTaskList,
        _memberList = memberList;

  factory _$_MultiUserLearn.fromJson(Map<String, dynamic> json) =>
      _$$_MultiUserLearnFromJson(json);

  @override
  final AchInfo? achInfo;
  @override
  final AwardInfo? awardInfo;
  final List<FinishTaskList>? _finishTaskList;
  @override
  List<FinishTaskList>? get finishTaskList {
    final value = _finishTaskList;
    if (value == null) return null;
    if (_finishTaskList is EqualUnmodifiableListView) return _finishTaskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<MemberList>? _memberList;
  @override
  List<MemberList>? get memberList {
    final value = _memberList;
    if (value == null) return null;
    if (_memberList is EqualUnmodifiableListView) return _memberList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final PreInfo? preInfo;
  @override
  final int? totalScore;

  @override
  String toString() {
    return 'MultiUserLearn(achInfo: $achInfo, awardInfo: $awardInfo, finishTaskList: $finishTaskList, memberList: $memberList, preInfo: $preInfo, totalScore: $totalScore)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MultiUserLearn &&
            (identical(other.achInfo, achInfo) || other.achInfo == achInfo) &&
            (identical(other.awardInfo, awardInfo) ||
                other.awardInfo == awardInfo) &&
            const DeepCollectionEquality()
                .equals(other._finishTaskList, _finishTaskList) &&
            const DeepCollectionEquality()
                .equals(other._memberList, _memberList) &&
            (identical(other.preInfo, preInfo) || other.preInfo == preInfo) &&
            (identical(other.totalScore, totalScore) ||
                other.totalScore == totalScore));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      achInfo,
      awardInfo,
      const DeepCollectionEquality().hash(_finishTaskList),
      const DeepCollectionEquality().hash(_memberList),
      preInfo,
      totalScore);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MultiUserLearnCopyWith<_$_MultiUserLearn> get copyWith =>
      __$$_MultiUserLearnCopyWithImpl<_$_MultiUserLearn>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MultiUserLearnToJson(
      this,
    );
  }
}

abstract class _MultiUserLearn implements MultiUserLearn {
  const factory _MultiUserLearn(
      {final AchInfo? achInfo,
      final AwardInfo? awardInfo,
      final List<FinishTaskList>? finishTaskList,
      final List<MemberList>? memberList,
      final PreInfo? preInfo,
      final int? totalScore}) = _$_MultiUserLearn;

  factory _MultiUserLearn.fromJson(Map<String, dynamic> json) =
      _$_MultiUserLearn.fromJson;

  @override
  AchInfo? get achInfo;
  @override
  AwardInfo? get awardInfo;
  @override
  List<FinishTaskList>? get finishTaskList;
  @override
  List<MemberList>? get memberList;
  @override
  PreInfo? get preInfo;
  @override
  int? get totalScore;
  @override
  @JsonKey(ignore: true)
  _$$_MultiUserLearnCopyWith<_$_MultiUserLearn> get copyWith =>
      throw _privateConstructorUsedError;
}

AchInfo _$AchInfoFromJson(Map<String, dynamic> json) {
  return _AchInfo.fromJson(json);
}

/// @nodoc
mixin _$AchInfo {
  int? get level => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get resource => throw _privateConstructorUsedError;
  String? get tip => throw _privateConstructorUsedError;
  ResourceSpineData? get resourceSpineData =>
      throw _privateConstructorUsedError;
  List<AchList>? get achList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AchInfoCopyWith<AchInfo> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AchInfoCopyWith<$Res> {
  factory $AchInfoCopyWith(AchInfo value, $Res Function(AchInfo) then) =
      _$AchInfoCopyWithImpl<$Res, AchInfo>;
  @useResult
  $Res call(
      {int? level,
      String? name,
      String? resource,
      String? tip,
      ResourceSpineData? resourceSpineData,
      List<AchList>? achList});

  $ResourceSpineDataCopyWith<$Res>? get resourceSpineData;
}

/// @nodoc
class _$AchInfoCopyWithImpl<$Res, $Val extends AchInfo>
    implements $AchInfoCopyWith<$Res> {
  _$AchInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? level = freezed,
    Object? name = freezed,
    Object? resource = freezed,
    Object? tip = freezed,
    Object? resourceSpineData = freezed,
    Object? achList = freezed,
  }) {
    return _then(_value.copyWith(
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceSpineData: freezed == resourceSpineData
          ? _value.resourceSpineData
          : resourceSpineData // ignore: cast_nullable_to_non_nullable
              as ResourceSpineData?,
      achList: freezed == achList
          ? _value.achList
          : achList // ignore: cast_nullable_to_non_nullable
              as List<AchList>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ResourceSpineDataCopyWith<$Res>? get resourceSpineData {
    if (_value.resourceSpineData == null) {
      return null;
    }

    return $ResourceSpineDataCopyWith<$Res>(_value.resourceSpineData!, (value) {
      return _then(_value.copyWith(resourceSpineData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_AchInfoCopyWith<$Res> implements $AchInfoCopyWith<$Res> {
  factory _$$_AchInfoCopyWith(
          _$_AchInfo value, $Res Function(_$_AchInfo) then) =
      __$$_AchInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? level,
      String? name,
      String? resource,
      String? tip,
      ResourceSpineData? resourceSpineData,
      List<AchList>? achList});

  @override
  $ResourceSpineDataCopyWith<$Res>? get resourceSpineData;
}

/// @nodoc
class __$$_AchInfoCopyWithImpl<$Res>
    extends _$AchInfoCopyWithImpl<$Res, _$_AchInfo>
    implements _$$_AchInfoCopyWith<$Res> {
  __$$_AchInfoCopyWithImpl(_$_AchInfo _value, $Res Function(_$_AchInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? level = freezed,
    Object? name = freezed,
    Object? resource = freezed,
    Object? tip = freezed,
    Object? resourceSpineData = freezed,
    Object? achList = freezed,
  }) {
    return _then(_$_AchInfo(
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceSpineData: freezed == resourceSpineData
          ? _value.resourceSpineData
          : resourceSpineData // ignore: cast_nullable_to_non_nullable
              as ResourceSpineData?,
      achList: freezed == achList
          ? _value._achList
          : achList // ignore: cast_nullable_to_non_nullable
              as List<AchList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AchInfo implements _AchInfo {
  const _$_AchInfo(
      {this.level,
      this.name,
      this.resource,
      this.tip,
      this.resourceSpineData,
      final List<AchList>? achList})
      : _achList = achList;

  factory _$_AchInfo.fromJson(Map<String, dynamic> json) =>
      _$$_AchInfoFromJson(json);

  @override
  final int? level;
  @override
  final String? name;
  @override
  final String? resource;
  @override
  final String? tip;
  @override
  final ResourceSpineData? resourceSpineData;
  final List<AchList>? _achList;
  @override
  List<AchList>? get achList {
    final value = _achList;
    if (value == null) return null;
    if (_achList is EqualUnmodifiableListView) return _achList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AchInfo(level: $level, name: $name, resource: $resource, tip: $tip, resourceSpineData: $resourceSpineData, achList: $achList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AchInfo &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.resource, resource) ||
                other.resource == resource) &&
            (identical(other.tip, tip) || other.tip == tip) &&
            (identical(other.resourceSpineData, resourceSpineData) ||
                other.resourceSpineData == resourceSpineData) &&
            const DeepCollectionEquality().equals(other._achList, _achList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, level, name, resource, tip,
      resourceSpineData, const DeepCollectionEquality().hash(_achList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AchInfoCopyWith<_$_AchInfo> get copyWith =>
      __$$_AchInfoCopyWithImpl<_$_AchInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AchInfoToJson(
      this,
    );
  }
}

abstract class _AchInfo implements AchInfo {
  const factory _AchInfo(
      {final int? level,
      final String? name,
      final String? resource,
      final String? tip,
      final ResourceSpineData? resourceSpineData,
      final List<AchList>? achList}) = _$_AchInfo;

  factory _AchInfo.fromJson(Map<String, dynamic> json) = _$_AchInfo.fromJson;

  @override
  int? get level;
  @override
  String? get name;
  @override
  String? get resource;
  @override
  String? get tip;
  @override
  ResourceSpineData? get resourceSpineData;
  @override
  List<AchList>? get achList;
  @override
  @JsonKey(ignore: true)
  _$$_AchInfoCopyWith<_$_AchInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ResourceSpineData _$ResourceSpineDataFromJson(Map<String, dynamic> json) {
  return _ResourceSpineData.fromJson(json);
}

/// @nodoc
mixin _$ResourceSpineData {
//  skel本地文件
  String? get skelFile => throw _privateConstructorUsedError; //  atlas本地文件
  String? get atlasFile => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ResourceSpineDataCopyWith<ResourceSpineData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResourceSpineDataCopyWith<$Res> {
  factory $ResourceSpineDataCopyWith(
          ResourceSpineData value, $Res Function(ResourceSpineData) then) =
      _$ResourceSpineDataCopyWithImpl<$Res, ResourceSpineData>;
  @useResult
  $Res call({String? skelFile, String? atlasFile});
}

/// @nodoc
class _$ResourceSpineDataCopyWithImpl<$Res, $Val extends ResourceSpineData>
    implements $ResourceSpineDataCopyWith<$Res> {
  _$ResourceSpineDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skelFile = freezed,
    Object? atlasFile = freezed,
  }) {
    return _then(_value.copyWith(
      skelFile: freezed == skelFile
          ? _value.skelFile
          : skelFile // ignore: cast_nullable_to_non_nullable
              as String?,
      atlasFile: freezed == atlasFile
          ? _value.atlasFile
          : atlasFile // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ResourceSpineDataCopyWith<$Res>
    implements $ResourceSpineDataCopyWith<$Res> {
  factory _$$_ResourceSpineDataCopyWith(_$_ResourceSpineData value,
          $Res Function(_$_ResourceSpineData) then) =
      __$$_ResourceSpineDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? skelFile, String? atlasFile});
}

/// @nodoc
class __$$_ResourceSpineDataCopyWithImpl<$Res>
    extends _$ResourceSpineDataCopyWithImpl<$Res, _$_ResourceSpineData>
    implements _$$_ResourceSpineDataCopyWith<$Res> {
  __$$_ResourceSpineDataCopyWithImpl(
      _$_ResourceSpineData _value, $Res Function(_$_ResourceSpineData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skelFile = freezed,
    Object? atlasFile = freezed,
  }) {
    return _then(_$_ResourceSpineData(
      skelFile: freezed == skelFile
          ? _value.skelFile
          : skelFile // ignore: cast_nullable_to_non_nullable
              as String?,
      atlasFile: freezed == atlasFile
          ? _value.atlasFile
          : atlasFile // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ResourceSpineData implements _ResourceSpineData {
  const _$_ResourceSpineData({this.skelFile, this.atlasFile});

  factory _$_ResourceSpineData.fromJson(Map<String, dynamic> json) =>
      _$$_ResourceSpineDataFromJson(json);

//  skel本地文件
  @override
  final String? skelFile;
//  atlas本地文件
  @override
  final String? atlasFile;

  @override
  String toString() {
    return 'ResourceSpineData(skelFile: $skelFile, atlasFile: $atlasFile)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ResourceSpineData &&
            (identical(other.skelFile, skelFile) ||
                other.skelFile == skelFile) &&
            (identical(other.atlasFile, atlasFile) ||
                other.atlasFile == atlasFile));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, skelFile, atlasFile);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ResourceSpineDataCopyWith<_$_ResourceSpineData> get copyWith =>
      __$$_ResourceSpineDataCopyWithImpl<_$_ResourceSpineData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ResourceSpineDataToJson(
      this,
    );
  }
}

abstract class _ResourceSpineData implements ResourceSpineData {
  const factory _ResourceSpineData(
      {final String? skelFile, final String? atlasFile}) = _$_ResourceSpineData;

  factory _ResourceSpineData.fromJson(Map<String, dynamic> json) =
      _$_ResourceSpineData.fromJson;

  @override //  skel本地文件
  String? get skelFile;
  @override //  atlas本地文件
  String? get atlasFile;
  @override
  @JsonKey(ignore: true)
  _$$_ResourceSpineDataCopyWith<_$_ResourceSpineData> get copyWith =>
      throw _privateConstructorUsedError;
}

AwardInfo _$AwardInfoFromJson(Map<String, dynamic> json) {
  return _AwardInfo.fromJson(json);
}

/// @nodoc
mixin _$AwardInfo {
  List<AwardItem>? get awardItem => throw _privateConstructorUsedError;
  String? get boxResource => throw _privateConstructorUsedError;
  ResourceSpineData? get boxSpineResource => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AwardInfoCopyWith<AwardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AwardInfoCopyWith<$Res> {
  factory $AwardInfoCopyWith(AwardInfo value, $Res Function(AwardInfo) then) =
      _$AwardInfoCopyWithImpl<$Res, AwardInfo>;
  @useResult
  $Res call(
      {List<AwardItem>? awardItem,
      String? boxResource,
      ResourceSpineData? boxSpineResource});

  $ResourceSpineDataCopyWith<$Res>? get boxSpineResource;
}

/// @nodoc
class _$AwardInfoCopyWithImpl<$Res, $Val extends AwardInfo>
    implements $AwardInfoCopyWith<$Res> {
  _$AwardInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? awardItem = freezed,
    Object? boxResource = freezed,
    Object? boxSpineResource = freezed,
  }) {
    return _then(_value.copyWith(
      awardItem: freezed == awardItem
          ? _value.awardItem
          : awardItem // ignore: cast_nullable_to_non_nullable
              as List<AwardItem>?,
      boxResource: freezed == boxResource
          ? _value.boxResource
          : boxResource // ignore: cast_nullable_to_non_nullable
              as String?,
      boxSpineResource: freezed == boxSpineResource
          ? _value.boxSpineResource
          : boxSpineResource // ignore: cast_nullable_to_non_nullable
              as ResourceSpineData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ResourceSpineDataCopyWith<$Res>? get boxSpineResource {
    if (_value.boxSpineResource == null) {
      return null;
    }

    return $ResourceSpineDataCopyWith<$Res>(_value.boxSpineResource!, (value) {
      return _then(_value.copyWith(boxSpineResource: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_AwardInfoCopyWith<$Res> implements $AwardInfoCopyWith<$Res> {
  factory _$$_AwardInfoCopyWith(
          _$_AwardInfo value, $Res Function(_$_AwardInfo) then) =
      __$$_AwardInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<AwardItem>? awardItem,
      String? boxResource,
      ResourceSpineData? boxSpineResource});

  @override
  $ResourceSpineDataCopyWith<$Res>? get boxSpineResource;
}

/// @nodoc
class __$$_AwardInfoCopyWithImpl<$Res>
    extends _$AwardInfoCopyWithImpl<$Res, _$_AwardInfo>
    implements _$$_AwardInfoCopyWith<$Res> {
  __$$_AwardInfoCopyWithImpl(
      _$_AwardInfo _value, $Res Function(_$_AwardInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? awardItem = freezed,
    Object? boxResource = freezed,
    Object? boxSpineResource = freezed,
  }) {
    return _then(_$_AwardInfo(
      awardItem: freezed == awardItem
          ? _value._awardItem
          : awardItem // ignore: cast_nullable_to_non_nullable
              as List<AwardItem>?,
      boxResource: freezed == boxResource
          ? _value.boxResource
          : boxResource // ignore: cast_nullable_to_non_nullable
              as String?,
      boxSpineResource: freezed == boxSpineResource
          ? _value.boxSpineResource
          : boxSpineResource // ignore: cast_nullable_to_non_nullable
              as ResourceSpineData?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AwardInfo implements _AwardInfo {
  const _$_AwardInfo(
      {final List<AwardItem>? awardItem,
      this.boxResource,
      this.boxSpineResource})
      : _awardItem = awardItem;

  factory _$_AwardInfo.fromJson(Map<String, dynamic> json) =>
      _$$_AwardInfoFromJson(json);

  final List<AwardItem>? _awardItem;
  @override
  List<AwardItem>? get awardItem {
    final value = _awardItem;
    if (value == null) return null;
    if (_awardItem is EqualUnmodifiableListView) return _awardItem;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? boxResource;
  @override
  final ResourceSpineData? boxSpineResource;

  @override
  String toString() {
    return 'AwardInfo(awardItem: $awardItem, boxResource: $boxResource, boxSpineResource: $boxSpineResource)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AwardInfo &&
            const DeepCollectionEquality()
                .equals(other._awardItem, _awardItem) &&
            (identical(other.boxResource, boxResource) ||
                other.boxResource == boxResource) &&
            (identical(other.boxSpineResource, boxSpineResource) ||
                other.boxSpineResource == boxSpineResource));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_awardItem),
      boxResource,
      boxSpineResource);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AwardInfoCopyWith<_$_AwardInfo> get copyWith =>
      __$$_AwardInfoCopyWithImpl<_$_AwardInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AwardInfoToJson(
      this,
    );
  }
}

abstract class _AwardInfo implements AwardInfo {
  const factory _AwardInfo(
      {final List<AwardItem>? awardItem,
      final String? boxResource,
      final ResourceSpineData? boxSpineResource}) = _$_AwardInfo;

  factory _AwardInfo.fromJson(Map<String, dynamic> json) =
      _$_AwardInfo.fromJson;

  @override
  List<AwardItem>? get awardItem;
  @override
  String? get boxResource;
  @override
  ResourceSpineData? get boxSpineResource;
  @override
  @JsonKey(ignore: true)
  _$$_AwardInfoCopyWith<_$_AwardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

AwardItem _$AwardItemFromJson(Map<String, dynamic> json) {
  return _AwardItem.fromJson(json);
}

/// @nodoc
mixin _$AwardItem {
  int? get count => throw _privateConstructorUsedError;
  String? get cover => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AwardItemCopyWith<AwardItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AwardItemCopyWith<$Res> {
  factory $AwardItemCopyWith(AwardItem value, $Res Function(AwardItem) then) =
      _$AwardItemCopyWithImpl<$Res, AwardItem>;
  @useResult
  $Res call({int? count, String? cover, String? name});
}

/// @nodoc
class _$AwardItemCopyWithImpl<$Res, $Val extends AwardItem>
    implements $AwardItemCopyWith<$Res> {
  _$AwardItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? count = freezed,
    Object? cover = freezed,
    Object? name = freezed,
  }) {
    return _then(_value.copyWith(
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AwardItemCopyWith<$Res> implements $AwardItemCopyWith<$Res> {
  factory _$$_AwardItemCopyWith(
          _$_AwardItem value, $Res Function(_$_AwardItem) then) =
      __$$_AwardItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? count, String? cover, String? name});
}

/// @nodoc
class __$$_AwardItemCopyWithImpl<$Res>
    extends _$AwardItemCopyWithImpl<$Res, _$_AwardItem>
    implements _$$_AwardItemCopyWith<$Res> {
  __$$_AwardItemCopyWithImpl(
      _$_AwardItem _value, $Res Function(_$_AwardItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? count = freezed,
    Object? cover = freezed,
    Object? name = freezed,
  }) {
    return _then(_$_AwardItem(
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AwardItem implements _AwardItem {
  const _$_AwardItem({this.count, this.cover, this.name});

  factory _$_AwardItem.fromJson(Map<String, dynamic> json) =>
      _$$_AwardItemFromJson(json);

  @override
  final int? count;
  @override
  final String? cover;
  @override
  final String? name;

  @override
  String toString() {
    return 'AwardItem(count: $count, cover: $cover, name: $name)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AwardItem &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.cover, cover) || other.cover == cover) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, count, cover, name);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AwardItemCopyWith<_$_AwardItem> get copyWith =>
      __$$_AwardItemCopyWithImpl<_$_AwardItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AwardItemToJson(
      this,
    );
  }
}

abstract class _AwardItem implements AwardItem {
  const factory _AwardItem(
      {final int? count,
      final String? cover,
      final String? name}) = _$_AwardItem;

  factory _AwardItem.fromJson(Map<String, dynamic> json) =
      _$_AwardItem.fromJson;

  @override
  int? get count;
  @override
  String? get cover;
  @override
  String? get name;
  @override
  @JsonKey(ignore: true)
  _$$_AwardItemCopyWith<_$_AwardItem> get copyWith =>
      throw _privateConstructorUsedError;
}

FinishTaskList _$FinishTaskListFromJson(Map<String, dynamic> json) {
  return _FinishTaskList.fromJson(json);
}

/// @nodoc
mixin _$FinishTaskList {
  String? get taskName => throw _privateConstructorUsedError;
  int? get taskScore => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FinishTaskListCopyWith<FinishTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FinishTaskListCopyWith<$Res> {
  factory $FinishTaskListCopyWith(
          FinishTaskList value, $Res Function(FinishTaskList) then) =
      _$FinishTaskListCopyWithImpl<$Res, FinishTaskList>;
  @useResult
  $Res call({String? taskName, int? taskScore});
}

/// @nodoc
class _$FinishTaskListCopyWithImpl<$Res, $Val extends FinishTaskList>
    implements $FinishTaskListCopyWith<$Res> {
  _$FinishTaskListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskName = freezed,
    Object? taskScore = freezed,
  }) {
    return _then(_value.copyWith(
      taskName: freezed == taskName
          ? _value.taskName
          : taskName // ignore: cast_nullable_to_non_nullable
              as String?,
      taskScore: freezed == taskScore
          ? _value.taskScore
          : taskScore // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_FinishTaskListCopyWith<$Res>
    implements $FinishTaskListCopyWith<$Res> {
  factory _$$_FinishTaskListCopyWith(
          _$_FinishTaskList value, $Res Function(_$_FinishTaskList) then) =
      __$$_FinishTaskListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? taskName, int? taskScore});
}

/// @nodoc
class __$$_FinishTaskListCopyWithImpl<$Res>
    extends _$FinishTaskListCopyWithImpl<$Res, _$_FinishTaskList>
    implements _$$_FinishTaskListCopyWith<$Res> {
  __$$_FinishTaskListCopyWithImpl(
      _$_FinishTaskList _value, $Res Function(_$_FinishTaskList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskName = freezed,
    Object? taskScore = freezed,
  }) {
    return _then(_$_FinishTaskList(
      taskName: freezed == taskName
          ? _value.taskName
          : taskName // ignore: cast_nullable_to_non_nullable
              as String?,
      taskScore: freezed == taskScore
          ? _value.taskScore
          : taskScore // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_FinishTaskList implements _FinishTaskList {
  const _$_FinishTaskList({this.taskName, this.taskScore});

  factory _$_FinishTaskList.fromJson(Map<String, dynamic> json) =>
      _$$_FinishTaskListFromJson(json);

  @override
  final String? taskName;
  @override
  final int? taskScore;

  @override
  String toString() {
    return 'FinishTaskList(taskName: $taskName, taskScore: $taskScore)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FinishTaskList &&
            (identical(other.taskName, taskName) ||
                other.taskName == taskName) &&
            (identical(other.taskScore, taskScore) ||
                other.taskScore == taskScore));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, taskName, taskScore);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FinishTaskListCopyWith<_$_FinishTaskList> get copyWith =>
      __$$_FinishTaskListCopyWithImpl<_$_FinishTaskList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_FinishTaskListToJson(
      this,
    );
  }
}

abstract class _FinishTaskList implements FinishTaskList {
  const factory _FinishTaskList(
      {final String? taskName, final int? taskScore}) = _$_FinishTaskList;

  factory _FinishTaskList.fromJson(Map<String, dynamic> json) =
      _$_FinishTaskList.fromJson;

  @override
  String? get taskName;
  @override
  int? get taskScore;
  @override
  @JsonKey(ignore: true)
  _$$_FinishTaskListCopyWith<_$_FinishTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

MemberList _$MemberListFromJson(Map<String, dynamic> json) {
  return _MemberList.fromJson(json);
}

/// @nodoc
mixin _$MemberList {
  List<AchList>? get achList => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  String? get nickname => throw _privateConstructorUsedError;
  int? get userType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MemberListCopyWith<MemberList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MemberListCopyWith<$Res> {
  factory $MemberListCopyWith(
          MemberList value, $Res Function(MemberList) then) =
      _$MemberListCopyWithImpl<$Res, MemberList>;
  @useResult
  $Res call(
      {List<AchList>? achList,
      String? avatarUrl,
      String? nickname,
      int? userType});
}

/// @nodoc
class _$MemberListCopyWithImpl<$Res, $Val extends MemberList>
    implements $MemberListCopyWith<$Res> {
  _$MemberListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? achList = freezed,
    Object? avatarUrl = freezed,
    Object? nickname = freezed,
    Object? userType = freezed,
  }) {
    return _then(_value.copyWith(
      achList: freezed == achList
          ? _value.achList
          : achList // ignore: cast_nullable_to_non_nullable
              as List<AchList>?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      userType: freezed == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MemberListCopyWith<$Res>
    implements $MemberListCopyWith<$Res> {
  factory _$$_MemberListCopyWith(
          _$_MemberList value, $Res Function(_$_MemberList) then) =
      __$$_MemberListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<AchList>? achList,
      String? avatarUrl,
      String? nickname,
      int? userType});
}

/// @nodoc
class __$$_MemberListCopyWithImpl<$Res>
    extends _$MemberListCopyWithImpl<$Res, _$_MemberList>
    implements _$$_MemberListCopyWith<$Res> {
  __$$_MemberListCopyWithImpl(
      _$_MemberList _value, $Res Function(_$_MemberList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? achList = freezed,
    Object? avatarUrl = freezed,
    Object? nickname = freezed,
    Object? userType = freezed,
  }) {
    return _then(_$_MemberList(
      achList: freezed == achList
          ? _value._achList
          : achList // ignore: cast_nullable_to_non_nullable
              as List<AchList>?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      userType: freezed == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MemberList implements _MemberList {
  const _$_MemberList(
      {final List<AchList>? achList,
      this.avatarUrl,
      this.nickname,
      this.userType})
      : _achList = achList;

  factory _$_MemberList.fromJson(Map<String, dynamic> json) =>
      _$$_MemberListFromJson(json);

  final List<AchList>? _achList;
  @override
  List<AchList>? get achList {
    final value = _achList;
    if (value == null) return null;
    if (_achList is EqualUnmodifiableListView) return _achList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? avatarUrl;
  @override
  final String? nickname;
  @override
  final int? userType;

  @override
  String toString() {
    return 'MemberList(achList: $achList, avatarUrl: $avatarUrl, nickname: $nickname, userType: $userType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MemberList &&
            const DeepCollectionEquality().equals(other._achList, _achList) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.userType, userType) ||
                other.userType == userType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_achList),
      avatarUrl,
      nickname,
      userType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MemberListCopyWith<_$_MemberList> get copyWith =>
      __$$_MemberListCopyWithImpl<_$_MemberList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MemberListToJson(
      this,
    );
  }
}

abstract class _MemberList implements MemberList {
  const factory _MemberList(
      {final List<AchList>? achList,
      final String? avatarUrl,
      final String? nickname,
      final int? userType}) = _$_MemberList;

  factory _MemberList.fromJson(Map<String, dynamic> json) =
      _$_MemberList.fromJson;

  @override
  List<AchList>? get achList;
  @override
  String? get avatarUrl;
  @override
  String? get nickname;
  @override
  int? get userType;
  @override
  @JsonKey(ignore: true)
  _$$_MemberListCopyWith<_$_MemberList> get copyWith =>
      throw _privateConstructorUsedError;
}

AchList _$AchListFromJson(Map<String, dynamic> json) {
  return _AchList.fromJson(json);
}

/// @nodoc
mixin _$AchList {
  String? get icon => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AchListCopyWith<AchList> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AchListCopyWith<$Res> {
  factory $AchListCopyWith(AchList value, $Res Function(AchList) then) =
      _$AchListCopyWithImpl<$Res, AchList>;
  @useResult
  $Res call({String? icon, int? type});
}

/// @nodoc
class _$AchListCopyWithImpl<$Res, $Val extends AchList>
    implements $AchListCopyWith<$Res> {
  _$AchListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AchListCopyWith<$Res> implements $AchListCopyWith<$Res> {
  factory _$$_AchListCopyWith(
          _$_AchList value, $Res Function(_$_AchList) then) =
      __$$_AchListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? icon, int? type});
}

/// @nodoc
class __$$_AchListCopyWithImpl<$Res>
    extends _$AchListCopyWithImpl<$Res, _$_AchList>
    implements _$$_AchListCopyWith<$Res> {
  __$$_AchListCopyWithImpl(_$_AchList _value, $Res Function(_$_AchList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? type = freezed,
  }) {
    return _then(_$_AchList(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AchList implements _AchList {
  const _$_AchList({this.icon, this.type});

  factory _$_AchList.fromJson(Map<String, dynamic> json) =>
      _$$_AchListFromJson(json);

  @override
  final String? icon;
  @override
  final int? type;

  @override
  String toString() {
    return 'AchList(icon: $icon, type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AchList &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, icon, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AchListCopyWith<_$_AchList> get copyWith =>
      __$$_AchListCopyWithImpl<_$_AchList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AchListToJson(
      this,
    );
  }
}

abstract class _AchList implements AchList {
  const factory _AchList({final String? icon, final int? type}) = _$_AchList;

  factory _AchList.fromJson(Map<String, dynamic> json) = _$_AchList.fromJson;

  @override
  String? get icon;
  @override
  int? get type;
  @override
  @JsonKey(ignore: true)
  _$$_AchListCopyWith<_$_AchList> get copyWith =>
      throw _privateConstructorUsedError;
}

PreInfo _$PreInfoFromJson(Map<String, dynamic> json) {
  return _PreInfo.fromJson(json);
}

/// @nodoc
mixin _$PreInfo {
  String? get content => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PreInfoCopyWith<PreInfo> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PreInfoCopyWith<$Res> {
  factory $PreInfoCopyWith(PreInfo value, $Res Function(PreInfo) then) =
      _$PreInfoCopyWithImpl<$Res, PreInfo>;
  @useResult
  $Res call({String? content, String? title});
}

/// @nodoc
class _$PreInfoCopyWithImpl<$Res, $Val extends PreInfo>
    implements $PreInfoCopyWith<$Res> {
  _$PreInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? title = freezed,
  }) {
    return _then(_value.copyWith(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PreInfoCopyWith<$Res> implements $PreInfoCopyWith<$Res> {
  factory _$$_PreInfoCopyWith(
          _$_PreInfo value, $Res Function(_$_PreInfo) then) =
      __$$_PreInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? content, String? title});
}

/// @nodoc
class __$$_PreInfoCopyWithImpl<$Res>
    extends _$PreInfoCopyWithImpl<$Res, _$_PreInfo>
    implements _$$_PreInfoCopyWith<$Res> {
  __$$_PreInfoCopyWithImpl(_$_PreInfo _value, $Res Function(_$_PreInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? title = freezed,
  }) {
    return _then(_$_PreInfo(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PreInfo implements _PreInfo {
  const _$_PreInfo({this.content, this.title});

  factory _$_PreInfo.fromJson(Map<String, dynamic> json) =>
      _$$_PreInfoFromJson(json);

  @override
  final String? content;
  @override
  final String? title;

  @override
  String toString() {
    return 'PreInfo(content: $content, title: $title)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PreInfo &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, content, title);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PreInfoCopyWith<_$_PreInfo> get copyWith =>
      __$$_PreInfoCopyWithImpl<_$_PreInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PreInfoToJson(
      this,
    );
  }
}

abstract class _PreInfo implements PreInfo {
  const factory _PreInfo({final String? content, final String? title}) =
      _$_PreInfo;

  factory _PreInfo.fromJson(Map<String, dynamic> json) = _$_PreInfo.fromJson;

  @override
  String? get content;
  @override
  String? get title;
  @override
  @JsonKey(ignore: true)
  _$$_PreInfoCopyWith<_$_PreInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

Res _$ResFromJson(Map<String, dynamic> json) {
  return _Res.fromJson(json);
}

/// @nodoc
mixin _$Res {
  String? get audio => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ResCopyWith<Res> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResCopyWith<$Res> {
  factory $ResCopyWith(Res value, $Res Function(Res) then) =
      _$ResCopyWithImpl<$Res, Res>;
  @useResult
  $Res call({String? audio, String? img});
}

/// @nodoc
class _$ResCopyWithImpl<$Res, $Val extends Res> implements $ResCopyWith<$Res> {
  _$ResCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audio = freezed,
    Object? img = freezed,
  }) {
    return _then(_value.copyWith(
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ResCopyWith<$Res> implements $ResCopyWith<$Res> {
  factory _$$_ResCopyWith(_$_Res value, $Res Function(_$_Res) then) =
      __$$_ResCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? audio, String? img});
}

/// @nodoc
class __$$_ResCopyWithImpl<$Res> extends _$ResCopyWithImpl<$Res, _$_Res>
    implements _$$_ResCopyWith<$Res> {
  __$$_ResCopyWithImpl(_$_Res _value, $Res Function(_$_Res) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audio = freezed,
    Object? img = freezed,
  }) {
    return _then(_$_Res(
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Res implements _Res {
  const _$_Res({this.audio, this.img});

  factory _$_Res.fromJson(Map<String, dynamic> json) => _$$_ResFromJson(json);

  @override
  final String? audio;
  @override
  final String? img;

  @override
  String toString() {
    return 'Res(audio: $audio, img: $img)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Res &&
            (identical(other.audio, audio) || other.audio == audio) &&
            (identical(other.img, img) || other.img == img));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, audio, img);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ResCopyWith<_$_Res> get copyWith =>
      __$$_ResCopyWithImpl<_$_Res>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ResToJson(
      this,
    );
  }
}

abstract class _Res implements Res {
  const factory _Res({final String? audio, final String? img}) = _$_Res;

  factory _Res.fromJson(Map<String, dynamic> json) = _$_Res.fromJson;

  @override
  String? get audio;
  @override
  String? get img;
  @override
  @JsonKey(ignore: true)
  _$$_ResCopyWith<_$_Res> get copyWith => throw _privateConstructorUsedError;
}
