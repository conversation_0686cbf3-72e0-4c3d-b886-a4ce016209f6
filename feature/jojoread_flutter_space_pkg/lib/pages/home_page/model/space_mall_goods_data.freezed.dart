// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_mall_goods_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceMallGoodsData _$SpaceMallGoodsDataFromJson(Map<String, dynamic> json) {
  return _SpaceMallGoodsData.fromJson(json);
}

/// @nodoc
mixin _$SpaceMallGoodsData {
  List<GoodsSkin>? get dressUps => throw _privateConstructorUsedError;
  int? get useAssetType => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  int? get pageNum => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceMallGoodsDataCopyWith<SpaceMallGoodsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceMallGoodsDataCopyWith<$Res> {
  factory $SpaceMallGoodsDataCopyWith(
          SpaceMallGoodsData value, $Res Function(SpaceMallGoodsData) then) =
      _$SpaceMallGoodsDataCopyWithImpl<$Res, SpaceMallGoodsData>;
  @useResult
  $Res call(
      {List<GoodsSkin>? dressUps,
      int? useAssetType,
      int? total,
      int? pageSize,
      int? pageNum});
}

/// @nodoc
class _$SpaceMallGoodsDataCopyWithImpl<$Res, $Val extends SpaceMallGoodsData>
    implements $SpaceMallGoodsDataCopyWith<$Res> {
  _$SpaceMallGoodsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dressUps = freezed,
    Object? useAssetType = freezed,
    Object? total = freezed,
    Object? pageSize = freezed,
    Object? pageNum = freezed,
  }) {
    return _then(_value.copyWith(
      dressUps: freezed == dressUps
          ? _value.dressUps
          : dressUps // ignore: cast_nullable_to_non_nullable
              as List<GoodsSkin>?,
      useAssetType: freezed == useAssetType
          ? _value.useAssetType
          : useAssetType // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SpaceMallGoodsDataCopyWith<$Res>
    implements $SpaceMallGoodsDataCopyWith<$Res> {
  factory _$$_SpaceMallGoodsDataCopyWith(_$_SpaceMallGoodsData value,
          $Res Function(_$_SpaceMallGoodsData) then) =
      __$$_SpaceMallGoodsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<GoodsSkin>? dressUps,
      int? useAssetType,
      int? total,
      int? pageSize,
      int? pageNum});
}

/// @nodoc
class __$$_SpaceMallGoodsDataCopyWithImpl<$Res>
    extends _$SpaceMallGoodsDataCopyWithImpl<$Res, _$_SpaceMallGoodsData>
    implements _$$_SpaceMallGoodsDataCopyWith<$Res> {
  __$$_SpaceMallGoodsDataCopyWithImpl(
      _$_SpaceMallGoodsData _value, $Res Function(_$_SpaceMallGoodsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dressUps = freezed,
    Object? useAssetType = freezed,
    Object? total = freezed,
    Object? pageSize = freezed,
    Object? pageNum = freezed,
  }) {
    return _then(_$_SpaceMallGoodsData(
      dressUps: freezed == dressUps
          ? _value._dressUps
          : dressUps // ignore: cast_nullable_to_non_nullable
              as List<GoodsSkin>?,
      useAssetType: freezed == useAssetType
          ? _value.useAssetType
          : useAssetType // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpaceMallGoodsData implements _SpaceMallGoodsData {
  const _$_SpaceMallGoodsData(
      {final List<GoodsSkin>? dressUps,
      this.useAssetType,
      this.total,
      this.pageSize,
      this.pageNum})
      : _dressUps = dressUps;

  factory _$_SpaceMallGoodsData.fromJson(Map<String, dynamic> json) =>
      _$$_SpaceMallGoodsDataFromJson(json);

  final List<GoodsSkin>? _dressUps;
  @override
  List<GoodsSkin>? get dressUps {
    final value = _dressUps;
    if (value == null) return null;
    if (_dressUps is EqualUnmodifiableListView) return _dressUps;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? useAssetType;
  @override
  final int? total;
  @override
  final int? pageSize;
  @override
  final int? pageNum;

  @override
  String toString() {
    return 'SpaceMallGoodsData(dressUps: $dressUps, useAssetType: $useAssetType, total: $total, pageSize: $pageSize, pageNum: $pageNum)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SpaceMallGoodsData &&
            const DeepCollectionEquality().equals(other._dressUps, _dressUps) &&
            (identical(other.useAssetType, useAssetType) ||
                other.useAssetType == useAssetType) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.pageNum, pageNum) || other.pageNum == pageNum));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_dressUps),
      useAssetType,
      total,
      pageSize,
      pageNum);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpaceMallGoodsDataCopyWith<_$_SpaceMallGoodsData> get copyWith =>
      __$$_SpaceMallGoodsDataCopyWithImpl<_$_SpaceMallGoodsData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpaceMallGoodsDataToJson(
      this,
    );
  }
}

abstract class _SpaceMallGoodsData implements SpaceMallGoodsData {
  const factory _SpaceMallGoodsData(
      {final List<GoodsSkin>? dressUps,
      final int? useAssetType,
      final int? total,
      final int? pageSize,
      final int? pageNum}) = _$_SpaceMallGoodsData;

  factory _SpaceMallGoodsData.fromJson(Map<String, dynamic> json) =
      _$_SpaceMallGoodsData.fromJson;

  @override
  List<GoodsSkin>? get dressUps;
  @override
  int? get useAssetType;
  @override
  int? get total;
  @override
  int? get pageSize;
  @override
  int? get pageNum;
  @override
  @JsonKey(ignore: true)
  _$$_SpaceMallGoodsDataCopyWith<_$_SpaceMallGoodsData> get copyWith =>
      throw _privateConstructorUsedError;
}

GoodsSkin _$GoodsSkinFromJson(Map<String, dynamic> json) {
  return _GoodsSkin.fromJson(json);
}

/// @nodoc
mixin _$GoodsSkin {
  int? get status =>
      throw _privateConstructorUsedError; // （0：未购买，1：已购买(拥有)，2：已使用）
  int? get skuId => throw _privateConstructorUsedError;
  String? get dressUpName => throw _privateConstructorUsedError;
  String? get price => throw _privateConstructorUsedError; // 购买价格
  int? get goodId => throw _privateConstructorUsedError;
  int? get dressUpCategory => throw _privateConstructorUsedError;
  String? get dressShowUrl => throw _privateConstructorUsedError;
  String? get resourcesZipUrl => throw _privateConstructorUsedError;
  String? get dressUpLevel => throw _privateConstructorUsedError;
  List<GoodsSkin?>? get children => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GoodsSkinCopyWith<GoodsSkin> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GoodsSkinCopyWith<$Res> {
  factory $GoodsSkinCopyWith(GoodsSkin value, $Res Function(GoodsSkin) then) =
      _$GoodsSkinCopyWithImpl<$Res, GoodsSkin>;
  @useResult
  $Res call(
      {int? status,
      int? skuId,
      String? dressUpName,
      String? price,
      int? goodId,
      int? dressUpCategory,
      String? dressShowUrl,
      String? resourcesZipUrl,
      String? dressUpLevel,
      List<GoodsSkin?>? children});
}

/// @nodoc
class _$GoodsSkinCopyWithImpl<$Res, $Val extends GoodsSkin>
    implements $GoodsSkinCopyWith<$Res> {
  _$GoodsSkinCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? skuId = freezed,
    Object? dressUpName = freezed,
    Object? price = freezed,
    Object? goodId = freezed,
    Object? dressUpCategory = freezed,
    Object? dressShowUrl = freezed,
    Object? resourcesZipUrl = freezed,
    Object? dressUpLevel = freezed,
    Object? children = freezed,
  }) {
    return _then(_value.copyWith(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressUpName: freezed == dressUpName
          ? _value.dressUpName
          : dressUpName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      goodId: freezed == goodId
          ? _value.goodId
          : goodId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressUpCategory: freezed == dressUpCategory
          ? _value.dressUpCategory
          : dressUpCategory // ignore: cast_nullable_to_non_nullable
              as int?,
      dressShowUrl: freezed == dressShowUrl
          ? _value.dressShowUrl
          : dressShowUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      resourcesZipUrl: freezed == resourcesZipUrl
          ? _value.resourcesZipUrl
          : resourcesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      dressUpLevel: freezed == dressUpLevel
          ? _value.dressUpLevel
          : dressUpLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      children: freezed == children
          ? _value.children
          : children // ignore: cast_nullable_to_non_nullable
              as List<GoodsSkin?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GoodsSkinCopyWith<$Res> implements $GoodsSkinCopyWith<$Res> {
  factory _$$_GoodsSkinCopyWith(
          _$_GoodsSkin value, $Res Function(_$_GoodsSkin) then) =
      __$$_GoodsSkinCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? status,
      int? skuId,
      String? dressUpName,
      String? price,
      int? goodId,
      int? dressUpCategory,
      String? dressShowUrl,
      String? resourcesZipUrl,
      String? dressUpLevel,
      List<GoodsSkin?>? children});
}

/// @nodoc
class __$$_GoodsSkinCopyWithImpl<$Res>
    extends _$GoodsSkinCopyWithImpl<$Res, _$_GoodsSkin>
    implements _$$_GoodsSkinCopyWith<$Res> {
  __$$_GoodsSkinCopyWithImpl(
      _$_GoodsSkin _value, $Res Function(_$_GoodsSkin) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? skuId = freezed,
    Object? dressUpName = freezed,
    Object? price = freezed,
    Object? goodId = freezed,
    Object? dressUpCategory = freezed,
    Object? dressShowUrl = freezed,
    Object? resourcesZipUrl = freezed,
    Object? dressUpLevel = freezed,
    Object? children = freezed,
  }) {
    return _then(_$_GoodsSkin(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressUpName: freezed == dressUpName
          ? _value.dressUpName
          : dressUpName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      goodId: freezed == goodId
          ? _value.goodId
          : goodId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressUpCategory: freezed == dressUpCategory
          ? _value.dressUpCategory
          : dressUpCategory // ignore: cast_nullable_to_non_nullable
              as int?,
      dressShowUrl: freezed == dressShowUrl
          ? _value.dressShowUrl
          : dressShowUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      resourcesZipUrl: freezed == resourcesZipUrl
          ? _value.resourcesZipUrl
          : resourcesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      dressUpLevel: freezed == dressUpLevel
          ? _value.dressUpLevel
          : dressUpLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      children: freezed == children
          ? _value._children
          : children // ignore: cast_nullable_to_non_nullable
              as List<GoodsSkin?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GoodsSkin implements _GoodsSkin {
  const _$_GoodsSkin(
      {this.status,
      this.skuId,
      this.dressUpName,
      this.price,
      this.goodId,
      this.dressUpCategory,
      this.dressShowUrl,
      this.resourcesZipUrl,
      this.dressUpLevel,
      final List<GoodsSkin?>? children})
      : _children = children;

  factory _$_GoodsSkin.fromJson(Map<String, dynamic> json) =>
      _$$_GoodsSkinFromJson(json);

  @override
  final int? status;
// （0：未购买，1：已购买(拥有)，2：已使用）
  @override
  final int? skuId;
  @override
  final String? dressUpName;
  @override
  final String? price;
// 购买价格
  @override
  final int? goodId;
  @override
  final int? dressUpCategory;
  @override
  final String? dressShowUrl;
  @override
  final String? resourcesZipUrl;
  @override
  final String? dressUpLevel;
  final List<GoodsSkin?>? _children;
  @override
  List<GoodsSkin?>? get children {
    final value = _children;
    if (value == null) return null;
    if (_children is EqualUnmodifiableListView) return _children;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'GoodsSkin(status: $status, skuId: $skuId, dressUpName: $dressUpName, price: $price, goodId: $goodId, dressUpCategory: $dressUpCategory, dressShowUrl: $dressShowUrl, resourcesZipUrl: $resourcesZipUrl, dressUpLevel: $dressUpLevel, children: $children)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GoodsSkin &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.dressUpName, dressUpName) ||
                other.dressUpName == dressUpName) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.goodId, goodId) || other.goodId == goodId) &&
            (identical(other.dressUpCategory, dressUpCategory) ||
                other.dressUpCategory == dressUpCategory) &&
            (identical(other.dressShowUrl, dressShowUrl) ||
                other.dressShowUrl == dressShowUrl) &&
            (identical(other.resourcesZipUrl, resourcesZipUrl) ||
                other.resourcesZipUrl == resourcesZipUrl) &&
            (identical(other.dressUpLevel, dressUpLevel) ||
                other.dressUpLevel == dressUpLevel) &&
            const DeepCollectionEquality().equals(other._children, _children));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      status,
      skuId,
      dressUpName,
      price,
      goodId,
      dressUpCategory,
      dressShowUrl,
      resourcesZipUrl,
      dressUpLevel,
      const DeepCollectionEquality().hash(_children));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GoodsSkinCopyWith<_$_GoodsSkin> get copyWith =>
      __$$_GoodsSkinCopyWithImpl<_$_GoodsSkin>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GoodsSkinToJson(
      this,
    );
  }
}

abstract class _GoodsSkin implements GoodsSkin {
  const factory _GoodsSkin(
      {final int? status,
      final int? skuId,
      final String? dressUpName,
      final String? price,
      final int? goodId,
      final int? dressUpCategory,
      final String? dressShowUrl,
      final String? resourcesZipUrl,
      final String? dressUpLevel,
      final List<GoodsSkin?>? children}) = _$_GoodsSkin;

  factory _GoodsSkin.fromJson(Map<String, dynamic> json) =
      _$_GoodsSkin.fromJson;

  @override
  int? get status;
  @override // （0：未购买，1：已购买(拥有)，2：已使用）
  int? get skuId;
  @override
  String? get dressUpName;
  @override
  String? get price;
  @override // 购买价格
  int? get goodId;
  @override
  int? get dressUpCategory;
  @override
  String? get dressShowUrl;
  @override
  String? get resourcesZipUrl;
  @override
  String? get dressUpLevel;
  @override
  List<GoodsSkin?>? get children;
  @override
  @JsonKey(ignore: true)
  _$$_GoodsSkinCopyWith<_$_GoodsSkin> get copyWith =>
      throw _privateConstructorUsedError;
}
