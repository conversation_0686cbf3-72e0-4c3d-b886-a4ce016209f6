// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_home_pop_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SpaceHomePopData _$$_SpaceHomePopDataFromJson(Map<String, dynamic> json) =>
    _$_SpaceHomePopData(
      popups: (json['popups'] as List<dynamic>?)
          ?.map((e) => Popup.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SpaceHomePopDataToJson(_$_SpaceHomePopData instance) =>
    <String, dynamic>{
      'popups': instance.popups,
    };

_$_Popup _$$_PopupFromJson(Map<String, dynamic> json) => _$_Popup(
      extend: json['extend'] == null
          ? null
          : Extend.fromJson(json['extend'] as Map<String, dynamic>),
      name: json['name'] as String?,
      order: json['order'] as int?,
      res: json['res'] == null
          ? null
          : Res.fromJson(json['res'] as Map<String, dynamic>),
      route: json['route'] as String?,
      type: json['type'] as String?,
    );

Map<String, dynamic> _$$_PopupToJson(_$_Popup instance) => <String, dynamic>{
      'extend': instance.extend,
      'name': instance.name,
      'order': instance.order,
      'res': instance.res,
      'route': instance.route,
      'type': instance.type,
    };

_$_Extend _$$_ExtendFromJson(Map<String, dynamic> json) => _$_Extend(
      burialResources: json['burialResources'] == null
          ? null
          : BurialResources.fromJson(
              json['burialResources'] as Map<String, dynamic>),
      changeImg: json['changeImg'] as String?,
      guideUrl: json['guideUrl'] as String?,
      titleImg: json['titleImg'] as String?,
      multiUserLearn: json['multiUserLearn'] == null
          ? null
          : MultiUserLearn.fromJson(
              json['multiUserLearn'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_ExtendToJson(_$_Extend instance) => <String, dynamic>{
      'burialResources': instance.burialResources,
      'changeImg': instance.changeImg,
      'guideUrl': instance.guideUrl,
      'titleImg': instance.titleImg,
      'multiUserLearn': instance.multiUserLearn,
    };

_$_BurialResources _$$_BurialResourcesFromJson(Map<String, dynamic> json) =>
    _$_BurialResources(
      classId: json['classId'] as String?,
      classKey: json['classKey'] as String?,
      courseKey: json['courseKey'] as String?,
      courseName: json['courseName'] as String?,
      lessonKey: json['lessonKey'] as String?,
      lessonName: json['lessonName'] as String?,
    );

Map<String, dynamic> _$$_BurialResourcesToJson(_$_BurialResources instance) =>
    <String, dynamic>{
      'classId': instance.classId,
      'classKey': instance.classKey,
      'courseKey': instance.courseKey,
      'courseName': instance.courseName,
      'lessonKey': instance.lessonKey,
      'lessonName': instance.lessonName,
    };

_$_MultiUserLearn _$$_MultiUserLearnFromJson(Map<String, dynamic> json) =>
    _$_MultiUserLearn(
      achInfo: json['achInfo'] == null
          ? null
          : AchInfo.fromJson(json['achInfo'] as Map<String, dynamic>),
      awardInfo: json['awardInfo'] == null
          ? null
          : AwardInfo.fromJson(json['awardInfo'] as Map<String, dynamic>),
      finishTaskList: (json['finishTaskList'] as List<dynamic>?)
          ?.map((e) => FinishTaskList.fromJson(e as Map<String, dynamic>))
          .toList(),
      memberList: (json['memberList'] as List<dynamic>?)
          ?.map((e) => MemberList.fromJson(e as Map<String, dynamic>))
          .toList(),
      preInfo: json['preInfo'] == null
          ? null
          : PreInfo.fromJson(json['preInfo'] as Map<String, dynamic>),
      totalScore: json['totalScore'] as int?,
    );

Map<String, dynamic> _$$_MultiUserLearnToJson(_$_MultiUserLearn instance) =>
    <String, dynamic>{
      'achInfo': instance.achInfo,
      'awardInfo': instance.awardInfo,
      'finishTaskList': instance.finishTaskList,
      'memberList': instance.memberList,
      'preInfo': instance.preInfo,
      'totalScore': instance.totalScore,
    };

_$_AchInfo _$$_AchInfoFromJson(Map<String, dynamic> json) => _$_AchInfo(
      level: json['level'] as int?,
      name: json['name'] as String?,
      resource: json['resource'] as String?,
      tip: json['tip'] as String?,
      resourceSpineData: json['resourceSpineData'] == null
          ? null
          : ResourceSpineData.fromJson(
              json['resourceSpineData'] as Map<String, dynamic>),
      achList: (json['achList'] as List<dynamic>?)
          ?.map((e) => AchList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_AchInfoToJson(_$_AchInfo instance) =>
    <String, dynamic>{
      'level': instance.level,
      'name': instance.name,
      'resource': instance.resource,
      'tip': instance.tip,
      'resourceSpineData': instance.resourceSpineData,
      'achList': instance.achList,
    };

_$_ResourceSpineData _$$_ResourceSpineDataFromJson(Map<String, dynamic> json) =>
    _$_ResourceSpineData(
      skelFile: json['skelFile'] as String?,
      atlasFile: json['atlasFile'] as String?,
    );

Map<String, dynamic> _$$_ResourceSpineDataToJson(
        _$_ResourceSpineData instance) =>
    <String, dynamic>{
      'skelFile': instance.skelFile,
      'atlasFile': instance.atlasFile,
    };

_$_AwardInfo _$$_AwardInfoFromJson(Map<String, dynamic> json) => _$_AwardInfo(
      awardItem: (json['awardItem'] as List<dynamic>?)
          ?.map((e) => AwardItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      boxResource: json['boxResource'] as String?,
      boxSpineResource: json['boxSpineResource'] == null
          ? null
          : ResourceSpineData.fromJson(
              json['boxSpineResource'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_AwardInfoToJson(_$_AwardInfo instance) =>
    <String, dynamic>{
      'awardItem': instance.awardItem,
      'boxResource': instance.boxResource,
      'boxSpineResource': instance.boxSpineResource,
    };

_$_AwardItem _$$_AwardItemFromJson(Map<String, dynamic> json) => _$_AwardItem(
      count: json['count'] as int?,
      cover: json['cover'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$$_AwardItemToJson(_$_AwardItem instance) =>
    <String, dynamic>{
      'count': instance.count,
      'cover': instance.cover,
      'name': instance.name,
    };

_$_FinishTaskList _$$_FinishTaskListFromJson(Map<String, dynamic> json) =>
    _$_FinishTaskList(
      taskName: json['taskName'] as String?,
      taskScore: json['taskScore'] as int?,
    );

Map<String, dynamic> _$$_FinishTaskListToJson(_$_FinishTaskList instance) =>
    <String, dynamic>{
      'taskName': instance.taskName,
      'taskScore': instance.taskScore,
    };

_$_MemberList _$$_MemberListFromJson(Map<String, dynamic> json) =>
    _$_MemberList(
      achList: (json['achList'] as List<dynamic>?)
          ?.map((e) => AchList.fromJson(e as Map<String, dynamic>))
          .toList(),
      avatarUrl: json['avatarUrl'] as String?,
      nickname: json['nickname'] as String?,
      userType: json['userType'] as int?,
    );

Map<String, dynamic> _$$_MemberListToJson(_$_MemberList instance) =>
    <String, dynamic>{
      'achList': instance.achList,
      'avatarUrl': instance.avatarUrl,
      'nickname': instance.nickname,
      'userType': instance.userType,
    };

_$_AchList _$$_AchListFromJson(Map<String, dynamic> json) => _$_AchList(
      icon: json['icon'] as String?,
      type: json['type'] as int?,
    );

Map<String, dynamic> _$$_AchListToJson(_$_AchList instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'type': instance.type,
    };

_$_PreInfo _$$_PreInfoFromJson(Map<String, dynamic> json) => _$_PreInfo(
      content: json['content'] as String?,
      title: json['title'] as String?,
    );

Map<String, dynamic> _$$_PreInfoToJson(_$_PreInfo instance) =>
    <String, dynamic>{
      'content': instance.content,
      'title': instance.title,
    };

_$_Res _$$_ResFromJson(Map<String, dynamic> json) => _$_Res(
      audio: json['audio'] as String?,
      img: json['img'] as String?,
    );

Map<String, dynamic> _$$_ResToJson(_$_Res instance) => <String, dynamic>{
      'audio': instance.audio,
      'img': instance.img,
    };
