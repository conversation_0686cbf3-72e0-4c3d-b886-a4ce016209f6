// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_mall_goods_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SpaceMallGoodsData _$$_SpaceMallGoodsDataFromJson(
        Map<String, dynamic> json) =>
    _$_SpaceMallGoodsData(
      dressUps: (json['dressUps'] as List<dynamic>?)
          ?.map((e) => GoodsSkin.fromJson(e as Map<String, dynamic>))
          .toList(),
      useAssetType: json['useAssetType'] as int?,
      total: json['total'] as int?,
      pageSize: json['pageSize'] as int?,
      pageNum: json['pageNum'] as int?,
    );

Map<String, dynamic> _$$_SpaceMallGoodsDataToJson(
        _$_SpaceMallGoodsData instance) =>
    <String, dynamic>{
      'dressUps': instance.dressUps,
      'useAssetType': instance.useAssetType,
      'total': instance.total,
      'pageSize': instance.pageSize,
      'pageNum': instance.pageNum,
    };

_$_GoodsSkin _$$_GoodsSkinFromJson(Map<String, dynamic> json) => _$_GoodsSkin(
      status: json['status'] as int?,
      skuId: json['skuId'] as int?,
      dressUpName: json['dressUpName'] as String?,
      price: json['price'] as String?,
      goodId: json['goodId'] as int?,
      dressUpCategory: json['dressUpCategory'] as int?,
      dressShowUrl: json['dressShowUrl'] as String?,
      resourcesZipUrl: json['resourcesZipUrl'] as String?,
      dressUpLevel: json['dressUpLevel'] as String?,
      children: (json['children'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : GoodsSkin.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_GoodsSkinToJson(_$_GoodsSkin instance) =>
    <String, dynamic>{
      'status': instance.status,
      'skuId': instance.skuId,
      'dressUpName': instance.dressUpName,
      'price': instance.price,
      'goodId': instance.goodId,
      'dressUpCategory': instance.dressUpCategory,
      'dressShowUrl': instance.dressShowUrl,
      'resourcesZipUrl': instance.resourcesZipUrl,
      'dressUpLevel': instance.dressUpLevel,
      'children': instance.children,
    };
