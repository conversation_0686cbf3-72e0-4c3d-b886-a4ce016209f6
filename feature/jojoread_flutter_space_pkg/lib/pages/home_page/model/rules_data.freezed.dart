// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rules_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

RulesData _$RulesDataFromJson(Map<String, dynamic> json) {
  return _RulesData.fromJson(json);
}

/// @nodoc
mixin _$RulesData {
  String? get spineUrl => throw _privateConstructorUsedError;
  List<String>? get ruleImageList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RulesDataCopyWith<RulesData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RulesDataCopyWith<$Res> {
  factory $RulesDataCopyWith(RulesData value, $Res Function(RulesData) then) =
      _$RulesDataCopyWithImpl<$Res, RulesData>;
  @useResult
  $Res call({String? spineUrl, List<String>? ruleImageList});
}

/// @nodoc
class _$RulesDataCopyWithImpl<$Res, $Val extends RulesData>
    implements $RulesDataCopyWith<$Res> {
  _$RulesDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? spineUrl = freezed,
    Object? ruleImageList = freezed,
  }) {
    return _then(_value.copyWith(
      spineUrl: freezed == spineUrl
          ? _value.spineUrl
          : spineUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ruleImageList: freezed == ruleImageList
          ? _value.ruleImageList
          : ruleImageList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RulesDataCopyWith<$Res> implements $RulesDataCopyWith<$Res> {
  factory _$$_RulesDataCopyWith(
          _$_RulesData value, $Res Function(_$_RulesData) then) =
      __$$_RulesDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? spineUrl, List<String>? ruleImageList});
}

/// @nodoc
class __$$_RulesDataCopyWithImpl<$Res>
    extends _$RulesDataCopyWithImpl<$Res, _$_RulesData>
    implements _$$_RulesDataCopyWith<$Res> {
  __$$_RulesDataCopyWithImpl(
      _$_RulesData _value, $Res Function(_$_RulesData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? spineUrl = freezed,
    Object? ruleImageList = freezed,
  }) {
    return _then(_$_RulesData(
      spineUrl: freezed == spineUrl
          ? _value.spineUrl
          : spineUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ruleImageList: freezed == ruleImageList
          ? _value._ruleImageList
          : ruleImageList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RulesData implements _RulesData {
  const _$_RulesData({this.spineUrl, final List<String>? ruleImageList})
      : _ruleImageList = ruleImageList;

  factory _$_RulesData.fromJson(Map<String, dynamic> json) =>
      _$$_RulesDataFromJson(json);

  @override
  final String? spineUrl;
  final List<String>? _ruleImageList;
  @override
  List<String>? get ruleImageList {
    final value = _ruleImageList;
    if (value == null) return null;
    if (_ruleImageList is EqualUnmodifiableListView) return _ruleImageList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'RulesData(spineUrl: $spineUrl, ruleImageList: $ruleImageList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RulesData &&
            (identical(other.spineUrl, spineUrl) ||
                other.spineUrl == spineUrl) &&
            const DeepCollectionEquality()
                .equals(other._ruleImageList, _ruleImageList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, spineUrl,
      const DeepCollectionEquality().hash(_ruleImageList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RulesDataCopyWith<_$_RulesData> get copyWith =>
      __$$_RulesDataCopyWithImpl<_$_RulesData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RulesDataToJson(
      this,
    );
  }
}

abstract class _RulesData implements RulesData {
  const factory _RulesData(
      {final String? spineUrl,
      final List<String>? ruleImageList}) = _$_RulesData;

  factory _RulesData.fromJson(Map<String, dynamic> json) =
      _$_RulesData.fromJson;

  @override
  String? get spineUrl;
  @override
  List<String>? get ruleImageList;
  @override
  @JsonKey(ignore: true)
  _$$_RulesDataCopyWith<_$_RulesData> get copyWith =>
      throw _privateConstructorUsedError;
}
