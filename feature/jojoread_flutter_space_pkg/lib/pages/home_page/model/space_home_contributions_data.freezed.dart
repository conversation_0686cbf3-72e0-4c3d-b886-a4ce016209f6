// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_home_contributions_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceHomeContributionsData _$SpaceHomeContributionsDataFromJson(
    Map<String, dynamic> json) {
  return _SpaceHomeContributionsData.fromJson(json);
}

/// @nodoc
mixin _$SpaceHomeContributionsData {
  int? get teamId => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  List<ContributionDetail>? get contributions =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceHomeContributionsDataCopyWith<SpaceHomeContributionsData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceHomeContributionsDataCopyWith<$Res> {
  factory $SpaceHomeContributionsDataCopyWith(SpaceHomeContributionsData value,
          $Res Function(SpaceHomeContributionsData) then) =
      _$SpaceHomeContributionsDataCopyWithImpl<$Res,
          SpaceHomeContributionsData>;
  @useResult
  $Res call(
      {int? teamId,
      int? classId,
      int? courseId,
      List<ContributionDetail>? contributions});
}

/// @nodoc
class _$SpaceHomeContributionsDataCopyWithImpl<$Res,
        $Val extends SpaceHomeContributionsData>
    implements $SpaceHomeContributionsDataCopyWith<$Res> {
  _$SpaceHomeContributionsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? classId = freezed,
    Object? courseId = freezed,
    Object? contributions = freezed,
  }) {
    return _then(_value.copyWith(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      contributions: freezed == contributions
          ? _value.contributions
          : contributions // ignore: cast_nullable_to_non_nullable
              as List<ContributionDetail>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SpaceHomeContributionsDataCopyWith<$Res>
    implements $SpaceHomeContributionsDataCopyWith<$Res> {
  factory _$$_SpaceHomeContributionsDataCopyWith(
          _$_SpaceHomeContributionsData value,
          $Res Function(_$_SpaceHomeContributionsData) then) =
      __$$_SpaceHomeContributionsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? teamId,
      int? classId,
      int? courseId,
      List<ContributionDetail>? contributions});
}

/// @nodoc
class __$$_SpaceHomeContributionsDataCopyWithImpl<$Res>
    extends _$SpaceHomeContributionsDataCopyWithImpl<$Res,
        _$_SpaceHomeContributionsData>
    implements _$$_SpaceHomeContributionsDataCopyWith<$Res> {
  __$$_SpaceHomeContributionsDataCopyWithImpl(
      _$_SpaceHomeContributionsData _value,
      $Res Function(_$_SpaceHomeContributionsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? classId = freezed,
    Object? courseId = freezed,
    Object? contributions = freezed,
  }) {
    return _then(_$_SpaceHomeContributionsData(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      contributions: freezed == contributions
          ? _value._contributions
          : contributions // ignore: cast_nullable_to_non_nullable
              as List<ContributionDetail>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpaceHomeContributionsData implements _SpaceHomeContributionsData {
  const _$_SpaceHomeContributionsData(
      {this.teamId,
      this.classId,
      this.courseId,
      final List<ContributionDetail>? contributions})
      : _contributions = contributions;

  factory _$_SpaceHomeContributionsData.fromJson(Map<String, dynamic> json) =>
      _$$_SpaceHomeContributionsDataFromJson(json);

  @override
  final int? teamId;
  @override
  final int? classId;
  @override
  final int? courseId;
  final List<ContributionDetail>? _contributions;
  @override
  List<ContributionDetail>? get contributions {
    final value = _contributions;
    if (value == null) return null;
    if (_contributions is EqualUnmodifiableListView) return _contributions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SpaceHomeContributionsData(teamId: $teamId, classId: $classId, courseId: $courseId, contributions: $contributions)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SpaceHomeContributionsData &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            const DeepCollectionEquality()
                .equals(other._contributions, _contributions));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, teamId, classId, courseId,
      const DeepCollectionEquality().hash(_contributions));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpaceHomeContributionsDataCopyWith<_$_SpaceHomeContributionsData>
      get copyWith => __$$_SpaceHomeContributionsDataCopyWithImpl<
          _$_SpaceHomeContributionsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpaceHomeContributionsDataToJson(
      this,
    );
  }
}

abstract class _SpaceHomeContributionsData
    implements SpaceHomeContributionsData {
  const factory _SpaceHomeContributionsData(
          {final int? teamId,
          final int? classId,
          final int? courseId,
          final List<ContributionDetail>? contributions}) =
      _$_SpaceHomeContributionsData;

  factory _SpaceHomeContributionsData.fromJson(Map<String, dynamic> json) =
      _$_SpaceHomeContributionsData.fromJson;

  @override
  int? get teamId;
  @override
  int? get classId;
  @override
  int? get courseId;
  @override
  List<ContributionDetail>? get contributions;
  @override
  @JsonKey(ignore: true)
  _$$_SpaceHomeContributionsDataCopyWith<_$_SpaceHomeContributionsData>
      get copyWith => throw _privateConstructorUsedError;
}

ContributionDetail _$ContributionDetailFromJson(Map<String, dynamic> json) {
  return _ContributionDetail.fromJson(json);
}

/// @nodoc
mixin _$ContributionDetail {
  int? get userId => throw _privateConstructorUsedError;
  String? get userName => throw _privateConstructorUsedError;
  String? get userAvatar => throw _privateConstructorUsedError;
  int? get contributionType => throw _privateConstructorUsedError;
  int? get contributionVal => throw _privateConstructorUsedError;
  String? get contributionDesc => throw _privateConstructorUsedError;
  int? get contributionDate => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContributionDetailCopyWith<ContributionDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContributionDetailCopyWith<$Res> {
  factory $ContributionDetailCopyWith(
          ContributionDetail value, $Res Function(ContributionDetail) then) =
      _$ContributionDetailCopyWithImpl<$Res, ContributionDetail>;
  @useResult
  $Res call(
      {int? userId,
      String? userName,
      String? userAvatar,
      int? contributionType,
      int? contributionVal,
      String? contributionDesc,
      int? contributionDate});
}

/// @nodoc
class _$ContributionDetailCopyWithImpl<$Res, $Val extends ContributionDetail>
    implements $ContributionDetailCopyWith<$Res> {
  _$ContributionDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? userName = freezed,
    Object? userAvatar = freezed,
    Object? contributionType = freezed,
    Object? contributionVal = freezed,
    Object? contributionDesc = freezed,
    Object? contributionDate = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
      userAvatar: freezed == userAvatar
          ? _value.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      contributionType: freezed == contributionType
          ? _value.contributionType
          : contributionType // ignore: cast_nullable_to_non_nullable
              as int?,
      contributionVal: freezed == contributionVal
          ? _value.contributionVal
          : contributionVal // ignore: cast_nullable_to_non_nullable
              as int?,
      contributionDesc: freezed == contributionDesc
          ? _value.contributionDesc
          : contributionDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      contributionDate: freezed == contributionDate
          ? _value.contributionDate
          : contributionDate // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ContributionDetailCopyWith<$Res>
    implements $ContributionDetailCopyWith<$Res> {
  factory _$$_ContributionDetailCopyWith(_$_ContributionDetail value,
          $Res Function(_$_ContributionDetail) then) =
      __$$_ContributionDetailCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? userId,
      String? userName,
      String? userAvatar,
      int? contributionType,
      int? contributionVal,
      String? contributionDesc,
      int? contributionDate});
}

/// @nodoc
class __$$_ContributionDetailCopyWithImpl<$Res>
    extends _$ContributionDetailCopyWithImpl<$Res, _$_ContributionDetail>
    implements _$$_ContributionDetailCopyWith<$Res> {
  __$$_ContributionDetailCopyWithImpl(
      _$_ContributionDetail _value, $Res Function(_$_ContributionDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? userName = freezed,
    Object? userAvatar = freezed,
    Object? contributionType = freezed,
    Object? contributionVal = freezed,
    Object? contributionDesc = freezed,
    Object? contributionDate = freezed,
  }) {
    return _then(_$_ContributionDetail(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
      userAvatar: freezed == userAvatar
          ? _value.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      contributionType: freezed == contributionType
          ? _value.contributionType
          : contributionType // ignore: cast_nullable_to_non_nullable
              as int?,
      contributionVal: freezed == contributionVal
          ? _value.contributionVal
          : contributionVal // ignore: cast_nullable_to_non_nullable
              as int?,
      contributionDesc: freezed == contributionDesc
          ? _value.contributionDesc
          : contributionDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      contributionDate: freezed == contributionDate
          ? _value.contributionDate
          : contributionDate // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ContributionDetail implements _ContributionDetail {
  const _$_ContributionDetail(
      {this.userId,
      this.userName,
      this.userAvatar,
      this.contributionType,
      this.contributionVal,
      this.contributionDesc,
      this.contributionDate});

  factory _$_ContributionDetail.fromJson(Map<String, dynamic> json) =>
      _$$_ContributionDetailFromJson(json);

  @override
  final int? userId;
  @override
  final String? userName;
  @override
  final String? userAvatar;
  @override
  final int? contributionType;
  @override
  final int? contributionVal;
  @override
  final String? contributionDesc;
  @override
  final int? contributionDate;

  @override
  String toString() {
    return 'ContributionDetail(userId: $userId, userName: $userName, userAvatar: $userAvatar, contributionType: $contributionType, contributionVal: $contributionVal, contributionDesc: $contributionDesc, contributionDate: $contributionDate)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ContributionDetail &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.contributionType, contributionType) ||
                other.contributionType == contributionType) &&
            (identical(other.contributionVal, contributionVal) ||
                other.contributionVal == contributionVal) &&
            (identical(other.contributionDesc, contributionDesc) ||
                other.contributionDesc == contributionDesc) &&
            (identical(other.contributionDate, contributionDate) ||
                other.contributionDate == contributionDate));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userId, userName, userAvatar,
      contributionType, contributionVal, contributionDesc, contributionDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ContributionDetailCopyWith<_$_ContributionDetail> get copyWith =>
      __$$_ContributionDetailCopyWithImpl<_$_ContributionDetail>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ContributionDetailToJson(
      this,
    );
  }
}

abstract class _ContributionDetail implements ContributionDetail {
  const factory _ContributionDetail(
      {final int? userId,
      final String? userName,
      final String? userAvatar,
      final int? contributionType,
      final int? contributionVal,
      final String? contributionDesc,
      final int? contributionDate}) = _$_ContributionDetail;

  factory _ContributionDetail.fromJson(Map<String, dynamic> json) =
      _$_ContributionDetail.fromJson;

  @override
  int? get userId;
  @override
  String? get userName;
  @override
  String? get userAvatar;
  @override
  int? get contributionType;
  @override
  int? get contributionVal;
  @override
  String? get contributionDesc;
  @override
  int? get contributionDate;
  @override
  @JsonKey(ignore: true)
  _$$_ContributionDetailCopyWith<_$_ContributionDetail> get copyWith =>
      throw _privateConstructorUsedError;
}
