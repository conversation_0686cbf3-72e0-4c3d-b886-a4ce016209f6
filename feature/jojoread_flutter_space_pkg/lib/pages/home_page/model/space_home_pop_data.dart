import 'package:jojo_flutter_base/base.dart';

part 'space_home_pop_data.freezed.dart';
part 'space_home_pop_data.g.dart';

@freezed
class SpaceHomePopData with _$SpaceHomePopData {
  const factory SpaceHomePopData({
    List<Popup>? popups,
  }) = _SpaceHomePopData;

  factory SpaceHomePopData.fromJson(Map<String, dynamic> json) =>
      _$SpaceHomePopDataFromJson(json);
}

@freezed
class Popup with _$Popup {
  const factory Popup({
    Extend? extend,
    String? name,
    int? order,
    Res? res,
    String? route,
    String? type,
  }) = _Popup;

  factory Popup.fromJson(Map<String, dynamic> json) => _$PopupFromJson(json);
}

@freezed
class Extend with _$Extend {
  const factory Extend({
    BurialResources? burialResources,
    String? changeImg,
    String? guideUrl,
    String? titleImg,
    MultiUserLearn? multiUserLearn,
  }) = _Extend;

  factory Extend.fromJson(Map<String, dynamic> json) => _$ExtendFromJson(json);
}

@freezed
class BurialResources with _$BurialResources {
  const factory BurialResources({
    String? classId,
    String? classKey,
    String? courseKey,
    String? courseName,
    String? lessonKey,
    String? lessonName,
  }) = _BurialResources;

  factory BurialResources.fromJson(Map<String, dynamic> json) =>
      _$BurialResourcesFromJson(json);
}

@freezed
class MultiUserLearn with _$MultiUserLearn {
  const factory MultiUserLearn({
    AchInfo? achInfo,
    AwardInfo? awardInfo,
    List<FinishTaskList>? finishTaskList,
    List<MemberList>? memberList,
    PreInfo? preInfo,
    int? totalScore,
  }) = _MultiUserLearn;

  factory MultiUserLearn.fromJson(Map<String, dynamic> json) =>
      _$MultiUserLearnFromJson(json);
}

@freezed
class AchInfo with _$AchInfo {
  const factory AchInfo({
    int? level,
    String? name,
    String? resource,
    String? tip,
    ResourceSpineData? resourceSpineData,
    List<AchList>? achList,
  }) = _AchInfo;

  factory AchInfo.fromJson(Map<String, dynamic> json) =>
      _$AchInfoFromJson(json);
}

@freezed
class ResourceSpineData with _$ResourceSpineData {
  const factory ResourceSpineData({
    //  skel本地文件
    String? skelFile,
    //  atlas本地文件
    String? atlasFile,
  }) = _ResourceSpineData;

  factory ResourceSpineData.fromJson(Map<String, dynamic> json) =>
      _$ResourceSpineDataFromJson(json);
}

@freezed
class AwardInfo with _$AwardInfo {
  const factory AwardInfo({
    List<AwardItem>? awardItem,
    String? boxResource,
    ResourceSpineData? boxSpineResource,
  }) = _AwardInfo;

  factory AwardInfo.fromJson(Map<String, dynamic> json) =>
      _$AwardInfoFromJson(json);
}

@freezed
class AwardItem with _$AwardItem {
  const factory AwardItem({
    int? count,
    String? cover,
    String? name,
  }) = _AwardItem;

  factory AwardItem.fromJson(Map<String, dynamic> json) =>
      _$AwardItemFromJson(json);
}

@freezed
class FinishTaskList with _$FinishTaskList {
  const factory FinishTaskList({
    String? taskName,
    int? taskScore,
  }) = _FinishTaskList;

  factory FinishTaskList.fromJson(Map<String, dynamic> json) =>
      _$FinishTaskListFromJson(json);
}

@freezed
class MemberList with _$MemberList {
  const factory MemberList({
    List<AchList>? achList,
    String? avatarUrl,
    String? nickname,
    int? userType,
  }) = _MemberList;

  factory MemberList.fromJson(Map<String, dynamic> json) =>
      _$MemberListFromJson(json);
}

@freezed
class AchList with _$AchList {
  const factory AchList({
    String? icon,
    int? type,
  }) = _AchList;

  factory AchList.fromJson(Map<String, dynamic> json) =>
      _$AchListFromJson(json);
}

@freezed
class PreInfo with _$PreInfo {
  const factory PreInfo({
    String? content,
    String? title,
  }) = _PreInfo;

  factory PreInfo.fromJson(Map<String, dynamic> json) =>
      _$PreInfoFromJson(json);
}

@freezed
class Res with _$Res {
  const factory Res({
    String? audio,
    String? img,
  }) = _Res;

  factory Res.fromJson(Map<String, dynamic> json) => _$ResFromJson(json);
}
