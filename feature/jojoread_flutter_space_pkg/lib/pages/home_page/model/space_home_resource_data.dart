import 'package:jojo_flutter_base/base.dart';

part 'space_home_resource_data.freezed.dart';
part 'space_home_resource_data.g.dart';

@freezed
class SpaceHomeResourceData with _$SpaceHomeResourceData {
  const factory SpaceHomeResourceData({
    ///组员骨骼压缩包
    String? memberBonesZipUrl,

    ///背景压缩包
    String? backGroundZipUrl,

    ///点赞动画压缩包
    String? likeZipUrl,

    ///戳一戳动画压缩包
    String? prickZipUrl,

    ///宝箱动画压缩包
    String? boxZipUrl,

    ///规则介绍页压缩包
    String? ruleZipUrl,

    ///学习按钮信息
    ButtonInfo? learnButtonInfo,

    ///规则按钮信息
    ButtonInfo? ruleButtonInfo,

    ///历史战绩按钮信息
    ButtonInfo? historyPerformanceButtonInfo,

    ///团队贡献详情按钮信息
    ButtonInfo? teamContributionDetailButtonInfo,

    ///我的装扮按钮信息
    ButtonInfo? myDressUpButtonInfo,

    ///黄金队伍图标信息
    DTeamIconInfo? goldTeamIconInfo,

    ///超神队伍图标信息
    DTeamIconInfo? legendTeamIconInfo,

    ///默认皮肤列表
    List<DefaultSkinList>? defaultSkinList,

    ///已拥有和默认皮肤列表
    List<DefaultSkinList>? ownAndDefaultSkinList,

    /// 彩带资源
    String? flowerZipUrl,
  }) = _SpaceHomeResourceData;

  factory SpaceHomeResourceData.fromJson(Map<String, dynamic> json) =>
      _$SpaceHomeResourceDataFromJson(json);
}

@freezed
class DefaultSkinList with _$DefaultSkinList {
  const factory DefaultSkinList({
    int? skinId,
    String? dressShowUrl,
    String? categoryShowName,
    int? categoryShowCode,
    String? resourcesZipUrl,
  }) = _DefaultSkinList;

  factory DefaultSkinList.fromJson(Map<String, dynamic> json) =>
      _$DefaultSkinListFromJson(json);
}

@freezed
class DTeamIconInfo with _$DTeamIconInfo {
  const factory DTeamIconInfo({
    String? selectedIconUrl,
    String? unselectIconUrl,
    String? iconName,
  }) = _DTeamIconInfo;

  factory DTeamIconInfo.fromJson(Map<String, dynamic> json) =>
      _$DTeamIconInfoFromJson(json);
}

@freezed
class ButtonInfo with _$ButtonInfo {
  const factory ButtonInfo({
    String? iconUrl,
    String? buttonName,
    String? buttonUrl,
  }) = _ButtonInfo;

  factory ButtonInfo.fromJson(Map<String, dynamic> json) =>
      _$ButtonInfoFromJson(json);
}
