// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_home_contributions_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SpaceHomeContributionsData _$$_SpaceHomeContributionsDataFromJson(
        Map<String, dynamic> json) =>
    _$_SpaceHomeContributionsData(
      teamId: json['teamId'] as int?,
      classId: json['classId'] as int?,
      courseId: json['courseId'] as int?,
      contributions: (json['contributions'] as List<dynamic>?)
          ?.map((e) => ContributionDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SpaceHomeContributionsDataToJson(
        _$_SpaceHomeContributionsData instance) =>
    <String, dynamic>{
      'teamId': instance.teamId,
      'classId': instance.classId,
      'courseId': instance.courseId,
      'contributions': instance.contributions,
    };

_$_ContributionDetail _$$_ContributionDetailFromJson(
        Map<String, dynamic> json) =>
    _$_ContributionDetail(
      userId: json['userId'] as int?,
      userName: json['userName'] as String?,
      userAvatar: json['userAvatar'] as String?,
      contributionType: json['contributionType'] as int?,
      contributionVal: json['contributionVal'] as int?,
      contributionDesc: json['contributionDesc'] as String?,
      contributionDate: json['contributionDate'] as int?,
    );

Map<String, dynamic> _$$_ContributionDetailToJson(
        _$_ContributionDetail instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userName': instance.userName,
      'userAvatar': instance.userAvatar,
      'contributionType': instance.contributionType,
      'contributionVal': instance.contributionVal,
      'contributionDesc': instance.contributionDesc,
      'contributionDate': instance.contributionDate,
    };
