// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_home_group_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SpaceHomeGroupData _$$_SpaceHomeGroupDataFromJson(
        Map<String, dynamic> json) =>
    _$_SpaceHomeGroupData(
      teamId: json['teamId'] as int?,
      classId: json['classId'] as int?,
      courseId: json['courseId'] as int?,
      backgroundImage: json['backgroundImage'] as String?,
      stateImage: json['stateImage'] as String?,
      teamMembers: (json['teamMembers'] as List<dynamic>?)
          ?.map((e) => TeamMember.fromJson(e as Map<String, dynamic>))
          .toList(),
      teamInfo: json['teamInfo'] == null
          ? null
          : TeamInfo.fromJson(json['teamInfo'] as Map<String, dynamic>),
      contributions: (json['contributions'] as List<dynamic>?)
          ?.map((e) => Contribution.fromJson(e as Map<String, dynamic>))
          .toList(),
      seasonInfo: json['seasonInfo'] == null
          ? null
          : SeasonInfo.fromJson(json['seasonInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_SpaceHomeGroupDataToJson(
        _$_SpaceHomeGroupData instance) =>
    <String, dynamic>{
      'teamId': instance.teamId,
      'classId': instance.classId,
      'courseId': instance.courseId,
      'backgroundImage': instance.backgroundImage,
      'stateImage': instance.stateImage,
      'teamMembers': instance.teamMembers,
      'teamInfo': instance.teamInfo,
      'contributions': instance.contributions,
      'seasonInfo': instance.seasonInfo,
    };

_$_Contribution _$$_ContributionFromJson(Map<String, dynamic> json) =>
    _$_Contribution(
      order: json['order'] as int?,
      userId: json['userId'] as int?,
      userName: json['userName'] as String?,
      userAvatar: json['userAvatar'] as String?,
      seasonTitle: (json['seasonTitle'] as List<dynamic>?)
          ?.map((e) => SeasonTitle.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentTodayIntegral: json['currentTodayIntegral'] as int?,
      currentWeekIntegral: json['currentWeekIntegral'] as int?,
    );

Map<String, dynamic> _$$_ContributionToJson(_$_Contribution instance) =>
    <String, dynamic>{
      'order': instance.order,
      'userId': instance.userId,
      'userName': instance.userName,
      'userAvatar': instance.userAvatar,
      'seasonTitle': instance.seasonTitle,
      'currentTodayIntegral': instance.currentTodayIntegral,
      'currentWeekIntegral': instance.currentWeekIntegral,
    };

_$_SeasonTitle _$$_SeasonTitleFromJson(Map<String, dynamic> json) =>
    _$_SeasonTitle(
      titlePicUrl: json['titlePicUrl'] as String?,
    );

Map<String, dynamic> _$$_SeasonTitleToJson(_$_SeasonTitle instance) =>
    <String, dynamic>{
      'titlePicUrl': instance.titlePicUrl,
    };

_$_SeasonInfo _$$_SeasonInfoFromJson(Map<String, dynamic> json) =>
    _$_SeasonInfo(
      seasonId: json['seasonId'] as int?,
      startTime: json['startTime'] as int?,
      endTime: json['endTime'] as int?,
    );

Map<String, dynamic> _$$_SeasonInfoToJson(_$_SeasonInfo instance) =>
    <String, dynamic>{
      'seasonId': instance.seasonId,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
    };

_$_TeamInfo _$$_TeamInfoFromJson(Map<String, dynamic> json) => _$_TeamInfo(
      currentIntegral: json['currentIntegral'] as int?,
      totalIntegral: json['totalIntegral'] as int?,
      upgradeInfo: (json['upgradeInfo'] as List<dynamic>?)
          ?.map((e) => GroupUpgradeInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TeamInfoToJson(_$_TeamInfo instance) =>
    <String, dynamic>{
      'currentIntegral': instance.currentIntegral,
      'totalIntegral': instance.totalIntegral,
      'upgradeInfo': instance.upgradeInfo,
    };

_$_GroupUpgradeInfo _$$_GroupUpgradeInfoFromJson(Map<String, dynamic> json) =>
    _$_GroupUpgradeInfo(
      upgradeTargetIntegral: json['upgradeTargetIntegral'] as int?,
      upgradeLevelDesc: json['upgradeLevelDesc'] as String?,
    );

Map<String, dynamic> _$$_GroupUpgradeInfoToJson(_$_GroupUpgradeInfo instance) =>
    <String, dynamic>{
      'upgradeTargetIntegral': instance.upgradeTargetIntegral,
      'upgradeLevelDesc': instance.upgradeLevelDesc,
    };

_$_TeamMember _$$_TeamMemberFromJson(Map<String, dynamic> json) =>
    _$_TeamMember(
      userId: json['userId'] as int?,
      userName: json['userName'] as String?,
      userType: json['userType'] as int?,
      dressList: (json['dressList'] as List<dynamic>?)
          ?.map((e) => Skin.fromJson(e as Map<String, dynamic>))
          .toList(),
      userStatus: json['userStatus'] as int?,
      studyBtn: json['studyBtn'] == null
          ? null
          : StudyBtn.fromJson(json['studyBtn'] as Map<String, dynamic>),
      userContribution: (json['userContribution'] as List<dynamic>?)
          ?.map((e) => UserContribution.fromJson(e as Map<String, dynamic>))
          .toList(),
      learningInfo: json['learningInfo'] == null
          ? null
          : LearningInfo.fromJson(json['learningInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_TeamMemberToJson(_$_TeamMember instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userName': instance.userName,
      'userType': instance.userType,
      'dressList': instance.dressList,
      'userStatus': instance.userStatus,
      'studyBtn': instance.studyBtn,
      'userContribution': instance.userContribution,
      'learningInfo': instance.learningInfo,
    };

_$_StudyBtn _$$_StudyBtnFromJson(Map<String, dynamic> json) => _$_StudyBtn(
      btnName: json['btnName'] as String?,
      router: json['router'] as String?,
    );

Map<String, dynamic> _$$_StudyBtnToJson(_$_StudyBtn instance) =>
    <String, dynamic>{
      'btnName': instance.btnName,
      'router': instance.router,
    };

_$_LearningInfo _$$_LearningInfoFromJson(Map<String, dynamic> json) =>
    _$_LearningInfo(
      icon: json['icon'] as String?,
      continuousLearningDays: json['continuousLearningDays'] as int?,
      todayIntegral: json['todayIntegral'] as int?,
    );

Map<String, dynamic> _$$_LearningInfoToJson(_$_LearningInfo instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'continuousLearningDays': instance.continuousLearningDays,
      'todayIntegral': instance.todayIntegral,
    };

_$_Skin _$$_SkinFromJson(Map<String, dynamic> json) => _$_Skin(
      dressUpCategoryId: json['dressUpCategoryId'] as int?,
      dressShowUrl: json['dressShowUrl'] as String?,
      resourcesZipUrl: json['resourcesZipUrl'] as String?,
    );

Map<String, dynamic> _$$_SkinToJson(_$_Skin instance) => <String, dynamic>{
      'dressUpCategoryId': instance.dressUpCategoryId,
      'dressShowUrl': instance.dressShowUrl,
      'resourcesZipUrl': instance.resourcesZipUrl,
    };

_$_UserContribution _$$_UserContributionFromJson(Map<String, dynamic> json) =>
    _$_UserContribution(
      json['contributionType'] as int?,
      json['titlePicUrl'] as String?,
    );

Map<String, dynamic> _$$_UserContributionToJson(_$_UserContribution instance) =>
    <String, dynamic>{
      'contributionType': instance.contributionType,
      'titlePicUrl': instance.titlePicUrl,
    };
