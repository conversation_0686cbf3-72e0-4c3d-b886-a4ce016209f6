// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_home_group_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceHomeGroupData _$SpaceHomeGroupDataFromJson(Map<String, dynamic> json) {
  return _SpaceHomeGroupData.fromJson(json);
}

/// @nodoc
mixin _$SpaceHomeGroupData {
  int? get teamId => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  String? get backgroundImage => throw _privateConstructorUsedError;
  String? get stateImage => throw _privateConstructorUsedError;
  List<TeamMember>? get teamMembers => throw _privateConstructorUsedError;
  TeamInfo? get teamInfo => throw _privateConstructorUsedError;
  List<Contribution>? get contributions => throw _privateConstructorUsedError;
  SeasonInfo? get seasonInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceHomeGroupDataCopyWith<SpaceHomeGroupData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceHomeGroupDataCopyWith<$Res> {
  factory $SpaceHomeGroupDataCopyWith(
          SpaceHomeGroupData value, $Res Function(SpaceHomeGroupData) then) =
      _$SpaceHomeGroupDataCopyWithImpl<$Res, SpaceHomeGroupData>;
  @useResult
  $Res call(
      {int? teamId,
      int? classId,
      int? courseId,
      String? backgroundImage,
      String? stateImage,
      List<TeamMember>? teamMembers,
      TeamInfo? teamInfo,
      List<Contribution>? contributions,
      SeasonInfo? seasonInfo});

  $TeamInfoCopyWith<$Res>? get teamInfo;
  $SeasonInfoCopyWith<$Res>? get seasonInfo;
}

/// @nodoc
class _$SpaceHomeGroupDataCopyWithImpl<$Res, $Val extends SpaceHomeGroupData>
    implements $SpaceHomeGroupDataCopyWith<$Res> {
  _$SpaceHomeGroupDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? classId = freezed,
    Object? courseId = freezed,
    Object? backgroundImage = freezed,
    Object? stateImage = freezed,
    Object? teamMembers = freezed,
    Object? teamInfo = freezed,
    Object? contributions = freezed,
    Object? seasonInfo = freezed,
  }) {
    return _then(_value.copyWith(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as String?,
      stateImage: freezed == stateImage
          ? _value.stateImage
          : stateImage // ignore: cast_nullable_to_non_nullable
              as String?,
      teamMembers: freezed == teamMembers
          ? _value.teamMembers
          : teamMembers // ignore: cast_nullable_to_non_nullable
              as List<TeamMember>?,
      teamInfo: freezed == teamInfo
          ? _value.teamInfo
          : teamInfo // ignore: cast_nullable_to_non_nullable
              as TeamInfo?,
      contributions: freezed == contributions
          ? _value.contributions
          : contributions // ignore: cast_nullable_to_non_nullable
              as List<Contribution>?,
      seasonInfo: freezed == seasonInfo
          ? _value.seasonInfo
          : seasonInfo // ignore: cast_nullable_to_non_nullable
              as SeasonInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TeamInfoCopyWith<$Res>? get teamInfo {
    if (_value.teamInfo == null) {
      return null;
    }

    return $TeamInfoCopyWith<$Res>(_value.teamInfo!, (value) {
      return _then(_value.copyWith(teamInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SeasonInfoCopyWith<$Res>? get seasonInfo {
    if (_value.seasonInfo == null) {
      return null;
    }

    return $SeasonInfoCopyWith<$Res>(_value.seasonInfo!, (value) {
      return _then(_value.copyWith(seasonInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_SpaceHomeGroupDataCopyWith<$Res>
    implements $SpaceHomeGroupDataCopyWith<$Res> {
  factory _$$_SpaceHomeGroupDataCopyWith(_$_SpaceHomeGroupData value,
          $Res Function(_$_SpaceHomeGroupData) then) =
      __$$_SpaceHomeGroupDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? teamId,
      int? classId,
      int? courseId,
      String? backgroundImage,
      String? stateImage,
      List<TeamMember>? teamMembers,
      TeamInfo? teamInfo,
      List<Contribution>? contributions,
      SeasonInfo? seasonInfo});

  @override
  $TeamInfoCopyWith<$Res>? get teamInfo;
  @override
  $SeasonInfoCopyWith<$Res>? get seasonInfo;
}

/// @nodoc
class __$$_SpaceHomeGroupDataCopyWithImpl<$Res>
    extends _$SpaceHomeGroupDataCopyWithImpl<$Res, _$_SpaceHomeGroupData>
    implements _$$_SpaceHomeGroupDataCopyWith<$Res> {
  __$$_SpaceHomeGroupDataCopyWithImpl(
      _$_SpaceHomeGroupData _value, $Res Function(_$_SpaceHomeGroupData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? classId = freezed,
    Object? courseId = freezed,
    Object? backgroundImage = freezed,
    Object? stateImage = freezed,
    Object? teamMembers = freezed,
    Object? teamInfo = freezed,
    Object? contributions = freezed,
    Object? seasonInfo = freezed,
  }) {
    return _then(_$_SpaceHomeGroupData(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as String?,
      stateImage: freezed == stateImage
          ? _value.stateImage
          : stateImage // ignore: cast_nullable_to_non_nullable
              as String?,
      teamMembers: freezed == teamMembers
          ? _value._teamMembers
          : teamMembers // ignore: cast_nullable_to_non_nullable
              as List<TeamMember>?,
      teamInfo: freezed == teamInfo
          ? _value.teamInfo
          : teamInfo // ignore: cast_nullable_to_non_nullable
              as TeamInfo?,
      contributions: freezed == contributions
          ? _value._contributions
          : contributions // ignore: cast_nullable_to_non_nullable
              as List<Contribution>?,
      seasonInfo: freezed == seasonInfo
          ? _value.seasonInfo
          : seasonInfo // ignore: cast_nullable_to_non_nullable
              as SeasonInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpaceHomeGroupData implements _SpaceHomeGroupData {
  const _$_SpaceHomeGroupData(
      {this.teamId,
      this.classId,
      this.courseId,
      this.backgroundImage,
      this.stateImage,
      final List<TeamMember>? teamMembers,
      this.teamInfo,
      final List<Contribution>? contributions,
      this.seasonInfo})
      : _teamMembers = teamMembers,
        _contributions = contributions;

  factory _$_SpaceHomeGroupData.fromJson(Map<String, dynamic> json) =>
      _$$_SpaceHomeGroupDataFromJson(json);

  @override
  final int? teamId;
  @override
  final int? classId;
  @override
  final int? courseId;
  @override
  final String? backgroundImage;
  @override
  final String? stateImage;
  final List<TeamMember>? _teamMembers;
  @override
  List<TeamMember>? get teamMembers {
    final value = _teamMembers;
    if (value == null) return null;
    if (_teamMembers is EqualUnmodifiableListView) return _teamMembers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final TeamInfo? teamInfo;
  final List<Contribution>? _contributions;
  @override
  List<Contribution>? get contributions {
    final value = _contributions;
    if (value == null) return null;
    if (_contributions is EqualUnmodifiableListView) return _contributions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final SeasonInfo? seasonInfo;

  @override
  String toString() {
    return 'SpaceHomeGroupData(teamId: $teamId, classId: $classId, courseId: $courseId, backgroundImage: $backgroundImage, stateImage: $stateImage, teamMembers: $teamMembers, teamInfo: $teamInfo, contributions: $contributions, seasonInfo: $seasonInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SpaceHomeGroupData &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.backgroundImage, backgroundImage) ||
                other.backgroundImage == backgroundImage) &&
            (identical(other.stateImage, stateImage) ||
                other.stateImage == stateImage) &&
            const DeepCollectionEquality()
                .equals(other._teamMembers, _teamMembers) &&
            (identical(other.teamInfo, teamInfo) ||
                other.teamInfo == teamInfo) &&
            const DeepCollectionEquality()
                .equals(other._contributions, _contributions) &&
            (identical(other.seasonInfo, seasonInfo) ||
                other.seasonInfo == seasonInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      teamId,
      classId,
      courseId,
      backgroundImage,
      stateImage,
      const DeepCollectionEquality().hash(_teamMembers),
      teamInfo,
      const DeepCollectionEquality().hash(_contributions),
      seasonInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpaceHomeGroupDataCopyWith<_$_SpaceHomeGroupData> get copyWith =>
      __$$_SpaceHomeGroupDataCopyWithImpl<_$_SpaceHomeGroupData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpaceHomeGroupDataToJson(
      this,
    );
  }
}

abstract class _SpaceHomeGroupData implements SpaceHomeGroupData {
  const factory _SpaceHomeGroupData(
      {final int? teamId,
      final int? classId,
      final int? courseId,
      final String? backgroundImage,
      final String? stateImage,
      final List<TeamMember>? teamMembers,
      final TeamInfo? teamInfo,
      final List<Contribution>? contributions,
      final SeasonInfo? seasonInfo}) = _$_SpaceHomeGroupData;

  factory _SpaceHomeGroupData.fromJson(Map<String, dynamic> json) =
      _$_SpaceHomeGroupData.fromJson;

  @override
  int? get teamId;
  @override
  int? get classId;
  @override
  int? get courseId;
  @override
  String? get backgroundImage;
  @override
  String? get stateImage;
  @override
  List<TeamMember>? get teamMembers;
  @override
  TeamInfo? get teamInfo;
  @override
  List<Contribution>? get contributions;
  @override
  SeasonInfo? get seasonInfo;
  @override
  @JsonKey(ignore: true)
  _$$_SpaceHomeGroupDataCopyWith<_$_SpaceHomeGroupData> get copyWith =>
      throw _privateConstructorUsedError;
}

Contribution _$ContributionFromJson(Map<String, dynamic> json) {
  return _Contribution.fromJson(json);
}

/// @nodoc
mixin _$Contribution {
  int? get order => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;
  String? get userName => throw _privateConstructorUsedError;
  String? get userAvatar => throw _privateConstructorUsedError;
  List<SeasonTitle>? get seasonTitle => throw _privateConstructorUsedError;
  int? get currentTodayIntegral => throw _privateConstructorUsedError;
  int? get currentWeekIntegral => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContributionCopyWith<Contribution> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContributionCopyWith<$Res> {
  factory $ContributionCopyWith(
          Contribution value, $Res Function(Contribution) then) =
      _$ContributionCopyWithImpl<$Res, Contribution>;
  @useResult
  $Res call(
      {int? order,
      int? userId,
      String? userName,
      String? userAvatar,
      List<SeasonTitle>? seasonTitle,
      int? currentTodayIntegral,
      int? currentWeekIntegral});
}

/// @nodoc
class _$ContributionCopyWithImpl<$Res, $Val extends Contribution>
    implements $ContributionCopyWith<$Res> {
  _$ContributionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? order = freezed,
    Object? userId = freezed,
    Object? userName = freezed,
    Object? userAvatar = freezed,
    Object? seasonTitle = freezed,
    Object? currentTodayIntegral = freezed,
    Object? currentWeekIntegral = freezed,
  }) {
    return _then(_value.copyWith(
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
      userAvatar: freezed == userAvatar
          ? _value.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      seasonTitle: freezed == seasonTitle
          ? _value.seasonTitle
          : seasonTitle // ignore: cast_nullable_to_non_nullable
              as List<SeasonTitle>?,
      currentTodayIntegral: freezed == currentTodayIntegral
          ? _value.currentTodayIntegral
          : currentTodayIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
      currentWeekIntegral: freezed == currentWeekIntegral
          ? _value.currentWeekIntegral
          : currentWeekIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ContributionCopyWith<$Res>
    implements $ContributionCopyWith<$Res> {
  factory _$$_ContributionCopyWith(
          _$_Contribution value, $Res Function(_$_Contribution) then) =
      __$$_ContributionCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? order,
      int? userId,
      String? userName,
      String? userAvatar,
      List<SeasonTitle>? seasonTitle,
      int? currentTodayIntegral,
      int? currentWeekIntegral});
}

/// @nodoc
class __$$_ContributionCopyWithImpl<$Res>
    extends _$ContributionCopyWithImpl<$Res, _$_Contribution>
    implements _$$_ContributionCopyWith<$Res> {
  __$$_ContributionCopyWithImpl(
      _$_Contribution _value, $Res Function(_$_Contribution) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? order = freezed,
    Object? userId = freezed,
    Object? userName = freezed,
    Object? userAvatar = freezed,
    Object? seasonTitle = freezed,
    Object? currentTodayIntegral = freezed,
    Object? currentWeekIntegral = freezed,
  }) {
    return _then(_$_Contribution(
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
      userAvatar: freezed == userAvatar
          ? _value.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      seasonTitle: freezed == seasonTitle
          ? _value._seasonTitle
          : seasonTitle // ignore: cast_nullable_to_non_nullable
              as List<SeasonTitle>?,
      currentTodayIntegral: freezed == currentTodayIntegral
          ? _value.currentTodayIntegral
          : currentTodayIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
      currentWeekIntegral: freezed == currentWeekIntegral
          ? _value.currentWeekIntegral
          : currentWeekIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Contribution implements _Contribution {
  const _$_Contribution(
      {this.order,
      this.userId,
      this.userName,
      this.userAvatar,
      final List<SeasonTitle>? seasonTitle,
      this.currentTodayIntegral,
      this.currentWeekIntegral})
      : _seasonTitle = seasonTitle;

  factory _$_Contribution.fromJson(Map<String, dynamic> json) =>
      _$$_ContributionFromJson(json);

  @override
  final int? order;
  @override
  final int? userId;
  @override
  final String? userName;
  @override
  final String? userAvatar;
  final List<SeasonTitle>? _seasonTitle;
  @override
  List<SeasonTitle>? get seasonTitle {
    final value = _seasonTitle;
    if (value == null) return null;
    if (_seasonTitle is EqualUnmodifiableListView) return _seasonTitle;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? currentTodayIntegral;
  @override
  final int? currentWeekIntegral;

  @override
  String toString() {
    return 'Contribution(order: $order, userId: $userId, userName: $userName, userAvatar: $userAvatar, seasonTitle: $seasonTitle, currentTodayIntegral: $currentTodayIntegral, currentWeekIntegral: $currentWeekIntegral)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Contribution &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            const DeepCollectionEquality()
                .equals(other._seasonTitle, _seasonTitle) &&
            (identical(other.currentTodayIntegral, currentTodayIntegral) ||
                other.currentTodayIntegral == currentTodayIntegral) &&
            (identical(other.currentWeekIntegral, currentWeekIntegral) ||
                other.currentWeekIntegral == currentWeekIntegral));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      order,
      userId,
      userName,
      userAvatar,
      const DeepCollectionEquality().hash(_seasonTitle),
      currentTodayIntegral,
      currentWeekIntegral);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ContributionCopyWith<_$_Contribution> get copyWith =>
      __$$_ContributionCopyWithImpl<_$_Contribution>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ContributionToJson(
      this,
    );
  }
}

abstract class _Contribution implements Contribution {
  const factory _Contribution(
      {final int? order,
      final int? userId,
      final String? userName,
      final String? userAvatar,
      final List<SeasonTitle>? seasonTitle,
      final int? currentTodayIntegral,
      final int? currentWeekIntegral}) = _$_Contribution;

  factory _Contribution.fromJson(Map<String, dynamic> json) =
      _$_Contribution.fromJson;

  @override
  int? get order;
  @override
  int? get userId;
  @override
  String? get userName;
  @override
  String? get userAvatar;
  @override
  List<SeasonTitle>? get seasonTitle;
  @override
  int? get currentTodayIntegral;
  @override
  int? get currentWeekIntegral;
  @override
  @JsonKey(ignore: true)
  _$$_ContributionCopyWith<_$_Contribution> get copyWith =>
      throw _privateConstructorUsedError;
}

SeasonTitle _$SeasonTitleFromJson(Map<String, dynamic> json) {
  return _SeasonTitle.fromJson(json);
}

/// @nodoc
mixin _$SeasonTitle {
  String? get titlePicUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SeasonTitleCopyWith<SeasonTitle> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SeasonTitleCopyWith<$Res> {
  factory $SeasonTitleCopyWith(
          SeasonTitle value, $Res Function(SeasonTitle) then) =
      _$SeasonTitleCopyWithImpl<$Res, SeasonTitle>;
  @useResult
  $Res call({String? titlePicUrl});
}

/// @nodoc
class _$SeasonTitleCopyWithImpl<$Res, $Val extends SeasonTitle>
    implements $SeasonTitleCopyWith<$Res> {
  _$SeasonTitleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? titlePicUrl = freezed,
  }) {
    return _then(_value.copyWith(
      titlePicUrl: freezed == titlePicUrl
          ? _value.titlePicUrl
          : titlePicUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SeasonTitleCopyWith<$Res>
    implements $SeasonTitleCopyWith<$Res> {
  factory _$$_SeasonTitleCopyWith(
          _$_SeasonTitle value, $Res Function(_$_SeasonTitle) then) =
      __$$_SeasonTitleCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? titlePicUrl});
}

/// @nodoc
class __$$_SeasonTitleCopyWithImpl<$Res>
    extends _$SeasonTitleCopyWithImpl<$Res, _$_SeasonTitle>
    implements _$$_SeasonTitleCopyWith<$Res> {
  __$$_SeasonTitleCopyWithImpl(
      _$_SeasonTitle _value, $Res Function(_$_SeasonTitle) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? titlePicUrl = freezed,
  }) {
    return _then(_$_SeasonTitle(
      titlePicUrl: freezed == titlePicUrl
          ? _value.titlePicUrl
          : titlePicUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SeasonTitle implements _SeasonTitle {
  const _$_SeasonTitle({this.titlePicUrl});

  factory _$_SeasonTitle.fromJson(Map<String, dynamic> json) =>
      _$$_SeasonTitleFromJson(json);

  @override
  final String? titlePicUrl;

  @override
  String toString() {
    return 'SeasonTitle(titlePicUrl: $titlePicUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SeasonTitle &&
            (identical(other.titlePicUrl, titlePicUrl) ||
                other.titlePicUrl == titlePicUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, titlePicUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SeasonTitleCopyWith<_$_SeasonTitle> get copyWith =>
      __$$_SeasonTitleCopyWithImpl<_$_SeasonTitle>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SeasonTitleToJson(
      this,
    );
  }
}

abstract class _SeasonTitle implements SeasonTitle {
  const factory _SeasonTitle({final String? titlePicUrl}) = _$_SeasonTitle;

  factory _SeasonTitle.fromJson(Map<String, dynamic> json) =
      _$_SeasonTitle.fromJson;

  @override
  String? get titlePicUrl;
  @override
  @JsonKey(ignore: true)
  _$$_SeasonTitleCopyWith<_$_SeasonTitle> get copyWith =>
      throw _privateConstructorUsedError;
}

SeasonInfo _$SeasonInfoFromJson(Map<String, dynamic> json) {
  return _SeasonInfo.fromJson(json);
}

/// @nodoc
mixin _$SeasonInfo {
  int? get seasonId => throw _privateConstructorUsedError;
  int? get startTime => throw _privateConstructorUsedError;
  int? get endTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SeasonInfoCopyWith<SeasonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SeasonInfoCopyWith<$Res> {
  factory $SeasonInfoCopyWith(
          SeasonInfo value, $Res Function(SeasonInfo) then) =
      _$SeasonInfoCopyWithImpl<$Res, SeasonInfo>;
  @useResult
  $Res call({int? seasonId, int? startTime, int? endTime});
}

/// @nodoc
class _$SeasonInfoCopyWithImpl<$Res, $Val extends SeasonInfo>
    implements $SeasonInfoCopyWith<$Res> {
  _$SeasonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? seasonId = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_value.copyWith(
      seasonId: freezed == seasonId
          ? _value.seasonId
          : seasonId // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SeasonInfoCopyWith<$Res>
    implements $SeasonInfoCopyWith<$Res> {
  factory _$$_SeasonInfoCopyWith(
          _$_SeasonInfo value, $Res Function(_$_SeasonInfo) then) =
      __$$_SeasonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? seasonId, int? startTime, int? endTime});
}

/// @nodoc
class __$$_SeasonInfoCopyWithImpl<$Res>
    extends _$SeasonInfoCopyWithImpl<$Res, _$_SeasonInfo>
    implements _$$_SeasonInfoCopyWith<$Res> {
  __$$_SeasonInfoCopyWithImpl(
      _$_SeasonInfo _value, $Res Function(_$_SeasonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? seasonId = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_$_SeasonInfo(
      seasonId: freezed == seasonId
          ? _value.seasonId
          : seasonId // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SeasonInfo implements _SeasonInfo {
  const _$_SeasonInfo({this.seasonId, this.startTime, this.endTime});

  factory _$_SeasonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_SeasonInfoFromJson(json);

  @override
  final int? seasonId;
  @override
  final int? startTime;
  @override
  final int? endTime;

  @override
  String toString() {
    return 'SeasonInfo(seasonId: $seasonId, startTime: $startTime, endTime: $endTime)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SeasonInfo &&
            (identical(other.seasonId, seasonId) ||
                other.seasonId == seasonId) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, seasonId, startTime, endTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SeasonInfoCopyWith<_$_SeasonInfo> get copyWith =>
      __$$_SeasonInfoCopyWithImpl<_$_SeasonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SeasonInfoToJson(
      this,
    );
  }
}

abstract class _SeasonInfo implements SeasonInfo {
  const factory _SeasonInfo(
      {final int? seasonId,
      final int? startTime,
      final int? endTime}) = _$_SeasonInfo;

  factory _SeasonInfo.fromJson(Map<String, dynamic> json) =
      _$_SeasonInfo.fromJson;

  @override
  int? get seasonId;
  @override
  int? get startTime;
  @override
  int? get endTime;
  @override
  @JsonKey(ignore: true)
  _$$_SeasonInfoCopyWith<_$_SeasonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamInfo _$TeamInfoFromJson(Map<String, dynamic> json) {
  return _TeamInfo.fromJson(json);
}

/// @nodoc
mixin _$TeamInfo {
  int? get currentIntegral => throw _privateConstructorUsedError;
  int? get totalIntegral => throw _privateConstructorUsedError;
  List<GroupUpgradeInfo>? get upgradeInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamInfoCopyWith<TeamInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamInfoCopyWith<$Res> {
  factory $TeamInfoCopyWith(TeamInfo value, $Res Function(TeamInfo) then) =
      _$TeamInfoCopyWithImpl<$Res, TeamInfo>;
  @useResult
  $Res call(
      {int? currentIntegral,
      int? totalIntegral,
      List<GroupUpgradeInfo>? upgradeInfo});
}

/// @nodoc
class _$TeamInfoCopyWithImpl<$Res, $Val extends TeamInfo>
    implements $TeamInfoCopyWith<$Res> {
  _$TeamInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentIntegral = freezed,
    Object? totalIntegral = freezed,
    Object? upgradeInfo = freezed,
  }) {
    return _then(_value.copyWith(
      currentIntegral: freezed == currentIntegral
          ? _value.currentIntegral
          : currentIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
      totalIntegral: freezed == totalIntegral
          ? _value.totalIntegral
          : totalIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
      upgradeInfo: freezed == upgradeInfo
          ? _value.upgradeInfo
          : upgradeInfo // ignore: cast_nullable_to_non_nullable
              as List<GroupUpgradeInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamInfoCopyWith<$Res> implements $TeamInfoCopyWith<$Res> {
  factory _$$_TeamInfoCopyWith(
          _$_TeamInfo value, $Res Function(_$_TeamInfo) then) =
      __$$_TeamInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? currentIntegral,
      int? totalIntegral,
      List<GroupUpgradeInfo>? upgradeInfo});
}

/// @nodoc
class __$$_TeamInfoCopyWithImpl<$Res>
    extends _$TeamInfoCopyWithImpl<$Res, _$_TeamInfo>
    implements _$$_TeamInfoCopyWith<$Res> {
  __$$_TeamInfoCopyWithImpl(
      _$_TeamInfo _value, $Res Function(_$_TeamInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentIntegral = freezed,
    Object? totalIntegral = freezed,
    Object? upgradeInfo = freezed,
  }) {
    return _then(_$_TeamInfo(
      currentIntegral: freezed == currentIntegral
          ? _value.currentIntegral
          : currentIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
      totalIntegral: freezed == totalIntegral
          ? _value.totalIntegral
          : totalIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
      upgradeInfo: freezed == upgradeInfo
          ? _value._upgradeInfo
          : upgradeInfo // ignore: cast_nullable_to_non_nullable
              as List<GroupUpgradeInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamInfo implements _TeamInfo {
  const _$_TeamInfo(
      {this.currentIntegral,
      this.totalIntegral,
      final List<GroupUpgradeInfo>? upgradeInfo})
      : _upgradeInfo = upgradeInfo;

  factory _$_TeamInfo.fromJson(Map<String, dynamic> json) =>
      _$$_TeamInfoFromJson(json);

  @override
  final int? currentIntegral;
  @override
  final int? totalIntegral;
  final List<GroupUpgradeInfo>? _upgradeInfo;
  @override
  List<GroupUpgradeInfo>? get upgradeInfo {
    final value = _upgradeInfo;
    if (value == null) return null;
    if (_upgradeInfo is EqualUnmodifiableListView) return _upgradeInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TeamInfo(currentIntegral: $currentIntegral, totalIntegral: $totalIntegral, upgradeInfo: $upgradeInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamInfo &&
            (identical(other.currentIntegral, currentIntegral) ||
                other.currentIntegral == currentIntegral) &&
            (identical(other.totalIntegral, totalIntegral) ||
                other.totalIntegral == totalIntegral) &&
            const DeepCollectionEquality()
                .equals(other._upgradeInfo, _upgradeInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, currentIntegral, totalIntegral,
      const DeepCollectionEquality().hash(_upgradeInfo));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamInfoCopyWith<_$_TeamInfo> get copyWith =>
      __$$_TeamInfoCopyWithImpl<_$_TeamInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamInfoToJson(
      this,
    );
  }
}

abstract class _TeamInfo implements TeamInfo {
  const factory _TeamInfo(
      {final int? currentIntegral,
      final int? totalIntegral,
      final List<GroupUpgradeInfo>? upgradeInfo}) = _$_TeamInfo;

  factory _TeamInfo.fromJson(Map<String, dynamic> json) = _$_TeamInfo.fromJson;

  @override
  int? get currentIntegral;
  @override
  int? get totalIntegral;
  @override
  List<GroupUpgradeInfo>? get upgradeInfo;
  @override
  @JsonKey(ignore: true)
  _$$_TeamInfoCopyWith<_$_TeamInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

GroupUpgradeInfo _$GroupUpgradeInfoFromJson(Map<String, dynamic> json) {
  return _GroupUpgradeInfo.fromJson(json);
}

/// @nodoc
mixin _$GroupUpgradeInfo {
  int? get upgradeTargetIntegral => throw _privateConstructorUsedError;
  String? get upgradeLevelDesc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GroupUpgradeInfoCopyWith<GroupUpgradeInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GroupUpgradeInfoCopyWith<$Res> {
  factory $GroupUpgradeInfoCopyWith(
          GroupUpgradeInfo value, $Res Function(GroupUpgradeInfo) then) =
      _$GroupUpgradeInfoCopyWithImpl<$Res, GroupUpgradeInfo>;
  @useResult
  $Res call({int? upgradeTargetIntegral, String? upgradeLevelDesc});
}

/// @nodoc
class _$GroupUpgradeInfoCopyWithImpl<$Res, $Val extends GroupUpgradeInfo>
    implements $GroupUpgradeInfoCopyWith<$Res> {
  _$GroupUpgradeInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? upgradeTargetIntegral = freezed,
    Object? upgradeLevelDesc = freezed,
  }) {
    return _then(_value.copyWith(
      upgradeTargetIntegral: freezed == upgradeTargetIntegral
          ? _value.upgradeTargetIntegral
          : upgradeTargetIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
      upgradeLevelDesc: freezed == upgradeLevelDesc
          ? _value.upgradeLevelDesc
          : upgradeLevelDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GroupUpgradeInfoCopyWith<$Res>
    implements $GroupUpgradeInfoCopyWith<$Res> {
  factory _$$_GroupUpgradeInfoCopyWith(
          _$_GroupUpgradeInfo value, $Res Function(_$_GroupUpgradeInfo) then) =
      __$$_GroupUpgradeInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? upgradeTargetIntegral, String? upgradeLevelDesc});
}

/// @nodoc
class __$$_GroupUpgradeInfoCopyWithImpl<$Res>
    extends _$GroupUpgradeInfoCopyWithImpl<$Res, _$_GroupUpgradeInfo>
    implements _$$_GroupUpgradeInfoCopyWith<$Res> {
  __$$_GroupUpgradeInfoCopyWithImpl(
      _$_GroupUpgradeInfo _value, $Res Function(_$_GroupUpgradeInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? upgradeTargetIntegral = freezed,
    Object? upgradeLevelDesc = freezed,
  }) {
    return _then(_$_GroupUpgradeInfo(
      upgradeTargetIntegral: freezed == upgradeTargetIntegral
          ? _value.upgradeTargetIntegral
          : upgradeTargetIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
      upgradeLevelDesc: freezed == upgradeLevelDesc
          ? _value.upgradeLevelDesc
          : upgradeLevelDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GroupUpgradeInfo implements _GroupUpgradeInfo {
  const _$_GroupUpgradeInfo(
      {this.upgradeTargetIntegral, this.upgradeLevelDesc});

  factory _$_GroupUpgradeInfo.fromJson(Map<String, dynamic> json) =>
      _$$_GroupUpgradeInfoFromJson(json);

  @override
  final int? upgradeTargetIntegral;
  @override
  final String? upgradeLevelDesc;

  @override
  String toString() {
    return 'GroupUpgradeInfo(upgradeTargetIntegral: $upgradeTargetIntegral, upgradeLevelDesc: $upgradeLevelDesc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GroupUpgradeInfo &&
            (identical(other.upgradeTargetIntegral, upgradeTargetIntegral) ||
                other.upgradeTargetIntegral == upgradeTargetIntegral) &&
            (identical(other.upgradeLevelDesc, upgradeLevelDesc) ||
                other.upgradeLevelDesc == upgradeLevelDesc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, upgradeTargetIntegral, upgradeLevelDesc);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GroupUpgradeInfoCopyWith<_$_GroupUpgradeInfo> get copyWith =>
      __$$_GroupUpgradeInfoCopyWithImpl<_$_GroupUpgradeInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GroupUpgradeInfoToJson(
      this,
    );
  }
}

abstract class _GroupUpgradeInfo implements GroupUpgradeInfo {
  const factory _GroupUpgradeInfo(
      {final int? upgradeTargetIntegral,
      final String? upgradeLevelDesc}) = _$_GroupUpgradeInfo;

  factory _GroupUpgradeInfo.fromJson(Map<String, dynamic> json) =
      _$_GroupUpgradeInfo.fromJson;

  @override
  int? get upgradeTargetIntegral;
  @override
  String? get upgradeLevelDesc;
  @override
  @JsonKey(ignore: true)
  _$$_GroupUpgradeInfoCopyWith<_$_GroupUpgradeInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamMember _$TeamMemberFromJson(Map<String, dynamic> json) {
  return _TeamMember.fromJson(json);
}

/// @nodoc
mixin _$TeamMember {
  int? get userId => throw _privateConstructorUsedError;
  String? get userName => throw _privateConstructorUsedError;
  int? get userType => throw _privateConstructorUsedError; //是否是自己
  List<Skin>? get dressList => throw _privateConstructorUsedError;
  int? get userStatus => throw _privateConstructorUsedError;
  StudyBtn? get studyBtn => throw _privateConstructorUsedError;
  List<UserContribution>? get userContribution =>
      throw _privateConstructorUsedError;
  LearningInfo? get learningInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamMemberCopyWith<TeamMember> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamMemberCopyWith<$Res> {
  factory $TeamMemberCopyWith(
          TeamMember value, $Res Function(TeamMember) then) =
      _$TeamMemberCopyWithImpl<$Res, TeamMember>;
  @useResult
  $Res call(
      {int? userId,
      String? userName,
      int? userType,
      List<Skin>? dressList,
      int? userStatus,
      StudyBtn? studyBtn,
      List<UserContribution>? userContribution,
      LearningInfo? learningInfo});

  $StudyBtnCopyWith<$Res>? get studyBtn;
  $LearningInfoCopyWith<$Res>? get learningInfo;
}

/// @nodoc
class _$TeamMemberCopyWithImpl<$Res, $Val extends TeamMember>
    implements $TeamMemberCopyWith<$Res> {
  _$TeamMemberCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? userName = freezed,
    Object? userType = freezed,
    Object? dressList = freezed,
    Object? userStatus = freezed,
    Object? studyBtn = freezed,
    Object? userContribution = freezed,
    Object? learningInfo = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
      userType: freezed == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as int?,
      dressList: freezed == dressList
          ? _value.dressList
          : dressList // ignore: cast_nullable_to_non_nullable
              as List<Skin>?,
      userStatus: freezed == userStatus
          ? _value.userStatus
          : userStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      studyBtn: freezed == studyBtn
          ? _value.studyBtn
          : studyBtn // ignore: cast_nullable_to_non_nullable
              as StudyBtn?,
      userContribution: freezed == userContribution
          ? _value.userContribution
          : userContribution // ignore: cast_nullable_to_non_nullable
              as List<UserContribution>?,
      learningInfo: freezed == learningInfo
          ? _value.learningInfo
          : learningInfo // ignore: cast_nullable_to_non_nullable
              as LearningInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $StudyBtnCopyWith<$Res>? get studyBtn {
    if (_value.studyBtn == null) {
      return null;
    }

    return $StudyBtnCopyWith<$Res>(_value.studyBtn!, (value) {
      return _then(_value.copyWith(studyBtn: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LearningInfoCopyWith<$Res>? get learningInfo {
    if (_value.learningInfo == null) {
      return null;
    }

    return $LearningInfoCopyWith<$Res>(_value.learningInfo!, (value) {
      return _then(_value.copyWith(learningInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_TeamMemberCopyWith<$Res>
    implements $TeamMemberCopyWith<$Res> {
  factory _$$_TeamMemberCopyWith(
          _$_TeamMember value, $Res Function(_$_TeamMember) then) =
      __$$_TeamMemberCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? userId,
      String? userName,
      int? userType,
      List<Skin>? dressList,
      int? userStatus,
      StudyBtn? studyBtn,
      List<UserContribution>? userContribution,
      LearningInfo? learningInfo});

  @override
  $StudyBtnCopyWith<$Res>? get studyBtn;
  @override
  $LearningInfoCopyWith<$Res>? get learningInfo;
}

/// @nodoc
class __$$_TeamMemberCopyWithImpl<$Res>
    extends _$TeamMemberCopyWithImpl<$Res, _$_TeamMember>
    implements _$$_TeamMemberCopyWith<$Res> {
  __$$_TeamMemberCopyWithImpl(
      _$_TeamMember _value, $Res Function(_$_TeamMember) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? userName = freezed,
    Object? userType = freezed,
    Object? dressList = freezed,
    Object? userStatus = freezed,
    Object? studyBtn = freezed,
    Object? userContribution = freezed,
    Object? learningInfo = freezed,
  }) {
    return _then(_$_TeamMember(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      userName: freezed == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String?,
      userType: freezed == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as int?,
      dressList: freezed == dressList
          ? _value._dressList
          : dressList // ignore: cast_nullable_to_non_nullable
              as List<Skin>?,
      userStatus: freezed == userStatus
          ? _value.userStatus
          : userStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      studyBtn: freezed == studyBtn
          ? _value.studyBtn
          : studyBtn // ignore: cast_nullable_to_non_nullable
              as StudyBtn?,
      userContribution: freezed == userContribution
          ? _value._userContribution
          : userContribution // ignore: cast_nullable_to_non_nullable
              as List<UserContribution>?,
      learningInfo: freezed == learningInfo
          ? _value.learningInfo
          : learningInfo // ignore: cast_nullable_to_non_nullable
              as LearningInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamMember implements _TeamMember {
  const _$_TeamMember(
      {this.userId,
      this.userName,
      this.userType,
      final List<Skin>? dressList,
      this.userStatus,
      this.studyBtn,
      final List<UserContribution>? userContribution,
      this.learningInfo})
      : _dressList = dressList,
        _userContribution = userContribution;

  factory _$_TeamMember.fromJson(Map<String, dynamic> json) =>
      _$$_TeamMemberFromJson(json);

  @override
  final int? userId;
  @override
  final String? userName;
  @override
  final int? userType;
//是否是自己
  final List<Skin>? _dressList;
//是否是自己
  @override
  List<Skin>? get dressList {
    final value = _dressList;
    if (value == null) return null;
    if (_dressList is EqualUnmodifiableListView) return _dressList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? userStatus;
  @override
  final StudyBtn? studyBtn;
  final List<UserContribution>? _userContribution;
  @override
  List<UserContribution>? get userContribution {
    final value = _userContribution;
    if (value == null) return null;
    if (_userContribution is EqualUnmodifiableListView)
      return _userContribution;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final LearningInfo? learningInfo;

  @override
  String toString() {
    return 'TeamMember(userId: $userId, userName: $userName, userType: $userType, dressList: $dressList, userStatus: $userStatus, studyBtn: $studyBtn, userContribution: $userContribution, learningInfo: $learningInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamMember &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            const DeepCollectionEquality()
                .equals(other._dressList, _dressList) &&
            (identical(other.userStatus, userStatus) ||
                other.userStatus == userStatus) &&
            (identical(other.studyBtn, studyBtn) ||
                other.studyBtn == studyBtn) &&
            const DeepCollectionEquality()
                .equals(other._userContribution, _userContribution) &&
            (identical(other.learningInfo, learningInfo) ||
                other.learningInfo == learningInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      userName,
      userType,
      const DeepCollectionEquality().hash(_dressList),
      userStatus,
      studyBtn,
      const DeepCollectionEquality().hash(_userContribution),
      learningInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamMemberCopyWith<_$_TeamMember> get copyWith =>
      __$$_TeamMemberCopyWithImpl<_$_TeamMember>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamMemberToJson(
      this,
    );
  }
}

abstract class _TeamMember implements TeamMember {
  const factory _TeamMember(
      {final int? userId,
      final String? userName,
      final int? userType,
      final List<Skin>? dressList,
      final int? userStatus,
      final StudyBtn? studyBtn,
      final List<UserContribution>? userContribution,
      final LearningInfo? learningInfo}) = _$_TeamMember;

  factory _TeamMember.fromJson(Map<String, dynamic> json) =
      _$_TeamMember.fromJson;

  @override
  int? get userId;
  @override
  String? get userName;
  @override
  int? get userType;
  @override //是否是自己
  List<Skin>? get dressList;
  @override
  int? get userStatus;
  @override
  StudyBtn? get studyBtn;
  @override
  List<UserContribution>? get userContribution;
  @override
  LearningInfo? get learningInfo;
  @override
  @JsonKey(ignore: true)
  _$$_TeamMemberCopyWith<_$_TeamMember> get copyWith =>
      throw _privateConstructorUsedError;
}

StudyBtn _$StudyBtnFromJson(Map<String, dynamic> json) {
  return _StudyBtn.fromJson(json);
}

/// @nodoc
mixin _$StudyBtn {
  String? get btnName => throw _privateConstructorUsedError;
  String? get router => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StudyBtnCopyWith<StudyBtn> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StudyBtnCopyWith<$Res> {
  factory $StudyBtnCopyWith(StudyBtn value, $Res Function(StudyBtn) then) =
      _$StudyBtnCopyWithImpl<$Res, StudyBtn>;
  @useResult
  $Res call({String? btnName, String? router});
}

/// @nodoc
class _$StudyBtnCopyWithImpl<$Res, $Val extends StudyBtn>
    implements $StudyBtnCopyWith<$Res> {
  _$StudyBtnCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? btnName = freezed,
    Object? router = freezed,
  }) {
    return _then(_value.copyWith(
      btnName: freezed == btnName
          ? _value.btnName
          : btnName // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_StudyBtnCopyWith<$Res> implements $StudyBtnCopyWith<$Res> {
  factory _$$_StudyBtnCopyWith(
          _$_StudyBtn value, $Res Function(_$_StudyBtn) then) =
      __$$_StudyBtnCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? btnName, String? router});
}

/// @nodoc
class __$$_StudyBtnCopyWithImpl<$Res>
    extends _$StudyBtnCopyWithImpl<$Res, _$_StudyBtn>
    implements _$$_StudyBtnCopyWith<$Res> {
  __$$_StudyBtnCopyWithImpl(
      _$_StudyBtn _value, $Res Function(_$_StudyBtn) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? btnName = freezed,
    Object? router = freezed,
  }) {
    return _then(_$_StudyBtn(
      btnName: freezed == btnName
          ? _value.btnName
          : btnName // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_StudyBtn implements _StudyBtn {
  const _$_StudyBtn({this.btnName, this.router});

  factory _$_StudyBtn.fromJson(Map<String, dynamic> json) =>
      _$$_StudyBtnFromJson(json);

  @override
  final String? btnName;
  @override
  final String? router;

  @override
  String toString() {
    return 'StudyBtn(btnName: $btnName, router: $router)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_StudyBtn &&
            (identical(other.btnName, btnName) || other.btnName == btnName) &&
            (identical(other.router, router) || other.router == router));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, btnName, router);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_StudyBtnCopyWith<_$_StudyBtn> get copyWith =>
      __$$_StudyBtnCopyWithImpl<_$_StudyBtn>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_StudyBtnToJson(
      this,
    );
  }
}

abstract class _StudyBtn implements StudyBtn {
  const factory _StudyBtn({final String? btnName, final String? router}) =
      _$_StudyBtn;

  factory _StudyBtn.fromJson(Map<String, dynamic> json) = _$_StudyBtn.fromJson;

  @override
  String? get btnName;
  @override
  String? get router;
  @override
  @JsonKey(ignore: true)
  _$$_StudyBtnCopyWith<_$_StudyBtn> get copyWith =>
      throw _privateConstructorUsedError;
}

LearningInfo _$LearningInfoFromJson(Map<String, dynamic> json) {
  return _LearningInfo.fromJson(json);
}

/// @nodoc
mixin _$LearningInfo {
  String? get icon => throw _privateConstructorUsedError;
  int? get continuousLearningDays => throw _privateConstructorUsedError;
  int? get todayIntegral => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LearningInfoCopyWith<LearningInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LearningInfoCopyWith<$Res> {
  factory $LearningInfoCopyWith(
          LearningInfo value, $Res Function(LearningInfo) then) =
      _$LearningInfoCopyWithImpl<$Res, LearningInfo>;
  @useResult
  $Res call({String? icon, int? continuousLearningDays, int? todayIntegral});
}

/// @nodoc
class _$LearningInfoCopyWithImpl<$Res, $Val extends LearningInfo>
    implements $LearningInfoCopyWith<$Res> {
  _$LearningInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? continuousLearningDays = freezed,
    Object? todayIntegral = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      continuousLearningDays: freezed == continuousLearningDays
          ? _value.continuousLearningDays
          : continuousLearningDays // ignore: cast_nullable_to_non_nullable
              as int?,
      todayIntegral: freezed == todayIntegral
          ? _value.todayIntegral
          : todayIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LearningInfoCopyWith<$Res>
    implements $LearningInfoCopyWith<$Res> {
  factory _$$_LearningInfoCopyWith(
          _$_LearningInfo value, $Res Function(_$_LearningInfo) then) =
      __$$_LearningInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? icon, int? continuousLearningDays, int? todayIntegral});
}

/// @nodoc
class __$$_LearningInfoCopyWithImpl<$Res>
    extends _$LearningInfoCopyWithImpl<$Res, _$_LearningInfo>
    implements _$$_LearningInfoCopyWith<$Res> {
  __$$_LearningInfoCopyWithImpl(
      _$_LearningInfo _value, $Res Function(_$_LearningInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? continuousLearningDays = freezed,
    Object? todayIntegral = freezed,
  }) {
    return _then(_$_LearningInfo(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      continuousLearningDays: freezed == continuousLearningDays
          ? _value.continuousLearningDays
          : continuousLearningDays // ignore: cast_nullable_to_non_nullable
              as int?,
      todayIntegral: freezed == todayIntegral
          ? _value.todayIntegral
          : todayIntegral // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LearningInfo implements _LearningInfo {
  const _$_LearningInfo(
      {this.icon, this.continuousLearningDays, this.todayIntegral});

  factory _$_LearningInfo.fromJson(Map<String, dynamic> json) =>
      _$$_LearningInfoFromJson(json);

  @override
  final String? icon;
  @override
  final int? continuousLearningDays;
  @override
  final int? todayIntegral;

  @override
  String toString() {
    return 'LearningInfo(icon: $icon, continuousLearningDays: $continuousLearningDays, todayIntegral: $todayIntegral)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LearningInfo &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.continuousLearningDays, continuousLearningDays) ||
                other.continuousLearningDays == continuousLearningDays) &&
            (identical(other.todayIntegral, todayIntegral) ||
                other.todayIntegral == todayIntegral));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, icon, continuousLearningDays, todayIntegral);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LearningInfoCopyWith<_$_LearningInfo> get copyWith =>
      __$$_LearningInfoCopyWithImpl<_$_LearningInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LearningInfoToJson(
      this,
    );
  }
}

abstract class _LearningInfo implements LearningInfo {
  const factory _LearningInfo(
      {final String? icon,
      final int? continuousLearningDays,
      final int? todayIntegral}) = _$_LearningInfo;

  factory _LearningInfo.fromJson(Map<String, dynamic> json) =
      _$_LearningInfo.fromJson;

  @override
  String? get icon;
  @override
  int? get continuousLearningDays;
  @override
  int? get todayIntegral;
  @override
  @JsonKey(ignore: true)
  _$$_LearningInfoCopyWith<_$_LearningInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

Skin _$SkinFromJson(Map<String, dynamic> json) {
  return _Skin.fromJson(json);
}

/// @nodoc
mixin _$Skin {
  int? get dressUpCategoryId => throw _privateConstructorUsedError;
  String? get dressShowUrl => throw _privateConstructorUsedError;
  String? get resourcesZipUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SkinCopyWith<Skin> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkinCopyWith<$Res> {
  factory $SkinCopyWith(Skin value, $Res Function(Skin) then) =
      _$SkinCopyWithImpl<$Res, Skin>;
  @useResult
  $Res call(
      {int? dressUpCategoryId, String? dressShowUrl, String? resourcesZipUrl});
}

/// @nodoc
class _$SkinCopyWithImpl<$Res, $Val extends Skin>
    implements $SkinCopyWith<$Res> {
  _$SkinCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dressUpCategoryId = freezed,
    Object? dressShowUrl = freezed,
    Object? resourcesZipUrl = freezed,
  }) {
    return _then(_value.copyWith(
      dressUpCategoryId: freezed == dressUpCategoryId
          ? _value.dressUpCategoryId
          : dressUpCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressShowUrl: freezed == dressShowUrl
          ? _value.dressShowUrl
          : dressShowUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      resourcesZipUrl: freezed == resourcesZipUrl
          ? _value.resourcesZipUrl
          : resourcesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SkinCopyWith<$Res> implements $SkinCopyWith<$Res> {
  factory _$$_SkinCopyWith(_$_Skin value, $Res Function(_$_Skin) then) =
      __$$_SkinCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? dressUpCategoryId, String? dressShowUrl, String? resourcesZipUrl});
}

/// @nodoc
class __$$_SkinCopyWithImpl<$Res> extends _$SkinCopyWithImpl<$Res, _$_Skin>
    implements _$$_SkinCopyWith<$Res> {
  __$$_SkinCopyWithImpl(_$_Skin _value, $Res Function(_$_Skin) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dressUpCategoryId = freezed,
    Object? dressShowUrl = freezed,
    Object? resourcesZipUrl = freezed,
  }) {
    return _then(_$_Skin(
      dressUpCategoryId: freezed == dressUpCategoryId
          ? _value.dressUpCategoryId
          : dressUpCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressShowUrl: freezed == dressShowUrl
          ? _value.dressShowUrl
          : dressShowUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      resourcesZipUrl: freezed == resourcesZipUrl
          ? _value.resourcesZipUrl
          : resourcesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Skin implements _Skin {
  const _$_Skin(
      {this.dressUpCategoryId, this.dressShowUrl, this.resourcesZipUrl});

  factory _$_Skin.fromJson(Map<String, dynamic> json) => _$$_SkinFromJson(json);

  @override
  final int? dressUpCategoryId;
  @override
  final String? dressShowUrl;
  @override
  final String? resourcesZipUrl;

  @override
  String toString() {
    return 'Skin(dressUpCategoryId: $dressUpCategoryId, dressShowUrl: $dressShowUrl, resourcesZipUrl: $resourcesZipUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Skin &&
            (identical(other.dressUpCategoryId, dressUpCategoryId) ||
                other.dressUpCategoryId == dressUpCategoryId) &&
            (identical(other.dressShowUrl, dressShowUrl) ||
                other.dressShowUrl == dressShowUrl) &&
            (identical(other.resourcesZipUrl, resourcesZipUrl) ||
                other.resourcesZipUrl == resourcesZipUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, dressUpCategoryId, dressShowUrl, resourcesZipUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SkinCopyWith<_$_Skin> get copyWith =>
      __$$_SkinCopyWithImpl<_$_Skin>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SkinToJson(
      this,
    );
  }
}

abstract class _Skin implements Skin {
  const factory _Skin(
      {final int? dressUpCategoryId,
      final String? dressShowUrl,
      final String? resourcesZipUrl}) = _$_Skin;

  factory _Skin.fromJson(Map<String, dynamic> json) = _$_Skin.fromJson;

  @override
  int? get dressUpCategoryId;
  @override
  String? get dressShowUrl;
  @override
  String? get resourcesZipUrl;
  @override
  @JsonKey(ignore: true)
  _$$_SkinCopyWith<_$_Skin> get copyWith => throw _privateConstructorUsedError;
}

UserContribution _$UserContributionFromJson(Map<String, dynamic> json) {
  return _UserContribution.fromJson(json);
}

/// @nodoc
mixin _$UserContribution {
  int? get contributionType => throw _privateConstructorUsedError;
  String? get titlePicUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserContributionCopyWith<UserContribution> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserContributionCopyWith<$Res> {
  factory $UserContributionCopyWith(
          UserContribution value, $Res Function(UserContribution) then) =
      _$UserContributionCopyWithImpl<$Res, UserContribution>;
  @useResult
  $Res call({int? contributionType, String? titlePicUrl});
}

/// @nodoc
class _$UserContributionCopyWithImpl<$Res, $Val extends UserContribution>
    implements $UserContributionCopyWith<$Res> {
  _$UserContributionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contributionType = freezed,
    Object? titlePicUrl = freezed,
  }) {
    return _then(_value.copyWith(
      contributionType: freezed == contributionType
          ? _value.contributionType
          : contributionType // ignore: cast_nullable_to_non_nullable
              as int?,
      titlePicUrl: freezed == titlePicUrl
          ? _value.titlePicUrl
          : titlePicUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserContributionCopyWith<$Res>
    implements $UserContributionCopyWith<$Res> {
  factory _$$_UserContributionCopyWith(
          _$_UserContribution value, $Res Function(_$_UserContribution) then) =
      __$$_UserContributionCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? contributionType, String? titlePicUrl});
}

/// @nodoc
class __$$_UserContributionCopyWithImpl<$Res>
    extends _$UserContributionCopyWithImpl<$Res, _$_UserContribution>
    implements _$$_UserContributionCopyWith<$Res> {
  __$$_UserContributionCopyWithImpl(
      _$_UserContribution _value, $Res Function(_$_UserContribution) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contributionType = freezed,
    Object? titlePicUrl = freezed,
  }) {
    return _then(_$_UserContribution(
      freezed == contributionType
          ? _value.contributionType
          : contributionType // ignore: cast_nullable_to_non_nullable
              as int?,
      freezed == titlePicUrl
          ? _value.titlePicUrl
          : titlePicUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserContribution implements _UserContribution {
  const _$_UserContribution(this.contributionType, this.titlePicUrl);

  factory _$_UserContribution.fromJson(Map<String, dynamic> json) =>
      _$$_UserContributionFromJson(json);

  @override
  final int? contributionType;
  @override
  final String? titlePicUrl;

  @override
  String toString() {
    return 'UserContribution(contributionType: $contributionType, titlePicUrl: $titlePicUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserContribution &&
            (identical(other.contributionType, contributionType) ||
                other.contributionType == contributionType) &&
            (identical(other.titlePicUrl, titlePicUrl) ||
                other.titlePicUrl == titlePicUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, contributionType, titlePicUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserContributionCopyWith<_$_UserContribution> get copyWith =>
      __$$_UserContributionCopyWithImpl<_$_UserContribution>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserContributionToJson(
      this,
    );
  }
}

abstract class _UserContribution implements UserContribution {
  const factory _UserContribution(
          final int? contributionType, final String? titlePicUrl) =
      _$_UserContribution;

  factory _UserContribution.fromJson(Map<String, dynamic> json) =
      _$_UserContribution.fromJson;

  @override
  int? get contributionType;
  @override
  String? get titlePicUrl;
  @override
  @JsonKey(ignore: true)
  _$$_UserContributionCopyWith<_$_UserContribution> get copyWith =>
      throw _privateConstructorUsedError;
}
