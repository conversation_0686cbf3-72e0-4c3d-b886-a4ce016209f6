import 'package:jojo_flutter_base/base.dart';

part 'space_home_group_data.freezed.dart';
part 'space_home_group_data.g.dart';

@freezed
class SpaceHomeGroupData with _$SpaceHomeGroupData {
  const factory SpaceHomeGroupData({
    int? teamId,
    int? classId,
    int? courseId,
    String? backgroundImage,
    String? stateImage,
    List<TeamMember>? teamMembers,
    TeamInfo? teamInfo,
    List<Contribution>? contributions,
    SeasonInfo? seasonInfo,
  }) = _SpaceHomeGroupData;

  factory SpaceHomeGroupData.fromJson(Map<String, dynamic> json) =>
      _$SpaceHomeGroupDataFromJson(json);
}

@freezed
class Contribution with _$Contribution {
  const factory Contribution({
    int? order,
    int? userId,
    String? userName,
    String? userAvatar,
    List<SeasonTitle>? seasonTitle,
    int? currentTodayIntegral,
    int? currentWeekIntegral,
  }) = _Contribution;

  factory Contribution.fromJson(Map<String, dynamic> json) =>
      _$ContributionFromJson(json);
}

@freezed
class SeasonTitle with _$SeasonTitle {
  const factory SeasonTitle({
    String? titlePicUrl,
  }) = _SeasonTitle;

  factory SeasonTitle.fromJson(Map<String, dynamic> json) =>
      _$SeasonTitleFromJson(json);
}

@freezed
class SeasonInfo with _$SeasonInfo {
  const factory SeasonInfo({
    int? seasonId,
    int? startTime,
    int? endTime,
  }) = _SeasonInfo;

  factory SeasonInfo.fromJson(Map<String, dynamic> json) =>
      _$SeasonInfoFromJson(json);
}

@freezed
class TeamInfo with _$TeamInfo {
  const factory TeamInfo({
    int? currentIntegral,
    int? totalIntegral,
    List<GroupUpgradeInfo>? upgradeInfo,
  }) = _TeamInfo;

  factory TeamInfo.fromJson(Map<String, dynamic> json) =>
      _$TeamInfoFromJson(json);
}

@freezed
class GroupUpgradeInfo with _$GroupUpgradeInfo {
  const factory GroupUpgradeInfo({
    int? upgradeTargetIntegral,
    String? upgradeLevelDesc,
  }) = _GroupUpgradeInfo;

  factory GroupUpgradeInfo.fromJson(Map<String, dynamic> json) =>
      _$GroupUpgradeInfoFromJson(json);
}

@freezed
class TeamMember with _$TeamMember {
  const factory TeamMember({
    int? userId,
    String? userName,
    int? userType, //是否是自己
    List<Skin>? dressList,
    int? userStatus,
    StudyBtn? studyBtn,
    List<UserContribution>? userContribution,
    LearningInfo? learningInfo,
  }) = _TeamMember;

  factory TeamMember.fromJson(Map<String, dynamic> json) =>
      _$TeamMemberFromJson(json);
}

@freezed
class StudyBtn with _$StudyBtn {
  const factory StudyBtn({
    String? btnName,
    String? router,
  }) = _StudyBtn;

  factory StudyBtn.fromJson(Map<String, dynamic> json) =>
      _$StudyBtnFromJson(json);
}

@freezed
class LearningInfo with _$LearningInfo {
  const factory LearningInfo({
    String? icon,
    int? continuousLearningDays,
    int? todayIntegral,
  }) = _LearningInfo;

  factory LearningInfo.fromJson(Map<String, dynamic> json) =>
      _$LearningInfoFromJson(json);
}

@freezed
class Skin with _$Skin {
  const factory Skin({
    int? dressUpCategoryId,
    String? dressShowUrl,
    String? resourcesZipUrl,
  }) = _Skin;

  factory Skin.fromJson(Map<String, dynamic> json) => _$SkinFromJson(json);
}

@freezed
class UserContribution with _$UserContribution {
  const factory UserContribution(int? contributionType, String? titlePicUrl) =
      _UserContribution;

  factory UserContribution.fromJson(Map<String, dynamic> json) =>
      _$UserContributionFromJson(json);
}
