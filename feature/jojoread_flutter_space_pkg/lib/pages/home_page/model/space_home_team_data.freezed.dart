// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_home_team_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceHomeTeamData _$SpaceHomeTeamDataFromJson(Map<String, dynamic> json) {
  return _SpaceHomeTeamData.fromJson(json);
}

/// @nodoc
mixin _$SpaceHomeTeamData {
  String? get notTeamsTip => throw _privateConstructorUsedError;
  List<Team>? get teams => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceHomeTeamDataCopyWith<SpaceHomeTeamData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceHomeTeamDataCopyWith<$Res> {
  factory $SpaceHomeTeamDataCopyWith(
          SpaceHomeTeamData value, $Res Function(SpaceHomeTeamData) then) =
      _$SpaceHomeTeamDataCopyWithImpl<$Res, SpaceHomeTeamData>;
  @useResult
  $Res call({String? notTeamsTip, List<Team>? teams});
}

/// @nodoc
class _$SpaceHomeTeamDataCopyWithImpl<$Res, $Val extends SpaceHomeTeamData>
    implements $SpaceHomeTeamDataCopyWith<$Res> {
  _$SpaceHomeTeamDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notTeamsTip = freezed,
    Object? teams = freezed,
  }) {
    return _then(_value.copyWith(
      notTeamsTip: freezed == notTeamsTip
          ? _value.notTeamsTip
          : notTeamsTip // ignore: cast_nullable_to_non_nullable
              as String?,
      teams: freezed == teams
          ? _value.teams
          : teams // ignore: cast_nullable_to_non_nullable
              as List<Team>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SpaceHomeTeamDataCopyWith<$Res>
    implements $SpaceHomeTeamDataCopyWith<$Res> {
  factory _$$_SpaceHomeTeamDataCopyWith(_$_SpaceHomeTeamData value,
          $Res Function(_$_SpaceHomeTeamData) then) =
      __$$_SpaceHomeTeamDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? notTeamsTip, List<Team>? teams});
}

/// @nodoc
class __$$_SpaceHomeTeamDataCopyWithImpl<$Res>
    extends _$SpaceHomeTeamDataCopyWithImpl<$Res, _$_SpaceHomeTeamData>
    implements _$$_SpaceHomeTeamDataCopyWith<$Res> {
  __$$_SpaceHomeTeamDataCopyWithImpl(
      _$_SpaceHomeTeamData _value, $Res Function(_$_SpaceHomeTeamData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notTeamsTip = freezed,
    Object? teams = freezed,
  }) {
    return _then(_$_SpaceHomeTeamData(
      notTeamsTip: freezed == notTeamsTip
          ? _value.notTeamsTip
          : notTeamsTip // ignore: cast_nullable_to_non_nullable
              as String?,
      teams: freezed == teams
          ? _value._teams
          : teams // ignore: cast_nullable_to_non_nullable
              as List<Team>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpaceHomeTeamData implements _SpaceHomeTeamData {
  const _$_SpaceHomeTeamData({this.notTeamsTip, final List<Team>? teams})
      : _teams = teams;

  factory _$_SpaceHomeTeamData.fromJson(Map<String, dynamic> json) =>
      _$$_SpaceHomeTeamDataFromJson(json);

  @override
  final String? notTeamsTip;
  final List<Team>? _teams;
  @override
  List<Team>? get teams {
    final value = _teams;
    if (value == null) return null;
    if (_teams is EqualUnmodifiableListView) return _teams;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SpaceHomeTeamData(notTeamsTip: $notTeamsTip, teams: $teams)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SpaceHomeTeamData &&
            (identical(other.notTeamsTip, notTeamsTip) ||
                other.notTeamsTip == notTeamsTip) &&
            const DeepCollectionEquality().equals(other._teams, _teams));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, notTeamsTip, const DeepCollectionEquality().hash(_teams));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpaceHomeTeamDataCopyWith<_$_SpaceHomeTeamData> get copyWith =>
      __$$_SpaceHomeTeamDataCopyWithImpl<_$_SpaceHomeTeamData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpaceHomeTeamDataToJson(
      this,
    );
  }
}

abstract class _SpaceHomeTeamData implements SpaceHomeTeamData {
  const factory _SpaceHomeTeamData(
      {final String? notTeamsTip,
      final List<Team>? teams}) = _$_SpaceHomeTeamData;

  factory _SpaceHomeTeamData.fromJson(Map<String, dynamic> json) =
      _$_SpaceHomeTeamData.fromJson;

  @override
  String? get notTeamsTip;
  @override
  List<Team>? get teams;
  @override
  @JsonKey(ignore: true)
  _$$_SpaceHomeTeamDataCopyWith<_$_SpaceHomeTeamData> get copyWith =>
      throw _privateConstructorUsedError;
}

Team _$TeamFromJson(Map<String, dynamic> json) {
  return _Team.fromJson(json);
}

/// @nodoc
mixin _$Team {
  String? get className => throw _privateConstructorUsedError;
  int? get teamId => throw _privateConstructorUsedError;
  CourseInfo? get courseInfo => throw _privateConstructorUsedError;
  ClassInfo? get classInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamCopyWith<Team> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamCopyWith<$Res> {
  factory $TeamCopyWith(Team value, $Res Function(Team) then) =
      _$TeamCopyWithImpl<$Res, Team>;
  @useResult
  $Res call(
      {String? className,
      int? teamId,
      CourseInfo? courseInfo,
      ClassInfo? classInfo});

  $CourseInfoCopyWith<$Res>? get courseInfo;
  $ClassInfoCopyWith<$Res>? get classInfo;
}

/// @nodoc
class _$TeamCopyWithImpl<$Res, $Val extends Team>
    implements $TeamCopyWith<$Res> {
  _$TeamCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? className = freezed,
    Object? teamId = freezed,
    Object? courseInfo = freezed,
    Object? classInfo = freezed,
  }) {
    return _then(_value.copyWith(
      className: freezed == className
          ? _value.className
          : className // ignore: cast_nullable_to_non_nullable
              as String?,
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as CourseInfo?,
      classInfo: freezed == classInfo
          ? _value.classInfo
          : classInfo // ignore: cast_nullable_to_non_nullable
              as ClassInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseInfoCopyWith<$Res>? get courseInfo {
    if (_value.courseInfo == null) {
      return null;
    }

    return $CourseInfoCopyWith<$Res>(_value.courseInfo!, (value) {
      return _then(_value.copyWith(courseInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassInfoCopyWith<$Res>? get classInfo {
    if (_value.classInfo == null) {
      return null;
    }

    return $ClassInfoCopyWith<$Res>(_value.classInfo!, (value) {
      return _then(_value.copyWith(classInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_TeamCopyWith<$Res> implements $TeamCopyWith<$Res> {
  factory _$$_TeamCopyWith(_$_Team value, $Res Function(_$_Team) then) =
      __$$_TeamCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? className,
      int? teamId,
      CourseInfo? courseInfo,
      ClassInfo? classInfo});

  @override
  $CourseInfoCopyWith<$Res>? get courseInfo;
  @override
  $ClassInfoCopyWith<$Res>? get classInfo;
}

/// @nodoc
class __$$_TeamCopyWithImpl<$Res> extends _$TeamCopyWithImpl<$Res, _$_Team>
    implements _$$_TeamCopyWith<$Res> {
  __$$_TeamCopyWithImpl(_$_Team _value, $Res Function(_$_Team) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? className = freezed,
    Object? teamId = freezed,
    Object? courseInfo = freezed,
    Object? classInfo = freezed,
  }) {
    return _then(_$_Team(
      className: freezed == className
          ? _value.className
          : className // ignore: cast_nullable_to_non_nullable
              as String?,
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as CourseInfo?,
      classInfo: freezed == classInfo
          ? _value.classInfo
          : classInfo // ignore: cast_nullable_to_non_nullable
              as ClassInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Team implements _Team {
  const _$_Team({this.className, this.teamId, this.courseInfo, this.classInfo});

  factory _$_Team.fromJson(Map<String, dynamic> json) => _$$_TeamFromJson(json);

  @override
  final String? className;
  @override
  final int? teamId;
  @override
  final CourseInfo? courseInfo;
  @override
  final ClassInfo? classInfo;

  @override
  String toString() {
    return 'Team(className: $className, teamId: $teamId, courseInfo: $courseInfo, classInfo: $classInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Team &&
            (identical(other.className, className) ||
                other.className == className) &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.courseInfo, courseInfo) ||
                other.courseInfo == courseInfo) &&
            (identical(other.classInfo, classInfo) ||
                other.classInfo == classInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, className, teamId, courseInfo, classInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamCopyWith<_$_Team> get copyWith =>
      __$$_TeamCopyWithImpl<_$_Team>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamToJson(
      this,
    );
  }
}

abstract class _Team implements Team {
  const factory _Team(
      {final String? className,
      final int? teamId,
      final CourseInfo? courseInfo,
      final ClassInfo? classInfo}) = _$_Team;

  factory _Team.fromJson(Map<String, dynamic> json) = _$_Team.fromJson;

  @override
  String? get className;
  @override
  int? get teamId;
  @override
  CourseInfo? get courseInfo;
  @override
  ClassInfo? get classInfo;
  @override
  @JsonKey(ignore: true)
  _$$_TeamCopyWith<_$_Team> get copyWith => throw _privateConstructorUsedError;
}

CourseInfo _$CourseInfoFromJson(Map<String, dynamic> json) {
  return _CourseInfo.fromJson(json);
}

/// @nodoc
mixin _$CourseInfo {
  int? get courseId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseInfoCopyWith<CourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseInfoCopyWith<$Res> {
  factory $CourseInfoCopyWith(
          CourseInfo value, $Res Function(CourseInfo) then) =
      _$CourseInfoCopyWithImpl<$Res, CourseInfo>;
  @useResult
  $Res call({int? courseId, String? courseKey, String? courseName});
}

/// @nodoc
class _$CourseInfoCopyWithImpl<$Res, $Val extends CourseInfo>
    implements $CourseInfoCopyWith<$Res> {
  _$CourseInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
  }) {
    return _then(_value.copyWith(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseInfoCopyWith<$Res>
    implements $CourseInfoCopyWith<$Res> {
  factory _$$_CourseInfoCopyWith(
          _$_CourseInfo value, $Res Function(_$_CourseInfo) then) =
      __$$_CourseInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? courseId, String? courseKey, String? courseName});
}

/// @nodoc
class __$$_CourseInfoCopyWithImpl<$Res>
    extends _$CourseInfoCopyWithImpl<$Res, _$_CourseInfo>
    implements _$$_CourseInfoCopyWith<$Res> {
  __$$_CourseInfoCopyWithImpl(
      _$_CourseInfo _value, $Res Function(_$_CourseInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
  }) {
    return _then(_$_CourseInfo(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseInfo implements _CourseInfo {
  const _$_CourseInfo({this.courseId, this.courseKey, this.courseName});

  factory _$_CourseInfo.fromJson(Map<String, dynamic> json) =>
      _$$_CourseInfoFromJson(json);

  @override
  final int? courseId;
  @override
  final String? courseKey;
  @override
  final String? courseName;

  @override
  String toString() {
    return 'CourseInfo(courseId: $courseId, courseKey: $courseKey, courseName: $courseName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseInfo &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, courseId, courseKey, courseName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseInfoCopyWith<_$_CourseInfo> get copyWith =>
      __$$_CourseInfoCopyWithImpl<_$_CourseInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseInfoToJson(
      this,
    );
  }
}

abstract class _CourseInfo implements CourseInfo {
  const factory _CourseInfo(
      {final int? courseId,
      final String? courseKey,
      final String? courseName}) = _$_CourseInfo;

  factory _CourseInfo.fromJson(Map<String, dynamic> json) =
      _$_CourseInfo.fromJson;

  @override
  int? get courseId;
  @override
  String? get courseKey;
  @override
  String? get courseName;
  @override
  @JsonKey(ignore: true)
  _$$_CourseInfoCopyWith<_$_CourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassInfo _$ClassInfoFromJson(Map<String, dynamic> json) {
  return _ClassInfo.fromJson(json);
}

/// @nodoc
mixin _$ClassInfo {
  int? get classId => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  String? get className => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassInfoCopyWith<ClassInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassInfoCopyWith<$Res> {
  factory $ClassInfoCopyWith(ClassInfo value, $Res Function(ClassInfo) then) =
      _$ClassInfoCopyWithImpl<$Res, ClassInfo>;
  @useResult
  $Res call({int? classId, String? classKey, String? className});
}

/// @nodoc
class _$ClassInfoCopyWithImpl<$Res, $Val extends ClassInfo>
    implements $ClassInfoCopyWith<$Res> {
  _$ClassInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? className = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      className: freezed == className
          ? _value.className
          : className // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassInfoCopyWith<$Res> implements $ClassInfoCopyWith<$Res> {
  factory _$$_ClassInfoCopyWith(
          _$_ClassInfo value, $Res Function(_$_ClassInfo) then) =
      __$$_ClassInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? classId, String? classKey, String? className});
}

/// @nodoc
class __$$_ClassInfoCopyWithImpl<$Res>
    extends _$ClassInfoCopyWithImpl<$Res, _$_ClassInfo>
    implements _$$_ClassInfoCopyWith<$Res> {
  __$$_ClassInfoCopyWithImpl(
      _$_ClassInfo _value, $Res Function(_$_ClassInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? className = freezed,
  }) {
    return _then(_$_ClassInfo(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      className: freezed == className
          ? _value.className
          : className // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassInfo implements _ClassInfo {
  const _$_ClassInfo({this.classId, this.classKey, this.className});

  factory _$_ClassInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassInfoFromJson(json);

  @override
  final int? classId;
  @override
  final String? classKey;
  @override
  final String? className;

  @override
  String toString() {
    return 'ClassInfo(classId: $classId, classKey: $classKey, className: $className)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassInfo &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.className, className) ||
                other.className == className));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, classId, classKey, className);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassInfoCopyWith<_$_ClassInfo> get copyWith =>
      __$$_ClassInfoCopyWithImpl<_$_ClassInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassInfoToJson(
      this,
    );
  }
}

abstract class _ClassInfo implements ClassInfo {
  const factory _ClassInfo(
      {final int? classId,
      final String? classKey,
      final String? className}) = _$_ClassInfo;

  factory _ClassInfo.fromJson(Map<String, dynamic> json) =
      _$_ClassInfo.fromJson;

  @override
  int? get classId;
  @override
  String? get classKey;
  @override
  String? get className;
  @override
  @JsonKey(ignore: true)
  _$$_ClassInfoCopyWith<_$_ClassInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
