import 'package:jojo_flutter_base/base.dart';

part 'space_mall_goods_data.freezed.dart';
part 'space_mall_goods_data.g.dart';

@freezed
class SpaceMallGoodsData with _$SpaceMallGoodsData {
  const factory SpaceMallGoodsData({
    List<GoodsSkin>? dressUps,
    int? useAssetType,
    int? total,
    int? pageSize,
    int? pageNum,
  }) = _SpaceMallGoodsData;

  factory SpaceMallGoodsData.fromJson(Map<String, dynamic> json) =>
      _$SpaceMallGoodsDataFromJson(json);
}

@freezed
class GoodsSkin with _$GoodsSkin {
  const factory GoodsSkin({
    int? status, // （0：未购买，1：已购买(拥有)，2：已使用）
    int? skuId,
    String? dressUpName,
    String? price,  // 购买价格
    int? goodId,
    int? dressUpCategory,
    String? dressShowUrl,
    String? resourcesZipUrl,
    String? dressUpLevel,
    List<GoodsSkin?>? children, // 新增的children字段
  }) = _GoodsSkin;

  factory GoodsSkin.fromJson(Map<String, dynamic> json) =>
      _$GoodsSkinFromJson(json);
}
