// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_home_resource_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceHomeResourceData _$SpaceHomeResourceDataFromJson(
    Map<String, dynamic> json) {
  return _SpaceHomeResourceData.fromJson(json);
}

/// @nodoc
mixin _$SpaceHomeResourceData {
  ///组员骨骼压缩包
  String? get memberBonesZipUrl => throw _privateConstructorUsedError;

  ///背景压缩包
  String? get backGroundZipUrl => throw _privateConstructorUsedError;

  ///点赞动画压缩包
  String? get likeZipUrl => throw _privateConstructorUsedError;

  ///戳一戳动画压缩包
  String? get prickZipUrl => throw _privateConstructorUsedError;

  ///宝箱动画压缩包
  String? get boxZipUrl => throw _privateConstructorUsedError;

  ///规则介绍页压缩包
  String? get ruleZipUrl => throw _privateConstructorUsedError;

  ///学习按钮信息
  ButtonInfo? get learnButtonInfo => throw _privateConstructorUsedError;

  ///规则按钮信息
  ButtonInfo? get ruleButtonInfo => throw _privateConstructorUsedError;

  ///历史战绩按钮信息
  ButtonInfo? get historyPerformanceButtonInfo =>
      throw _privateConstructorUsedError;

  ///团队贡献详情按钮信息
  ButtonInfo? get teamContributionDetailButtonInfo =>
      throw _privateConstructorUsedError;

  ///我的装扮按钮信息
  ButtonInfo? get myDressUpButtonInfo => throw _privateConstructorUsedError;

  ///黄金队伍图标信息
  DTeamIconInfo? get goldTeamIconInfo => throw _privateConstructorUsedError;

  ///超神队伍图标信息
  DTeamIconInfo? get legendTeamIconInfo => throw _privateConstructorUsedError;

  ///默认皮肤列表
  List<DefaultSkinList>? get defaultSkinList =>
      throw _privateConstructorUsedError;

  ///已拥有和默认皮肤列表
  List<DefaultSkinList>? get ownAndDefaultSkinList =>
      throw _privateConstructorUsedError;

  /// 彩带资源
  String? get flowerZipUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceHomeResourceDataCopyWith<SpaceHomeResourceData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceHomeResourceDataCopyWith<$Res> {
  factory $SpaceHomeResourceDataCopyWith(SpaceHomeResourceData value,
          $Res Function(SpaceHomeResourceData) then) =
      _$SpaceHomeResourceDataCopyWithImpl<$Res, SpaceHomeResourceData>;
  @useResult
  $Res call(
      {String? memberBonesZipUrl,
      String? backGroundZipUrl,
      String? likeZipUrl,
      String? prickZipUrl,
      String? boxZipUrl,
      String? ruleZipUrl,
      ButtonInfo? learnButtonInfo,
      ButtonInfo? ruleButtonInfo,
      ButtonInfo? historyPerformanceButtonInfo,
      ButtonInfo? teamContributionDetailButtonInfo,
      ButtonInfo? myDressUpButtonInfo,
      DTeamIconInfo? goldTeamIconInfo,
      DTeamIconInfo? legendTeamIconInfo,
      List<DefaultSkinList>? defaultSkinList,
      List<DefaultSkinList>? ownAndDefaultSkinList,
      String? flowerZipUrl});

  $ButtonInfoCopyWith<$Res>? get learnButtonInfo;
  $ButtonInfoCopyWith<$Res>? get ruleButtonInfo;
  $ButtonInfoCopyWith<$Res>? get historyPerformanceButtonInfo;
  $ButtonInfoCopyWith<$Res>? get teamContributionDetailButtonInfo;
  $ButtonInfoCopyWith<$Res>? get myDressUpButtonInfo;
  $DTeamIconInfoCopyWith<$Res>? get goldTeamIconInfo;
  $DTeamIconInfoCopyWith<$Res>? get legendTeamIconInfo;
}

/// @nodoc
class _$SpaceHomeResourceDataCopyWithImpl<$Res,
        $Val extends SpaceHomeResourceData>
    implements $SpaceHomeResourceDataCopyWith<$Res> {
  _$SpaceHomeResourceDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberBonesZipUrl = freezed,
    Object? backGroundZipUrl = freezed,
    Object? likeZipUrl = freezed,
    Object? prickZipUrl = freezed,
    Object? boxZipUrl = freezed,
    Object? ruleZipUrl = freezed,
    Object? learnButtonInfo = freezed,
    Object? ruleButtonInfo = freezed,
    Object? historyPerformanceButtonInfo = freezed,
    Object? teamContributionDetailButtonInfo = freezed,
    Object? myDressUpButtonInfo = freezed,
    Object? goldTeamIconInfo = freezed,
    Object? legendTeamIconInfo = freezed,
    Object? defaultSkinList = freezed,
    Object? ownAndDefaultSkinList = freezed,
    Object? flowerZipUrl = freezed,
  }) {
    return _then(_value.copyWith(
      memberBonesZipUrl: freezed == memberBonesZipUrl
          ? _value.memberBonesZipUrl
          : memberBonesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backGroundZipUrl: freezed == backGroundZipUrl
          ? _value.backGroundZipUrl
          : backGroundZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      likeZipUrl: freezed == likeZipUrl
          ? _value.likeZipUrl
          : likeZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      prickZipUrl: freezed == prickZipUrl
          ? _value.prickZipUrl
          : prickZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      boxZipUrl: freezed == boxZipUrl
          ? _value.boxZipUrl
          : boxZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ruleZipUrl: freezed == ruleZipUrl
          ? _value.ruleZipUrl
          : ruleZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      learnButtonInfo: freezed == learnButtonInfo
          ? _value.learnButtonInfo
          : learnButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      ruleButtonInfo: freezed == ruleButtonInfo
          ? _value.ruleButtonInfo
          : ruleButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      historyPerformanceButtonInfo: freezed == historyPerformanceButtonInfo
          ? _value.historyPerformanceButtonInfo
          : historyPerformanceButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      teamContributionDetailButtonInfo: freezed ==
              teamContributionDetailButtonInfo
          ? _value.teamContributionDetailButtonInfo
          : teamContributionDetailButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      myDressUpButtonInfo: freezed == myDressUpButtonInfo
          ? _value.myDressUpButtonInfo
          : myDressUpButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      goldTeamIconInfo: freezed == goldTeamIconInfo
          ? _value.goldTeamIconInfo
          : goldTeamIconInfo // ignore: cast_nullable_to_non_nullable
              as DTeamIconInfo?,
      legendTeamIconInfo: freezed == legendTeamIconInfo
          ? _value.legendTeamIconInfo
          : legendTeamIconInfo // ignore: cast_nullable_to_non_nullable
              as DTeamIconInfo?,
      defaultSkinList: freezed == defaultSkinList
          ? _value.defaultSkinList
          : defaultSkinList // ignore: cast_nullable_to_non_nullable
              as List<DefaultSkinList>?,
      ownAndDefaultSkinList: freezed == ownAndDefaultSkinList
          ? _value.ownAndDefaultSkinList
          : ownAndDefaultSkinList // ignore: cast_nullable_to_non_nullable
              as List<DefaultSkinList>?,
      flowerZipUrl: freezed == flowerZipUrl
          ? _value.flowerZipUrl
          : flowerZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ButtonInfoCopyWith<$Res>? get learnButtonInfo {
    if (_value.learnButtonInfo == null) {
      return null;
    }

    return $ButtonInfoCopyWith<$Res>(_value.learnButtonInfo!, (value) {
      return _then(_value.copyWith(learnButtonInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ButtonInfoCopyWith<$Res>? get ruleButtonInfo {
    if (_value.ruleButtonInfo == null) {
      return null;
    }

    return $ButtonInfoCopyWith<$Res>(_value.ruleButtonInfo!, (value) {
      return _then(_value.copyWith(ruleButtonInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ButtonInfoCopyWith<$Res>? get historyPerformanceButtonInfo {
    if (_value.historyPerformanceButtonInfo == null) {
      return null;
    }

    return $ButtonInfoCopyWith<$Res>(_value.historyPerformanceButtonInfo!,
        (value) {
      return _then(
          _value.copyWith(historyPerformanceButtonInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ButtonInfoCopyWith<$Res>? get teamContributionDetailButtonInfo {
    if (_value.teamContributionDetailButtonInfo == null) {
      return null;
    }

    return $ButtonInfoCopyWith<$Res>(_value.teamContributionDetailButtonInfo!,
        (value) {
      return _then(
          _value.copyWith(teamContributionDetailButtonInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ButtonInfoCopyWith<$Res>? get myDressUpButtonInfo {
    if (_value.myDressUpButtonInfo == null) {
      return null;
    }

    return $ButtonInfoCopyWith<$Res>(_value.myDressUpButtonInfo!, (value) {
      return _then(_value.copyWith(myDressUpButtonInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $DTeamIconInfoCopyWith<$Res>? get goldTeamIconInfo {
    if (_value.goldTeamIconInfo == null) {
      return null;
    }

    return $DTeamIconInfoCopyWith<$Res>(_value.goldTeamIconInfo!, (value) {
      return _then(_value.copyWith(goldTeamIconInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $DTeamIconInfoCopyWith<$Res>? get legendTeamIconInfo {
    if (_value.legendTeamIconInfo == null) {
      return null;
    }

    return $DTeamIconInfoCopyWith<$Res>(_value.legendTeamIconInfo!, (value) {
      return _then(_value.copyWith(legendTeamIconInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_SpaceHomeResourceDataCopyWith<$Res>
    implements $SpaceHomeResourceDataCopyWith<$Res> {
  factory _$$_SpaceHomeResourceDataCopyWith(_$_SpaceHomeResourceData value,
          $Res Function(_$_SpaceHomeResourceData) then) =
      __$$_SpaceHomeResourceDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? memberBonesZipUrl,
      String? backGroundZipUrl,
      String? likeZipUrl,
      String? prickZipUrl,
      String? boxZipUrl,
      String? ruleZipUrl,
      ButtonInfo? learnButtonInfo,
      ButtonInfo? ruleButtonInfo,
      ButtonInfo? historyPerformanceButtonInfo,
      ButtonInfo? teamContributionDetailButtonInfo,
      ButtonInfo? myDressUpButtonInfo,
      DTeamIconInfo? goldTeamIconInfo,
      DTeamIconInfo? legendTeamIconInfo,
      List<DefaultSkinList>? defaultSkinList,
      List<DefaultSkinList>? ownAndDefaultSkinList,
      String? flowerZipUrl});

  @override
  $ButtonInfoCopyWith<$Res>? get learnButtonInfo;
  @override
  $ButtonInfoCopyWith<$Res>? get ruleButtonInfo;
  @override
  $ButtonInfoCopyWith<$Res>? get historyPerformanceButtonInfo;
  @override
  $ButtonInfoCopyWith<$Res>? get teamContributionDetailButtonInfo;
  @override
  $ButtonInfoCopyWith<$Res>? get myDressUpButtonInfo;
  @override
  $DTeamIconInfoCopyWith<$Res>? get goldTeamIconInfo;
  @override
  $DTeamIconInfoCopyWith<$Res>? get legendTeamIconInfo;
}

/// @nodoc
class __$$_SpaceHomeResourceDataCopyWithImpl<$Res>
    extends _$SpaceHomeResourceDataCopyWithImpl<$Res, _$_SpaceHomeResourceData>
    implements _$$_SpaceHomeResourceDataCopyWith<$Res> {
  __$$_SpaceHomeResourceDataCopyWithImpl(_$_SpaceHomeResourceData _value,
      $Res Function(_$_SpaceHomeResourceData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberBonesZipUrl = freezed,
    Object? backGroundZipUrl = freezed,
    Object? likeZipUrl = freezed,
    Object? prickZipUrl = freezed,
    Object? boxZipUrl = freezed,
    Object? ruleZipUrl = freezed,
    Object? learnButtonInfo = freezed,
    Object? ruleButtonInfo = freezed,
    Object? historyPerformanceButtonInfo = freezed,
    Object? teamContributionDetailButtonInfo = freezed,
    Object? myDressUpButtonInfo = freezed,
    Object? goldTeamIconInfo = freezed,
    Object? legendTeamIconInfo = freezed,
    Object? defaultSkinList = freezed,
    Object? ownAndDefaultSkinList = freezed,
    Object? flowerZipUrl = freezed,
  }) {
    return _then(_$_SpaceHomeResourceData(
      memberBonesZipUrl: freezed == memberBonesZipUrl
          ? _value.memberBonesZipUrl
          : memberBonesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backGroundZipUrl: freezed == backGroundZipUrl
          ? _value.backGroundZipUrl
          : backGroundZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      likeZipUrl: freezed == likeZipUrl
          ? _value.likeZipUrl
          : likeZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      prickZipUrl: freezed == prickZipUrl
          ? _value.prickZipUrl
          : prickZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      boxZipUrl: freezed == boxZipUrl
          ? _value.boxZipUrl
          : boxZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ruleZipUrl: freezed == ruleZipUrl
          ? _value.ruleZipUrl
          : ruleZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      learnButtonInfo: freezed == learnButtonInfo
          ? _value.learnButtonInfo
          : learnButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      ruleButtonInfo: freezed == ruleButtonInfo
          ? _value.ruleButtonInfo
          : ruleButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      historyPerformanceButtonInfo: freezed == historyPerformanceButtonInfo
          ? _value.historyPerformanceButtonInfo
          : historyPerformanceButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      teamContributionDetailButtonInfo: freezed ==
              teamContributionDetailButtonInfo
          ? _value.teamContributionDetailButtonInfo
          : teamContributionDetailButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      myDressUpButtonInfo: freezed == myDressUpButtonInfo
          ? _value.myDressUpButtonInfo
          : myDressUpButtonInfo // ignore: cast_nullable_to_non_nullable
              as ButtonInfo?,
      goldTeamIconInfo: freezed == goldTeamIconInfo
          ? _value.goldTeamIconInfo
          : goldTeamIconInfo // ignore: cast_nullable_to_non_nullable
              as DTeamIconInfo?,
      legendTeamIconInfo: freezed == legendTeamIconInfo
          ? _value.legendTeamIconInfo
          : legendTeamIconInfo // ignore: cast_nullable_to_non_nullable
              as DTeamIconInfo?,
      defaultSkinList: freezed == defaultSkinList
          ? _value._defaultSkinList
          : defaultSkinList // ignore: cast_nullable_to_non_nullable
              as List<DefaultSkinList>?,
      ownAndDefaultSkinList: freezed == ownAndDefaultSkinList
          ? _value._ownAndDefaultSkinList
          : ownAndDefaultSkinList // ignore: cast_nullable_to_non_nullable
              as List<DefaultSkinList>?,
      flowerZipUrl: freezed == flowerZipUrl
          ? _value.flowerZipUrl
          : flowerZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpaceHomeResourceData implements _SpaceHomeResourceData {
  const _$_SpaceHomeResourceData(
      {this.memberBonesZipUrl,
      this.backGroundZipUrl,
      this.likeZipUrl,
      this.prickZipUrl,
      this.boxZipUrl,
      this.ruleZipUrl,
      this.learnButtonInfo,
      this.ruleButtonInfo,
      this.historyPerformanceButtonInfo,
      this.teamContributionDetailButtonInfo,
      this.myDressUpButtonInfo,
      this.goldTeamIconInfo,
      this.legendTeamIconInfo,
      final List<DefaultSkinList>? defaultSkinList,
      final List<DefaultSkinList>? ownAndDefaultSkinList,
      this.flowerZipUrl})
      : _defaultSkinList = defaultSkinList,
        _ownAndDefaultSkinList = ownAndDefaultSkinList;

  factory _$_SpaceHomeResourceData.fromJson(Map<String, dynamic> json) =>
      _$$_SpaceHomeResourceDataFromJson(json);

  ///组员骨骼压缩包
  @override
  final String? memberBonesZipUrl;

  ///背景压缩包
  @override
  final String? backGroundZipUrl;

  ///点赞动画压缩包
  @override
  final String? likeZipUrl;

  ///戳一戳动画压缩包
  @override
  final String? prickZipUrl;

  ///宝箱动画压缩包
  @override
  final String? boxZipUrl;

  ///规则介绍页压缩包
  @override
  final String? ruleZipUrl;

  ///学习按钮信息
  @override
  final ButtonInfo? learnButtonInfo;

  ///规则按钮信息
  @override
  final ButtonInfo? ruleButtonInfo;

  ///历史战绩按钮信息
  @override
  final ButtonInfo? historyPerformanceButtonInfo;

  ///团队贡献详情按钮信息
  @override
  final ButtonInfo? teamContributionDetailButtonInfo;

  ///我的装扮按钮信息
  @override
  final ButtonInfo? myDressUpButtonInfo;

  ///黄金队伍图标信息
  @override
  final DTeamIconInfo? goldTeamIconInfo;

  ///超神队伍图标信息
  @override
  final DTeamIconInfo? legendTeamIconInfo;

  ///默认皮肤列表
  final List<DefaultSkinList>? _defaultSkinList;

  ///默认皮肤列表
  @override
  List<DefaultSkinList>? get defaultSkinList {
    final value = _defaultSkinList;
    if (value == null) return null;
    if (_defaultSkinList is EqualUnmodifiableListView) return _defaultSkinList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  ///已拥有和默认皮肤列表
  final List<DefaultSkinList>? _ownAndDefaultSkinList;

  ///已拥有和默认皮肤列表
  @override
  List<DefaultSkinList>? get ownAndDefaultSkinList {
    final value = _ownAndDefaultSkinList;
    if (value == null) return null;
    if (_ownAndDefaultSkinList is EqualUnmodifiableListView)
      return _ownAndDefaultSkinList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// 彩带资源
  @override
  final String? flowerZipUrl;

  @override
  String toString() {
    return 'SpaceHomeResourceData(memberBonesZipUrl: $memberBonesZipUrl, backGroundZipUrl: $backGroundZipUrl, likeZipUrl: $likeZipUrl, prickZipUrl: $prickZipUrl, boxZipUrl: $boxZipUrl, ruleZipUrl: $ruleZipUrl, learnButtonInfo: $learnButtonInfo, ruleButtonInfo: $ruleButtonInfo, historyPerformanceButtonInfo: $historyPerformanceButtonInfo, teamContributionDetailButtonInfo: $teamContributionDetailButtonInfo, myDressUpButtonInfo: $myDressUpButtonInfo, goldTeamIconInfo: $goldTeamIconInfo, legendTeamIconInfo: $legendTeamIconInfo, defaultSkinList: $defaultSkinList, ownAndDefaultSkinList: $ownAndDefaultSkinList, flowerZipUrl: $flowerZipUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SpaceHomeResourceData &&
            (identical(other.memberBonesZipUrl, memberBonesZipUrl) ||
                other.memberBonesZipUrl == memberBonesZipUrl) &&
            (identical(other.backGroundZipUrl, backGroundZipUrl) ||
                other.backGroundZipUrl == backGroundZipUrl) &&
            (identical(other.likeZipUrl, likeZipUrl) ||
                other.likeZipUrl == likeZipUrl) &&
            (identical(other.prickZipUrl, prickZipUrl) ||
                other.prickZipUrl == prickZipUrl) &&
            (identical(other.boxZipUrl, boxZipUrl) ||
                other.boxZipUrl == boxZipUrl) &&
            (identical(other.ruleZipUrl, ruleZipUrl) ||
                other.ruleZipUrl == ruleZipUrl) &&
            (identical(other.learnButtonInfo, learnButtonInfo) ||
                other.learnButtonInfo == learnButtonInfo) &&
            (identical(other.ruleButtonInfo, ruleButtonInfo) ||
                other.ruleButtonInfo == ruleButtonInfo) &&
            (identical(other.historyPerformanceButtonInfo,
                    historyPerformanceButtonInfo) ||
                other.historyPerformanceButtonInfo ==
                    historyPerformanceButtonInfo) &&
            (identical(other.teamContributionDetailButtonInfo,
                    teamContributionDetailButtonInfo) ||
                other.teamContributionDetailButtonInfo ==
                    teamContributionDetailButtonInfo) &&
            (identical(other.myDressUpButtonInfo, myDressUpButtonInfo) ||
                other.myDressUpButtonInfo == myDressUpButtonInfo) &&
            (identical(other.goldTeamIconInfo, goldTeamIconInfo) ||
                other.goldTeamIconInfo == goldTeamIconInfo) &&
            (identical(other.legendTeamIconInfo, legendTeamIconInfo) ||
                other.legendTeamIconInfo == legendTeamIconInfo) &&
            const DeepCollectionEquality()
                .equals(other._defaultSkinList, _defaultSkinList) &&
            const DeepCollectionEquality()
                .equals(other._ownAndDefaultSkinList, _ownAndDefaultSkinList) &&
            (identical(other.flowerZipUrl, flowerZipUrl) ||
                other.flowerZipUrl == flowerZipUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      memberBonesZipUrl,
      backGroundZipUrl,
      likeZipUrl,
      prickZipUrl,
      boxZipUrl,
      ruleZipUrl,
      learnButtonInfo,
      ruleButtonInfo,
      historyPerformanceButtonInfo,
      teamContributionDetailButtonInfo,
      myDressUpButtonInfo,
      goldTeamIconInfo,
      legendTeamIconInfo,
      const DeepCollectionEquality().hash(_defaultSkinList),
      const DeepCollectionEquality().hash(_ownAndDefaultSkinList),
      flowerZipUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpaceHomeResourceDataCopyWith<_$_SpaceHomeResourceData> get copyWith =>
      __$$_SpaceHomeResourceDataCopyWithImpl<_$_SpaceHomeResourceData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpaceHomeResourceDataToJson(
      this,
    );
  }
}

abstract class _SpaceHomeResourceData implements SpaceHomeResourceData {
  const factory _SpaceHomeResourceData(
      {final String? memberBonesZipUrl,
      final String? backGroundZipUrl,
      final String? likeZipUrl,
      final String? prickZipUrl,
      final String? boxZipUrl,
      final String? ruleZipUrl,
      final ButtonInfo? learnButtonInfo,
      final ButtonInfo? ruleButtonInfo,
      final ButtonInfo? historyPerformanceButtonInfo,
      final ButtonInfo? teamContributionDetailButtonInfo,
      final ButtonInfo? myDressUpButtonInfo,
      final DTeamIconInfo? goldTeamIconInfo,
      final DTeamIconInfo? legendTeamIconInfo,
      final List<DefaultSkinList>? defaultSkinList,
      final List<DefaultSkinList>? ownAndDefaultSkinList,
      final String? flowerZipUrl}) = _$_SpaceHomeResourceData;

  factory _SpaceHomeResourceData.fromJson(Map<String, dynamic> json) =
      _$_SpaceHomeResourceData.fromJson;

  @override

  ///组员骨骼压缩包
  String? get memberBonesZipUrl;
  @override

  ///背景压缩包
  String? get backGroundZipUrl;
  @override

  ///点赞动画压缩包
  String? get likeZipUrl;
  @override

  ///戳一戳动画压缩包
  String? get prickZipUrl;
  @override

  ///宝箱动画压缩包
  String? get boxZipUrl;
  @override

  ///规则介绍页压缩包
  String? get ruleZipUrl;
  @override

  ///学习按钮信息
  ButtonInfo? get learnButtonInfo;
  @override

  ///规则按钮信息
  ButtonInfo? get ruleButtonInfo;
  @override

  ///历史战绩按钮信息
  ButtonInfo? get historyPerformanceButtonInfo;
  @override

  ///团队贡献详情按钮信息
  ButtonInfo? get teamContributionDetailButtonInfo;
  @override

  ///我的装扮按钮信息
  ButtonInfo? get myDressUpButtonInfo;
  @override

  ///黄金队伍图标信息
  DTeamIconInfo? get goldTeamIconInfo;
  @override

  ///超神队伍图标信息
  DTeamIconInfo? get legendTeamIconInfo;
  @override

  ///默认皮肤列表
  List<DefaultSkinList>? get defaultSkinList;
  @override

  ///已拥有和默认皮肤列表
  List<DefaultSkinList>? get ownAndDefaultSkinList;
  @override

  /// 彩带资源
  String? get flowerZipUrl;
  @override
  @JsonKey(ignore: true)
  _$$_SpaceHomeResourceDataCopyWith<_$_SpaceHomeResourceData> get copyWith =>
      throw _privateConstructorUsedError;
}

DefaultSkinList _$DefaultSkinListFromJson(Map<String, dynamic> json) {
  return _DefaultSkinList.fromJson(json);
}

/// @nodoc
mixin _$DefaultSkinList {
  int? get skinId => throw _privateConstructorUsedError;
  String? get dressShowUrl => throw _privateConstructorUsedError;
  String? get categoryShowName => throw _privateConstructorUsedError;
  int? get categoryShowCode => throw _privateConstructorUsedError;
  String? get resourcesZipUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DefaultSkinListCopyWith<DefaultSkinList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DefaultSkinListCopyWith<$Res> {
  factory $DefaultSkinListCopyWith(
          DefaultSkinList value, $Res Function(DefaultSkinList) then) =
      _$DefaultSkinListCopyWithImpl<$Res, DefaultSkinList>;
  @useResult
  $Res call(
      {int? skinId,
      String? dressShowUrl,
      String? categoryShowName,
      int? categoryShowCode,
      String? resourcesZipUrl});
}

/// @nodoc
class _$DefaultSkinListCopyWithImpl<$Res, $Val extends DefaultSkinList>
    implements $DefaultSkinListCopyWith<$Res> {
  _$DefaultSkinListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skinId = freezed,
    Object? dressShowUrl = freezed,
    Object? categoryShowName = freezed,
    Object? categoryShowCode = freezed,
    Object? resourcesZipUrl = freezed,
  }) {
    return _then(_value.copyWith(
      skinId: freezed == skinId
          ? _value.skinId
          : skinId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressShowUrl: freezed == dressShowUrl
          ? _value.dressShowUrl
          : dressShowUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryShowName: freezed == categoryShowName
          ? _value.categoryShowName
          : categoryShowName // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryShowCode: freezed == categoryShowCode
          ? _value.categoryShowCode
          : categoryShowCode // ignore: cast_nullable_to_non_nullable
              as int?,
      resourcesZipUrl: freezed == resourcesZipUrl
          ? _value.resourcesZipUrl
          : resourcesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DefaultSkinListCopyWith<$Res>
    implements $DefaultSkinListCopyWith<$Res> {
  factory _$$_DefaultSkinListCopyWith(
          _$_DefaultSkinList value, $Res Function(_$_DefaultSkinList) then) =
      __$$_DefaultSkinListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? skinId,
      String? dressShowUrl,
      String? categoryShowName,
      int? categoryShowCode,
      String? resourcesZipUrl});
}

/// @nodoc
class __$$_DefaultSkinListCopyWithImpl<$Res>
    extends _$DefaultSkinListCopyWithImpl<$Res, _$_DefaultSkinList>
    implements _$$_DefaultSkinListCopyWith<$Res> {
  __$$_DefaultSkinListCopyWithImpl(
      _$_DefaultSkinList _value, $Res Function(_$_DefaultSkinList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skinId = freezed,
    Object? dressShowUrl = freezed,
    Object? categoryShowName = freezed,
    Object? categoryShowCode = freezed,
    Object? resourcesZipUrl = freezed,
  }) {
    return _then(_$_DefaultSkinList(
      skinId: freezed == skinId
          ? _value.skinId
          : skinId // ignore: cast_nullable_to_non_nullable
              as int?,
      dressShowUrl: freezed == dressShowUrl
          ? _value.dressShowUrl
          : dressShowUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryShowName: freezed == categoryShowName
          ? _value.categoryShowName
          : categoryShowName // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryShowCode: freezed == categoryShowCode
          ? _value.categoryShowCode
          : categoryShowCode // ignore: cast_nullable_to_non_nullable
              as int?,
      resourcesZipUrl: freezed == resourcesZipUrl
          ? _value.resourcesZipUrl
          : resourcesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DefaultSkinList implements _DefaultSkinList {
  const _$_DefaultSkinList(
      {this.skinId,
      this.dressShowUrl,
      this.categoryShowName,
      this.categoryShowCode,
      this.resourcesZipUrl});

  factory _$_DefaultSkinList.fromJson(Map<String, dynamic> json) =>
      _$$_DefaultSkinListFromJson(json);

  @override
  final int? skinId;
  @override
  final String? dressShowUrl;
  @override
  final String? categoryShowName;
  @override
  final int? categoryShowCode;
  @override
  final String? resourcesZipUrl;

  @override
  String toString() {
    return 'DefaultSkinList(skinId: $skinId, dressShowUrl: $dressShowUrl, categoryShowName: $categoryShowName, categoryShowCode: $categoryShowCode, resourcesZipUrl: $resourcesZipUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DefaultSkinList &&
            (identical(other.skinId, skinId) || other.skinId == skinId) &&
            (identical(other.dressShowUrl, dressShowUrl) ||
                other.dressShowUrl == dressShowUrl) &&
            (identical(other.categoryShowName, categoryShowName) ||
                other.categoryShowName == categoryShowName) &&
            (identical(other.categoryShowCode, categoryShowCode) ||
                other.categoryShowCode == categoryShowCode) &&
            (identical(other.resourcesZipUrl, resourcesZipUrl) ||
                other.resourcesZipUrl == resourcesZipUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, skinId, dressShowUrl,
      categoryShowName, categoryShowCode, resourcesZipUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DefaultSkinListCopyWith<_$_DefaultSkinList> get copyWith =>
      __$$_DefaultSkinListCopyWithImpl<_$_DefaultSkinList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DefaultSkinListToJson(
      this,
    );
  }
}

abstract class _DefaultSkinList implements DefaultSkinList {
  const factory _DefaultSkinList(
      {final int? skinId,
      final String? dressShowUrl,
      final String? categoryShowName,
      final int? categoryShowCode,
      final String? resourcesZipUrl}) = _$_DefaultSkinList;

  factory _DefaultSkinList.fromJson(Map<String, dynamic> json) =
      _$_DefaultSkinList.fromJson;

  @override
  int? get skinId;
  @override
  String? get dressShowUrl;
  @override
  String? get categoryShowName;
  @override
  int? get categoryShowCode;
  @override
  String? get resourcesZipUrl;
  @override
  @JsonKey(ignore: true)
  _$$_DefaultSkinListCopyWith<_$_DefaultSkinList> get copyWith =>
      throw _privateConstructorUsedError;
}

DTeamIconInfo _$DTeamIconInfoFromJson(Map<String, dynamic> json) {
  return _DTeamIconInfo.fromJson(json);
}

/// @nodoc
mixin _$DTeamIconInfo {
  String? get selectedIconUrl => throw _privateConstructorUsedError;
  String? get unselectIconUrl => throw _privateConstructorUsedError;
  String? get iconName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DTeamIconInfoCopyWith<DTeamIconInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DTeamIconInfoCopyWith<$Res> {
  factory $DTeamIconInfoCopyWith(
          DTeamIconInfo value, $Res Function(DTeamIconInfo) then) =
      _$DTeamIconInfoCopyWithImpl<$Res, DTeamIconInfo>;
  @useResult
  $Res call(
      {String? selectedIconUrl, String? unselectIconUrl, String? iconName});
}

/// @nodoc
class _$DTeamIconInfoCopyWithImpl<$Res, $Val extends DTeamIconInfo>
    implements $DTeamIconInfoCopyWith<$Res> {
  _$DTeamIconInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedIconUrl = freezed,
    Object? unselectIconUrl = freezed,
    Object? iconName = freezed,
  }) {
    return _then(_value.copyWith(
      selectedIconUrl: freezed == selectedIconUrl
          ? _value.selectedIconUrl
          : selectedIconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      unselectIconUrl: freezed == unselectIconUrl
          ? _value.unselectIconUrl
          : unselectIconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      iconName: freezed == iconName
          ? _value.iconName
          : iconName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DTeamIconInfoCopyWith<$Res>
    implements $DTeamIconInfoCopyWith<$Res> {
  factory _$$_DTeamIconInfoCopyWith(
          _$_DTeamIconInfo value, $Res Function(_$_DTeamIconInfo) then) =
      __$$_DTeamIconInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? selectedIconUrl, String? unselectIconUrl, String? iconName});
}

/// @nodoc
class __$$_DTeamIconInfoCopyWithImpl<$Res>
    extends _$DTeamIconInfoCopyWithImpl<$Res, _$_DTeamIconInfo>
    implements _$$_DTeamIconInfoCopyWith<$Res> {
  __$$_DTeamIconInfoCopyWithImpl(
      _$_DTeamIconInfo _value, $Res Function(_$_DTeamIconInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedIconUrl = freezed,
    Object? unselectIconUrl = freezed,
    Object? iconName = freezed,
  }) {
    return _then(_$_DTeamIconInfo(
      selectedIconUrl: freezed == selectedIconUrl
          ? _value.selectedIconUrl
          : selectedIconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      unselectIconUrl: freezed == unselectIconUrl
          ? _value.unselectIconUrl
          : unselectIconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      iconName: freezed == iconName
          ? _value.iconName
          : iconName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DTeamIconInfo implements _DTeamIconInfo {
  const _$_DTeamIconInfo(
      {this.selectedIconUrl, this.unselectIconUrl, this.iconName});

  factory _$_DTeamIconInfo.fromJson(Map<String, dynamic> json) =>
      _$$_DTeamIconInfoFromJson(json);

  @override
  final String? selectedIconUrl;
  @override
  final String? unselectIconUrl;
  @override
  final String? iconName;

  @override
  String toString() {
    return 'DTeamIconInfo(selectedIconUrl: $selectedIconUrl, unselectIconUrl: $unselectIconUrl, iconName: $iconName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DTeamIconInfo &&
            (identical(other.selectedIconUrl, selectedIconUrl) ||
                other.selectedIconUrl == selectedIconUrl) &&
            (identical(other.unselectIconUrl, unselectIconUrl) ||
                other.unselectIconUrl == unselectIconUrl) &&
            (identical(other.iconName, iconName) ||
                other.iconName == iconName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, selectedIconUrl, unselectIconUrl, iconName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DTeamIconInfoCopyWith<_$_DTeamIconInfo> get copyWith =>
      __$$_DTeamIconInfoCopyWithImpl<_$_DTeamIconInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DTeamIconInfoToJson(
      this,
    );
  }
}

abstract class _DTeamIconInfo implements DTeamIconInfo {
  const factory _DTeamIconInfo(
      {final String? selectedIconUrl,
      final String? unselectIconUrl,
      final String? iconName}) = _$_DTeamIconInfo;

  factory _DTeamIconInfo.fromJson(Map<String, dynamic> json) =
      _$_DTeamIconInfo.fromJson;

  @override
  String? get selectedIconUrl;
  @override
  String? get unselectIconUrl;
  @override
  String? get iconName;
  @override
  @JsonKey(ignore: true)
  _$$_DTeamIconInfoCopyWith<_$_DTeamIconInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ButtonInfo _$ButtonInfoFromJson(Map<String, dynamic> json) {
  return _ButtonInfo.fromJson(json);
}

/// @nodoc
mixin _$ButtonInfo {
  String? get iconUrl => throw _privateConstructorUsedError;
  String? get buttonName => throw _privateConstructorUsedError;
  String? get buttonUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ButtonInfoCopyWith<ButtonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ButtonInfoCopyWith<$Res> {
  factory $ButtonInfoCopyWith(
          ButtonInfo value, $Res Function(ButtonInfo) then) =
      _$ButtonInfoCopyWithImpl<$Res, ButtonInfo>;
  @useResult
  $Res call({String? iconUrl, String? buttonName, String? buttonUrl});
}

/// @nodoc
class _$ButtonInfoCopyWithImpl<$Res, $Val extends ButtonInfo>
    implements $ButtonInfoCopyWith<$Res> {
  _$ButtonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? iconUrl = freezed,
    Object? buttonName = freezed,
    Object? buttonUrl = freezed,
  }) {
    return _then(_value.copyWith(
      iconUrl: freezed == iconUrl
          ? _value.iconUrl
          : iconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonName: freezed == buttonName
          ? _value.buttonName
          : buttonName // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonUrl: freezed == buttonUrl
          ? _value.buttonUrl
          : buttonUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ButtonInfoCopyWith<$Res>
    implements $ButtonInfoCopyWith<$Res> {
  factory _$$_ButtonInfoCopyWith(
          _$_ButtonInfo value, $Res Function(_$_ButtonInfo) then) =
      __$$_ButtonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? iconUrl, String? buttonName, String? buttonUrl});
}

/// @nodoc
class __$$_ButtonInfoCopyWithImpl<$Res>
    extends _$ButtonInfoCopyWithImpl<$Res, _$_ButtonInfo>
    implements _$$_ButtonInfoCopyWith<$Res> {
  __$$_ButtonInfoCopyWithImpl(
      _$_ButtonInfo _value, $Res Function(_$_ButtonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? iconUrl = freezed,
    Object? buttonName = freezed,
    Object? buttonUrl = freezed,
  }) {
    return _then(_$_ButtonInfo(
      iconUrl: freezed == iconUrl
          ? _value.iconUrl
          : iconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonName: freezed == buttonName
          ? _value.buttonName
          : buttonName // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonUrl: freezed == buttonUrl
          ? _value.buttonUrl
          : buttonUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ButtonInfo implements _ButtonInfo {
  const _$_ButtonInfo({this.iconUrl, this.buttonName, this.buttonUrl});

  factory _$_ButtonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ButtonInfoFromJson(json);

  @override
  final String? iconUrl;
  @override
  final String? buttonName;
  @override
  final String? buttonUrl;

  @override
  String toString() {
    return 'ButtonInfo(iconUrl: $iconUrl, buttonName: $buttonName, buttonUrl: $buttonUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ButtonInfo &&
            (identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl) &&
            (identical(other.buttonName, buttonName) ||
                other.buttonName == buttonName) &&
            (identical(other.buttonUrl, buttonUrl) ||
                other.buttonUrl == buttonUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, iconUrl, buttonName, buttonUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ButtonInfoCopyWith<_$_ButtonInfo> get copyWith =>
      __$$_ButtonInfoCopyWithImpl<_$_ButtonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ButtonInfoToJson(
      this,
    );
  }
}

abstract class _ButtonInfo implements ButtonInfo {
  const factory _ButtonInfo(
      {final String? iconUrl,
      final String? buttonName,
      final String? buttonUrl}) = _$_ButtonInfo;

  factory _ButtonInfo.fromJson(Map<String, dynamic> json) =
      _$_ButtonInfo.fromJson;

  @override
  String? get iconUrl;
  @override
  String? get buttonName;
  @override
  String? get buttonUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ButtonInfoCopyWith<_$_ButtonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
