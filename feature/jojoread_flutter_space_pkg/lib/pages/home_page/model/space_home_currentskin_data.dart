import 'package:jojo_flutter_base/base.dart';

part 'space_home_currentskin_data.freezed.dart';
part 'space_home_currentskin_data.g.dart';

@unfreezed
class SpaceHomeCurrentskinData with _$SpaceHomeCurrentskinData {
  factory SpaceHomeCurrentskinData({
    int? userId,
    String? userName,
    List<CurrentUserDressUps>? dressUps,
  }) = _SpaceHomeCurrentskinData;

  factory SpaceHomeCurrentskinData.fromJson(Map<String, dynamic> json) =>
      _$SpaceHomeCurrentskinDataFromJson(json);
}

@freezed
class CurrentUserDressUps with _$CurrentUserDressUps {
  const factory CurrentUserDressUps({
    int? dressUpId,
    int? dressUpCategoryId,
    String? spineDressUpCategoryName,
    String? dressShowUrl,
    String? resourcesZipUrl,
    String? price,
    int? status,
    List<CurrentUserDressUps?>? children,
  }) = _CurrentUserDressUps;

  factory CurrentUserDressUps.fromJson(Map<String, dynamic> json) =>
      _$CurrentUserDressUpsFromJson(json);
}

extension CurrentUserDressUpsExt on CurrentUserDressUps {
  bool get isGroup => children?.any((child) => child != null) ?? false;
}