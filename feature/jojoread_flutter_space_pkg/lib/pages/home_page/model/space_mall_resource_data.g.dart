// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_mall_resource_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SpaceMallResourceData _$$_SpaceMallResourceDataFromJson(
        Map<String, dynamic> json) =>
    _$_SpaceMallResourceData(
      memberBonesZipUrl: json['memberBonesZipUrl'] as String?,
      backGroundZipUrl: json['backGroundZipUrl'] as String?,
      categoryButtons: (json['categoryButtons'] as List<dynamic>?)
          ?.map((e) => CategoryButton.fromJson(e as Map<String, dynamic>))
          .toList(),
      dressImgGenerateConfig: json['dressImgGenerateConfig'] as String?,
      dressUpRarityBackImgConfig:
          (json['dressUpRarityBackImgConfig'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String?),
      ),
      bestContinuousLearnDays: json['bestContinuousLearnDays'] as int?,
      purchaseSwitch: json['purchaseSwitch'] as int?,
    );

Map<String, dynamic> _$$_SpaceMallResourceDataToJson(
        _$_SpaceMallResourceData instance) =>
    <String, dynamic>{
      'memberBonesZipUrl': instance.memberBonesZipUrl,
      'backGroundZipUrl': instance.backGroundZipUrl,
      'categoryButtons': instance.categoryButtons,
      'dressImgGenerateConfig': instance.dressImgGenerateConfig,
      'dressUpRarityBackImgConfig': instance.dressUpRarityBackImgConfig,
      'bestContinuousLearnDays': instance.bestContinuousLearnDays,
      'purchaseSwitch': instance.purchaseSwitch,
    };

_$_CategoryButton _$$_CategoryButtonFromJson(Map<String, dynamic> json) =>
    _$_CategoryButton(
      iconUrl: json['iconUrl'] as String?,
      buttonName: json['buttonName'] as String?,
      buttonSelectUrl: json['buttonSelectUrl'] as String?,
      buttonUnSelectUrl: json['buttonUnSelectUrl'] as String?,
      categoryId: json['categoryId'] as int?,
      spineSkinCategoryName: json['spineSkinCategoryName'] as String?,
      reachBuyThreshold: json['reachBuyThreshold'] as bool?,
      buyGoodsTips: json['buyGoodsTips'] as String?,
    );

Map<String, dynamic> _$$_CategoryButtonToJson(_$_CategoryButton instance) =>
    <String, dynamic>{
      'iconUrl': instance.iconUrl,
      'buttonName': instance.buttonName,
      'buttonSelectUrl': instance.buttonSelectUrl,
      'buttonUnSelectUrl': instance.buttonUnSelectUrl,
      'categoryId': instance.categoryId,
      'spineSkinCategoryName': instance.spineSkinCategoryName,
      'reachBuyThreshold': instance.reachBuyThreshold,
      'buyGoodsTips': instance.buyGoodsTips,
    };
