// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_mall_resource_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceMallResourceData _$SpaceMallResourceDataFromJson(
    Map<String, dynamic> json) {
  return _SpaceMallResourceData.fromJson(json);
}

/// @nodoc
mixin _$SpaceMallResourceData {
  String? get memberBonesZipUrl => throw _privateConstructorUsedError;
  String? get backGroundZipUrl => throw _privateConstructorUsedError;
  List<CategoryButton>? get categoryButtons =>
      throw _privateConstructorUsedError;
  String? get dressImgGenerateConfig => throw _privateConstructorUsedError;
  Map<String, String?>? get dressUpRarityBackImgConfig =>
      throw _privateConstructorUsedError;
  int? get bestContinuousLearnDays => throw _privateConstructorUsedError;
  int? get purchaseSwitch => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceMallResourceDataCopyWith<SpaceMallResourceData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceMallResourceDataCopyWith<$Res> {
  factory $SpaceMallResourceDataCopyWith(SpaceMallResourceData value,
          $Res Function(SpaceMallResourceData) then) =
      _$SpaceMallResourceDataCopyWithImpl<$Res, SpaceMallResourceData>;
  @useResult
  $Res call(
      {String? memberBonesZipUrl,
      String? backGroundZipUrl,
      List<CategoryButton>? categoryButtons,
      String? dressImgGenerateConfig,
      Map<String, String?>? dressUpRarityBackImgConfig,
      int? bestContinuousLearnDays,
      int? purchaseSwitch});
}

/// @nodoc
class _$SpaceMallResourceDataCopyWithImpl<$Res,
        $Val extends SpaceMallResourceData>
    implements $SpaceMallResourceDataCopyWith<$Res> {
  _$SpaceMallResourceDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberBonesZipUrl = freezed,
    Object? backGroundZipUrl = freezed,
    Object? categoryButtons = freezed,
    Object? dressImgGenerateConfig = freezed,
    Object? dressUpRarityBackImgConfig = freezed,
    Object? bestContinuousLearnDays = freezed,
    Object? purchaseSwitch = freezed,
  }) {
    return _then(_value.copyWith(
      memberBonesZipUrl: freezed == memberBonesZipUrl
          ? _value.memberBonesZipUrl
          : memberBonesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backGroundZipUrl: freezed == backGroundZipUrl
          ? _value.backGroundZipUrl
          : backGroundZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryButtons: freezed == categoryButtons
          ? _value.categoryButtons
          : categoryButtons // ignore: cast_nullable_to_non_nullable
              as List<CategoryButton>?,
      dressImgGenerateConfig: freezed == dressImgGenerateConfig
          ? _value.dressImgGenerateConfig
          : dressImgGenerateConfig // ignore: cast_nullable_to_non_nullable
              as String?,
      dressUpRarityBackImgConfig: freezed == dressUpRarityBackImgConfig
          ? _value.dressUpRarityBackImgConfig
          : dressUpRarityBackImgConfig // ignore: cast_nullable_to_non_nullable
              as Map<String, String?>?,
      bestContinuousLearnDays: freezed == bestContinuousLearnDays
          ? _value.bestContinuousLearnDays
          : bestContinuousLearnDays // ignore: cast_nullable_to_non_nullable
              as int?,
      purchaseSwitch: freezed == purchaseSwitch
          ? _value.purchaseSwitch
          : purchaseSwitch // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SpaceMallResourceDataCopyWith<$Res>
    implements $SpaceMallResourceDataCopyWith<$Res> {
  factory _$$_SpaceMallResourceDataCopyWith(_$_SpaceMallResourceData value,
          $Res Function(_$_SpaceMallResourceData) then) =
      __$$_SpaceMallResourceDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? memberBonesZipUrl,
      String? backGroundZipUrl,
      List<CategoryButton>? categoryButtons,
      String? dressImgGenerateConfig,
      Map<String, String?>? dressUpRarityBackImgConfig,
      int? bestContinuousLearnDays,
      int? purchaseSwitch});
}

/// @nodoc
class __$$_SpaceMallResourceDataCopyWithImpl<$Res>
    extends _$SpaceMallResourceDataCopyWithImpl<$Res, _$_SpaceMallResourceData>
    implements _$$_SpaceMallResourceDataCopyWith<$Res> {
  __$$_SpaceMallResourceDataCopyWithImpl(_$_SpaceMallResourceData _value,
      $Res Function(_$_SpaceMallResourceData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberBonesZipUrl = freezed,
    Object? backGroundZipUrl = freezed,
    Object? categoryButtons = freezed,
    Object? dressImgGenerateConfig = freezed,
    Object? dressUpRarityBackImgConfig = freezed,
    Object? bestContinuousLearnDays = freezed,
    Object? purchaseSwitch = freezed,
  }) {
    return _then(_$_SpaceMallResourceData(
      memberBonesZipUrl: freezed == memberBonesZipUrl
          ? _value.memberBonesZipUrl
          : memberBonesZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backGroundZipUrl: freezed == backGroundZipUrl
          ? _value.backGroundZipUrl
          : backGroundZipUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryButtons: freezed == categoryButtons
          ? _value._categoryButtons
          : categoryButtons // ignore: cast_nullable_to_non_nullable
              as List<CategoryButton>?,
      dressImgGenerateConfig: freezed == dressImgGenerateConfig
          ? _value.dressImgGenerateConfig
          : dressImgGenerateConfig // ignore: cast_nullable_to_non_nullable
              as String?,
      dressUpRarityBackImgConfig: freezed == dressUpRarityBackImgConfig
          ? _value._dressUpRarityBackImgConfig
          : dressUpRarityBackImgConfig // ignore: cast_nullable_to_non_nullable
              as Map<String, String?>?,
      bestContinuousLearnDays: freezed == bestContinuousLearnDays
          ? _value.bestContinuousLearnDays
          : bestContinuousLearnDays // ignore: cast_nullable_to_non_nullable
              as int?,
      purchaseSwitch: freezed == purchaseSwitch
          ? _value.purchaseSwitch
          : purchaseSwitch // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpaceMallResourceData implements _SpaceMallResourceData {
  const _$_SpaceMallResourceData(
      {this.memberBonesZipUrl,
      this.backGroundZipUrl,
      final List<CategoryButton>? categoryButtons,
      this.dressImgGenerateConfig,
      final Map<String, String?>? dressUpRarityBackImgConfig,
      this.bestContinuousLearnDays,
      this.purchaseSwitch})
      : _categoryButtons = categoryButtons,
        _dressUpRarityBackImgConfig = dressUpRarityBackImgConfig;

  factory _$_SpaceMallResourceData.fromJson(Map<String, dynamic> json) =>
      _$$_SpaceMallResourceDataFromJson(json);

  @override
  final String? memberBonesZipUrl;
  @override
  final String? backGroundZipUrl;
  final List<CategoryButton>? _categoryButtons;
  @override
  List<CategoryButton>? get categoryButtons {
    final value = _categoryButtons;
    if (value == null) return null;
    if (_categoryButtons is EqualUnmodifiableListView) return _categoryButtons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? dressImgGenerateConfig;
  final Map<String, String?>? _dressUpRarityBackImgConfig;
  @override
  Map<String, String?>? get dressUpRarityBackImgConfig {
    final value = _dressUpRarityBackImgConfig;
    if (value == null) return null;
    if (_dressUpRarityBackImgConfig is EqualUnmodifiableMapView)
      return _dressUpRarityBackImgConfig;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final int? bestContinuousLearnDays;
  @override
  final int? purchaseSwitch;

  @override
  String toString() {
    return 'SpaceMallResourceData(memberBonesZipUrl: $memberBonesZipUrl, backGroundZipUrl: $backGroundZipUrl, categoryButtons: $categoryButtons, dressImgGenerateConfig: $dressImgGenerateConfig, dressUpRarityBackImgConfig: $dressUpRarityBackImgConfig, bestContinuousLearnDays: $bestContinuousLearnDays, purchaseSwitch: $purchaseSwitch)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SpaceMallResourceData &&
            (identical(other.memberBonesZipUrl, memberBonesZipUrl) ||
                other.memberBonesZipUrl == memberBonesZipUrl) &&
            (identical(other.backGroundZipUrl, backGroundZipUrl) ||
                other.backGroundZipUrl == backGroundZipUrl) &&
            const DeepCollectionEquality()
                .equals(other._categoryButtons, _categoryButtons) &&
            (identical(other.dressImgGenerateConfig, dressImgGenerateConfig) ||
                other.dressImgGenerateConfig == dressImgGenerateConfig) &&
            const DeepCollectionEquality().equals(
                other._dressUpRarityBackImgConfig,
                _dressUpRarityBackImgConfig) &&
            (identical(
                    other.bestContinuousLearnDays, bestContinuousLearnDays) ||
                other.bestContinuousLearnDays == bestContinuousLearnDays) &&
            (identical(other.purchaseSwitch, purchaseSwitch) ||
                other.purchaseSwitch == purchaseSwitch));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      memberBonesZipUrl,
      backGroundZipUrl,
      const DeepCollectionEquality().hash(_categoryButtons),
      dressImgGenerateConfig,
      const DeepCollectionEquality().hash(_dressUpRarityBackImgConfig),
      bestContinuousLearnDays,
      purchaseSwitch);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpaceMallResourceDataCopyWith<_$_SpaceMallResourceData> get copyWith =>
      __$$_SpaceMallResourceDataCopyWithImpl<_$_SpaceMallResourceData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpaceMallResourceDataToJson(
      this,
    );
  }
}

abstract class _SpaceMallResourceData implements SpaceMallResourceData {
  const factory _SpaceMallResourceData(
      {final String? memberBonesZipUrl,
      final String? backGroundZipUrl,
      final List<CategoryButton>? categoryButtons,
      final String? dressImgGenerateConfig,
      final Map<String, String?>? dressUpRarityBackImgConfig,
      final int? bestContinuousLearnDays,
      final int? purchaseSwitch}) = _$_SpaceMallResourceData;

  factory _SpaceMallResourceData.fromJson(Map<String, dynamic> json) =
      _$_SpaceMallResourceData.fromJson;

  @override
  String? get memberBonesZipUrl;
  @override
  String? get backGroundZipUrl;
  @override
  List<CategoryButton>? get categoryButtons;
  @override
  String? get dressImgGenerateConfig;
  @override
  Map<String, String?>? get dressUpRarityBackImgConfig;
  @override
  int? get bestContinuousLearnDays;
  @override
  int? get purchaseSwitch;
  @override
  @JsonKey(ignore: true)
  _$$_SpaceMallResourceDataCopyWith<_$_SpaceMallResourceData> get copyWith =>
      throw _privateConstructorUsedError;
}

CategoryButton _$CategoryButtonFromJson(Map<String, dynamic> json) {
  return _CategoryButton.fromJson(json);
}

/// @nodoc
mixin _$CategoryButton {
  String? get iconUrl => throw _privateConstructorUsedError;
  String? get buttonName => throw _privateConstructorUsedError;
  String? get buttonSelectUrl => throw _privateConstructorUsedError;
  String? get buttonUnSelectUrl => throw _privateConstructorUsedError;
  int? get categoryId => throw _privateConstructorUsedError;
  String? get spineSkinCategoryName => throw _privateConstructorUsedError;
  bool? get reachBuyThreshold => throw _privateConstructorUsedError;
  String? get buyGoodsTips => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CategoryButtonCopyWith<CategoryButton> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CategoryButtonCopyWith<$Res> {
  factory $CategoryButtonCopyWith(
          CategoryButton value, $Res Function(CategoryButton) then) =
      _$CategoryButtonCopyWithImpl<$Res, CategoryButton>;
  @useResult
  $Res call(
      {String? iconUrl,
      String? buttonName,
      String? buttonSelectUrl,
      String? buttonUnSelectUrl,
      int? categoryId,
      String? spineSkinCategoryName,
      bool? reachBuyThreshold,
      String? buyGoodsTips});
}

/// @nodoc
class _$CategoryButtonCopyWithImpl<$Res, $Val extends CategoryButton>
    implements $CategoryButtonCopyWith<$Res> {
  _$CategoryButtonCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? iconUrl = freezed,
    Object? buttonName = freezed,
    Object? buttonSelectUrl = freezed,
    Object? buttonUnSelectUrl = freezed,
    Object? categoryId = freezed,
    Object? spineSkinCategoryName = freezed,
    Object? reachBuyThreshold = freezed,
    Object? buyGoodsTips = freezed,
  }) {
    return _then(_value.copyWith(
      iconUrl: freezed == iconUrl
          ? _value.iconUrl
          : iconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonName: freezed == buttonName
          ? _value.buttonName
          : buttonName // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonSelectUrl: freezed == buttonSelectUrl
          ? _value.buttonSelectUrl
          : buttonSelectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonUnSelectUrl: freezed == buttonUnSelectUrl
          ? _value.buttonUnSelectUrl
          : buttonUnSelectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      spineSkinCategoryName: freezed == spineSkinCategoryName
          ? _value.spineSkinCategoryName
          : spineSkinCategoryName // ignore: cast_nullable_to_non_nullable
              as String?,
      reachBuyThreshold: freezed == reachBuyThreshold
          ? _value.reachBuyThreshold
          : reachBuyThreshold // ignore: cast_nullable_to_non_nullable
              as bool?,
      buyGoodsTips: freezed == buyGoodsTips
          ? _value.buyGoodsTips
          : buyGoodsTips // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CategoryButtonCopyWith<$Res>
    implements $CategoryButtonCopyWith<$Res> {
  factory _$$_CategoryButtonCopyWith(
          _$_CategoryButton value, $Res Function(_$_CategoryButton) then) =
      __$$_CategoryButtonCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? iconUrl,
      String? buttonName,
      String? buttonSelectUrl,
      String? buttonUnSelectUrl,
      int? categoryId,
      String? spineSkinCategoryName,
      bool? reachBuyThreshold,
      String? buyGoodsTips});
}

/// @nodoc
class __$$_CategoryButtonCopyWithImpl<$Res>
    extends _$CategoryButtonCopyWithImpl<$Res, _$_CategoryButton>
    implements _$$_CategoryButtonCopyWith<$Res> {
  __$$_CategoryButtonCopyWithImpl(
      _$_CategoryButton _value, $Res Function(_$_CategoryButton) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? iconUrl = freezed,
    Object? buttonName = freezed,
    Object? buttonSelectUrl = freezed,
    Object? buttonUnSelectUrl = freezed,
    Object? categoryId = freezed,
    Object? spineSkinCategoryName = freezed,
    Object? reachBuyThreshold = freezed,
    Object? buyGoodsTips = freezed,
  }) {
    return _then(_$_CategoryButton(
      iconUrl: freezed == iconUrl
          ? _value.iconUrl
          : iconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonName: freezed == buttonName
          ? _value.buttonName
          : buttonName // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonSelectUrl: freezed == buttonSelectUrl
          ? _value.buttonSelectUrl
          : buttonSelectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonUnSelectUrl: freezed == buttonUnSelectUrl
          ? _value.buttonUnSelectUrl
          : buttonUnSelectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      spineSkinCategoryName: freezed == spineSkinCategoryName
          ? _value.spineSkinCategoryName
          : spineSkinCategoryName // ignore: cast_nullable_to_non_nullable
              as String?,
      reachBuyThreshold: freezed == reachBuyThreshold
          ? _value.reachBuyThreshold
          : reachBuyThreshold // ignore: cast_nullable_to_non_nullable
              as bool?,
      buyGoodsTips: freezed == buyGoodsTips
          ? _value.buyGoodsTips
          : buyGoodsTips // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CategoryButton implements _CategoryButton {
  const _$_CategoryButton(
      {this.iconUrl,
      this.buttonName,
      this.buttonSelectUrl,
      this.buttonUnSelectUrl,
      this.categoryId,
      this.spineSkinCategoryName,
      this.reachBuyThreshold,
      this.buyGoodsTips});

  factory _$_CategoryButton.fromJson(Map<String, dynamic> json) =>
      _$$_CategoryButtonFromJson(json);

  @override
  final String? iconUrl;
  @override
  final String? buttonName;
  @override
  final String? buttonSelectUrl;
  @override
  final String? buttonUnSelectUrl;
  @override
  final int? categoryId;
  @override
  final String? spineSkinCategoryName;
  @override
  final bool? reachBuyThreshold;
  @override
  final String? buyGoodsTips;

  @override
  String toString() {
    return 'CategoryButton(iconUrl: $iconUrl, buttonName: $buttonName, buttonSelectUrl: $buttonSelectUrl, buttonUnSelectUrl: $buttonUnSelectUrl, categoryId: $categoryId, spineSkinCategoryName: $spineSkinCategoryName, reachBuyThreshold: $reachBuyThreshold, buyGoodsTips: $buyGoodsTips)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CategoryButton &&
            (identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl) &&
            (identical(other.buttonName, buttonName) ||
                other.buttonName == buttonName) &&
            (identical(other.buttonSelectUrl, buttonSelectUrl) ||
                other.buttonSelectUrl == buttonSelectUrl) &&
            (identical(other.buttonUnSelectUrl, buttonUnSelectUrl) ||
                other.buttonUnSelectUrl == buttonUnSelectUrl) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.spineSkinCategoryName, spineSkinCategoryName) ||
                other.spineSkinCategoryName == spineSkinCategoryName) &&
            (identical(other.reachBuyThreshold, reachBuyThreshold) ||
                other.reachBuyThreshold == reachBuyThreshold) &&
            (identical(other.buyGoodsTips, buyGoodsTips) ||
                other.buyGoodsTips == buyGoodsTips));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      iconUrl,
      buttonName,
      buttonSelectUrl,
      buttonUnSelectUrl,
      categoryId,
      spineSkinCategoryName,
      reachBuyThreshold,
      buyGoodsTips);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CategoryButtonCopyWith<_$_CategoryButton> get copyWith =>
      __$$_CategoryButtonCopyWithImpl<_$_CategoryButton>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CategoryButtonToJson(
      this,
    );
  }
}

abstract class _CategoryButton implements CategoryButton {
  const factory _CategoryButton(
      {final String? iconUrl,
      final String? buttonName,
      final String? buttonSelectUrl,
      final String? buttonUnSelectUrl,
      final int? categoryId,
      final String? spineSkinCategoryName,
      final bool? reachBuyThreshold,
      final String? buyGoodsTips}) = _$_CategoryButton;

  factory _CategoryButton.fromJson(Map<String, dynamic> json) =
      _$_CategoryButton.fromJson;

  @override
  String? get iconUrl;
  @override
  String? get buttonName;
  @override
  String? get buttonSelectUrl;
  @override
  String? get buttonUnSelectUrl;
  @override
  int? get categoryId;
  @override
  String? get spineSkinCategoryName;
  @override
  bool? get reachBuyThreshold;
  @override
  String? get buyGoodsTips;
  @override
  @JsonKey(ignore: true)
  _$$_CategoryButtonCopyWith<_$_CategoryButton> get copyWith =>
      throw _privateConstructorUsedError;
}
