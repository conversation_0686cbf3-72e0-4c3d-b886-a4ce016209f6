// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_home_currentskin_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SpaceHomeCurrentskinData _$$_SpaceHomeCurrentskinDataFromJson(
        Map<String, dynamic> json) =>
    _$_SpaceHomeCurrentskinData(
      userId: json['userId'] as int?,
      userName: json['userName'] as String?,
      dressUps: (json['dressUps'] as List<dynamic>?)
          ?.map((e) => CurrentUserDressUps.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SpaceHomeCurrentskinDataToJson(
        _$_SpaceHomeCurrentskinData instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userName': instance.userName,
      'dressUps': instance.dressUps,
    };

_$_CurrentUserDressUps _$$_CurrentUserDressUpsFromJson(
        Map<String, dynamic> json) =>
    _$_CurrentUserDressUps(
      dressUpId: json['dressUpId'] as int?,
      dressUpCategoryId: json['dressUpCategoryId'] as int?,
      spineDressUpCategoryName: json['spineDressUpCategoryName'] as String?,
      dressShowUrl: json['dressShowUrl'] as String?,
      resourcesZipUrl: json['resourcesZipUrl'] as String?,
      price: json['price'] as String?,
      status: json['status'] as int?,
      children: (json['children'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : CurrentUserDressUps.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CurrentUserDressUpsToJson(
        _$_CurrentUserDressUps instance) =>
    <String, dynamic>{
      'dressUpId': instance.dressUpId,
      'dressUpCategoryId': instance.dressUpCategoryId,
      'spineDressUpCategoryName': instance.spineDressUpCategoryName,
      'dressShowUrl': instance.dressShowUrl,
      'resourcesZipUrl': instance.resourcesZipUrl,
      'price': instance.price,
      'status': instance.status,
      'children': instance.children,
    };
