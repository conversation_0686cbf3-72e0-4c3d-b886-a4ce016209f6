// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_home_resource_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SpaceHomeResourceData _$$_SpaceHomeResourceDataFromJson(
        Map<String, dynamic> json) =>
    _$_SpaceHomeResourceData(
      memberBonesZipUrl: json['memberBonesZipUrl'] as String?,
      backGroundZipUrl: json['backGroundZipUrl'] as String?,
      likeZipUrl: json['likeZipUrl'] as String?,
      prickZipUrl: json['prickZipUrl'] as String?,
      boxZipUrl: json['boxZipUrl'] as String?,
      ruleZipUrl: json['ruleZipUrl'] as String?,
      learnButtonInfo: json['learnButtonInfo'] == null
          ? null
          : ButtonInfo.fromJson(
              json['learnButtonInfo'] as Map<String, dynamic>),
      ruleButtonInfo: json['ruleButtonInfo'] == null
          ? null
          : ButtonInfo.fromJson(json['ruleButtonInfo'] as Map<String, dynamic>),
      historyPerformanceButtonInfo: json['historyPerformanceButtonInfo'] == null
          ? null
          : ButtonInfo.fromJson(
              json['historyPerformanceButtonInfo'] as Map<String, dynamic>),
      teamContributionDetailButtonInfo:
          json['teamContributionDetailButtonInfo'] == null
              ? null
              : ButtonInfo.fromJson(json['teamContributionDetailButtonInfo']
                  as Map<String, dynamic>),
      myDressUpButtonInfo: json['myDressUpButtonInfo'] == null
          ? null
          : ButtonInfo.fromJson(
              json['myDressUpButtonInfo'] as Map<String, dynamic>),
      goldTeamIconInfo: json['goldTeamIconInfo'] == null
          ? null
          : DTeamIconInfo.fromJson(
              json['goldTeamIconInfo'] as Map<String, dynamic>),
      legendTeamIconInfo: json['legendTeamIconInfo'] == null
          ? null
          : DTeamIconInfo.fromJson(
              json['legendTeamIconInfo'] as Map<String, dynamic>),
      defaultSkinList: (json['defaultSkinList'] as List<dynamic>?)
          ?.map((e) => DefaultSkinList.fromJson(e as Map<String, dynamic>))
          .toList(),
      ownAndDefaultSkinList: (json['ownAndDefaultSkinList'] as List<dynamic>?)
          ?.map((e) => DefaultSkinList.fromJson(e as Map<String, dynamic>))
          .toList(),
      flowerZipUrl: json['flowerZipUrl'] as String?,
    );

Map<String, dynamic> _$$_SpaceHomeResourceDataToJson(
        _$_SpaceHomeResourceData instance) =>
    <String, dynamic>{
      'memberBonesZipUrl': instance.memberBonesZipUrl,
      'backGroundZipUrl': instance.backGroundZipUrl,
      'likeZipUrl': instance.likeZipUrl,
      'prickZipUrl': instance.prickZipUrl,
      'boxZipUrl': instance.boxZipUrl,
      'ruleZipUrl': instance.ruleZipUrl,
      'learnButtonInfo': instance.learnButtonInfo,
      'ruleButtonInfo': instance.ruleButtonInfo,
      'historyPerformanceButtonInfo': instance.historyPerformanceButtonInfo,
      'teamContributionDetailButtonInfo':
          instance.teamContributionDetailButtonInfo,
      'myDressUpButtonInfo': instance.myDressUpButtonInfo,
      'goldTeamIconInfo': instance.goldTeamIconInfo,
      'legendTeamIconInfo': instance.legendTeamIconInfo,
      'defaultSkinList': instance.defaultSkinList,
      'ownAndDefaultSkinList': instance.ownAndDefaultSkinList,
      'flowerZipUrl': instance.flowerZipUrl,
    };

_$_DefaultSkinList _$$_DefaultSkinListFromJson(Map<String, dynamic> json) =>
    _$_DefaultSkinList(
      skinId: json['skinId'] as int?,
      dressShowUrl: json['dressShowUrl'] as String?,
      categoryShowName: json['categoryShowName'] as String?,
      categoryShowCode: json['categoryShowCode'] as int?,
      resourcesZipUrl: json['resourcesZipUrl'] as String?,
    );

Map<String, dynamic> _$$_DefaultSkinListToJson(_$_DefaultSkinList instance) =>
    <String, dynamic>{
      'skinId': instance.skinId,
      'dressShowUrl': instance.dressShowUrl,
      'categoryShowName': instance.categoryShowName,
      'categoryShowCode': instance.categoryShowCode,
      'resourcesZipUrl': instance.resourcesZipUrl,
    };

_$_DTeamIconInfo _$$_DTeamIconInfoFromJson(Map<String, dynamic> json) =>
    _$_DTeamIconInfo(
      selectedIconUrl: json['selectedIconUrl'] as String?,
      unselectIconUrl: json['unselectIconUrl'] as String?,
      iconName: json['iconName'] as String?,
    );

Map<String, dynamic> _$$_DTeamIconInfoToJson(_$_DTeamIconInfo instance) =>
    <String, dynamic>{
      'selectedIconUrl': instance.selectedIconUrl,
      'unselectIconUrl': instance.unselectIconUrl,
      'iconName': instance.iconName,
    };

_$_ButtonInfo _$$_ButtonInfoFromJson(Map<String, dynamic> json) =>
    _$_ButtonInfo(
      iconUrl: json['iconUrl'] as String?,
      buttonName: json['buttonName'] as String?,
      buttonUrl: json['buttonUrl'] as String?,
    );

Map<String, dynamic> _$$_ButtonInfoToJson(_$_ButtonInfo instance) =>
    <String, dynamic>{
      'iconUrl': instance.iconUrl,
      'buttonName': instance.buttonName,
      'buttonUrl': instance.buttonUrl,
    };
