// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_home_team_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SpaceHomeTeamData _$$_SpaceHomeTeamDataFromJson(Map<String, dynamic> json) =>
    _$_SpaceHomeTeamData(
      notTeamsTip: json['notTeamsTip'] as String?,
      teams: (json['teams'] as List<dynamic>?)
          ?.map((e) => Team.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SpaceHomeTeamDataToJson(
        _$_SpaceHomeTeamData instance) =>
    <String, dynamic>{
      'notTeamsTip': instance.notTeamsTip,
      'teams': instance.teams,
    };

_$_Team _$$_TeamFromJson(Map<String, dynamic> json) => _$_Team(
      className: json['className'] as String?,
      teamId: json['teamId'] as int?,
      courseInfo: json['courseInfo'] == null
          ? null
          : CourseInfo.fromJson(json['courseInfo'] as Map<String, dynamic>),
      classInfo: json['classInfo'] == null
          ? null
          : ClassInfo.fromJson(json['classInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_TeamToJson(_$_Team instance) => <String, dynamic>{
      'className': instance.className,
      'teamId': instance.teamId,
      'courseInfo': instance.courseInfo,
      'classInfo': instance.classInfo,
    };

_$_CourseInfo _$$_CourseInfoFromJson(Map<String, dynamic> json) =>
    _$_CourseInfo(
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      courseName: json['courseName'] as String?,
    );

Map<String, dynamic> _$$_CourseInfoToJson(_$_CourseInfo instance) =>
    <String, dynamic>{
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'courseName': instance.courseName,
    };

_$_ClassInfo _$$_ClassInfoFromJson(Map<String, dynamic> json) => _$_ClassInfo(
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
      className: json['className'] as String?,
    );

Map<String, dynamic> _$$_ClassInfoToJson(_$_ClassInfo instance) =>
    <String, dynamic>{
      'classId': instance.classId,
      'classKey': instance.classKey,
      'className': instance.className,
    };
