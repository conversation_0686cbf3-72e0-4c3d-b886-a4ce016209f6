import 'package:jojo_flutter_base/base.dart';

part 'space_home_contributions_data.freezed.dart';
part 'space_home_contributions_data.g.dart';

@freezed
class SpaceHomeContributionsData with _$SpaceHomeContributionsData {
  const factory SpaceHomeContributionsData({
    int? teamId,
    int? classId,
    int? courseId,
    List<ContributionDetail>? contributions,
  }) = _SpaceHomeContributionsData;

  factory SpaceHomeContributionsData.fromJson(Map<String, dynamic> json) =>
      _$SpaceHomeContributionsDataFromJson(json);
}

@freezed
class ContributionDetail with _$ContributionDetail {
  const factory ContributionDetail({
    int? userId,
    String? userName,
    String? userAvatar,
    int? contributionType,
    int? contributionVal,
    String? contributionDesc,
    int? contributionDate,
  }) = _ContributionDetail;

  factory ContributionDetail.fromJson(Map<String, dynamic> json) =>
      _$ContributionDetailFromJson(json);
}
