import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/controller.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_func_entrance_item_widget.dart';

import '../../../common/host_env/host_env.dart';
import '../../../page.dart';
import '../state.dart';

class SpaceHomeFuncEntranceWidget extends StatefulWidget {
  final SpaceHomePageState state;
  const SpaceHomeFuncEntranceWidget({Key? key, required this.state})
      : super(key: key);

  @override
  SpaceHomeFunctionEntranceWidgetState createState() =>
      SpaceHomeFunctionEntranceWidgetState();
}

class SpaceHomeFunctionEntranceWidgetState
    extends State<SpaceHomeFuncEntranceWidget> {
  String router = '';
  String buttonName = '';
  String iconUrl = '';
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    router = widget.state.resourceData?.myDressUpButtonInfo?.buttonUrl ?? '';
    buttonName =
        widget.state.resourceData?.myDressUpButtonInfo?.buttonName ?? '我的装扮';
    iconUrl = widget.state.resourceData?.myDressUpButtonInfo?.iconUrl ?? '';
    return Container(
        alignment: Alignment.center,
        height: 82.rdp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(0.0.rdp),
          ),
          color: Colors.transparent,
        ),
        child: Stack(
          alignment: Alignment.centerLeft,
          children: [
            Positioned(
              bottom: 10.rdp,
              left: 0,
              child: GestureDetector(
                onTap: () {
                  RunEnv.sensorsTrack('\$AppClick', {
                    '\$element_name': '功能入口点击',
                    'custom_state': 'zbrk',
                    ...?pageController?.commonSensorParams()
                  });
                  RunEnv.jumpLink(router);
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(
                      Radius.circular(33.rdp),
                    ),
                    color: Colors.white,
                  ),
                  alignment: Alignment.topCenter,
                  width: 63.rdp,
                  height: 63.rdp,
                  child: ImageNetworkCached(
                    imageUrl: iconUrl,
                    fit: BoxFit.contain,
                    width: 48.rdp,
                    height: 48.rdp,
                    borderRadius: 33.rdp,
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 8.rdp,
              left: 5.rdp,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(
                    Radius.circular(33.rdp),
                  ),
                  color: Colors.white,
                ),
                width: 56.rdp,
                height: 22.rdp,
                child: Center(
                    child: Text(
                  buttonName,
                  style: TextStyle(
                    fontSize: 12.rdp,
                    color: HexColor('#544300'),
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                )),
              ),
            ),
            Positioned(
                bottom: 8.rdp,
                right: 0,
                child: SpaceHomeFuncEntranceItemWidget(
                  state: widget.state,
                ))
          ],
        ));
  }

  SpaceHomePageCtrl? get pageController =>
      mounted ? context.read<SpaceHomePageCtrl>() : null;
}
