import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojoread_flutter_space_pkg/common/config/config.dart';
import 'package:jojoread_flutter_space_pkg/common/host_env/host_env.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/controller.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/component_switching_state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/back_widget.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

/// 规则介绍
class RuleIntroWidget extends StatefulWidget {
  const RuleIntroWidget({
    Key? key,
  }) : super(key: key);

  @override
  State<RuleIntroWidget> createState() => _RuleIntroWidgetState();
}

class _RuleIntroWidgetState extends State<RuleIntroWidget>
    with WidgetsBindingObserver {
  final SwiperController _swiperController = SwiperController();
  late JoJoSpineAnimationController animationController;

  /// spine动画控制器
  @override
  void initState() {
    super.initState();

    RunEnv.sensorsTrack('ElementView', {
      'c_element_name': '新手引导弹窗曝光',
      'custom_state': 'xsyd',
      ...context.read<SpaceHomePageCtrl>().commonSensorParams(),
    });

    /// 添加activity生命周期监听函数
    WidgetsBinding.instance.addObserver(this);
    var isFlag1 = false;
    var isFlag2 = false;
    final ctr = context.read<SpaceHomePageCtrl>();
    animationController = JoJoSpineAnimationController(
      playProgressListener: (time) {
        final _totalSeconds = time.inMilliseconds;

        if (_totalSeconds == 11000 && isFlag1 == false) {
          isFlag1 = true;
          _swiperController.next();
        } else if (_totalSeconds == 20000 && isFlag2 == false) {
          isFlag2 = true;
          _swiperController.next();
        }
      },
      isHasMusic: true,
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _swiperController.dispose();
    animationController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      animationController.pauseAnimation();
    } else if (state == AppLifecycleState.resumed) {
      animationController.resumeAnimation();
    }
  }

  @override
  Widget build(BuildContext context) {
    final ctr = context.read<SpaceHomePageCtrl>();
    final guideRuleData = ctr.state.guideRuleData;

    return VisibilityObserve(
      onShow: () {
        animationController.resumeAnimation();
      },
      onHidden: () {
        animationController.pauseAnimation();
      },
      child: Stack(
        children: [
          ImageAssetWeb(
            assetName: AssetsImg.INTRO_BG,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
            package: Config.package,
          ),
          Center(
            child: Stack(
              children: [
                SizedBox(
                  width: screen.screenWidth * 0.9,
                  height: 427.rdp,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 335.rdp,
                        height: 290.rdp,
                        margin: EdgeInsets.only(top: 22.rdp),
                        decoration: BoxDecoration(
                          color: HexColor('#F9F5FF'),
                          borderRadius:
                              BorderRadius.all(Radius.circular(52.rdp)),
                        ),
                        child: guideRuleData.ruleImageList != null
                            ? Swiper(
                                autoplay: false,
                                controller: _swiperController,
                                physics:
                                    const NeverScrollableScrollPhysics(), // 禁用滑动
                                scrollDirection: Axis.horizontal,
                                itemCount: guideRuleData.ruleImageList!.length,
                                itemBuilder: (BuildContext context, int index) {
                                  String _imageUrl =
                                      guideRuleData.ruleImageList?[index] ?? '';
                                  if (_imageUrl == '') {
                                    return const SizedBox();
                                  }
                                  bool isHttp = _imageUrl.startsWith('http');
                                  return ClipRRect(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(52.rdp),
                                    ),
                                    child: isHttp
                                        ? ImageNetworkCached(
                                            imageUrl: _imageUrl,
                                            width: 335.rdp,
                                            height: 290.rdp,
                                          )
                                        : Image(
                                            image: FileImage(File(_imageUrl)),
                                            width: 335.rdp,
                                            height: 290.rdp,
                                            fit: BoxFit.cover,
                                            frameBuilder: (context, child,
                                                frame, wasSynchronouslyLoaded) {
                                              // 如果图像在同步加载中完成，则直接返回图像组件
                                              if (frame == null) {
                                                return const SizedBox();
                                              }
                                              return child;
                                            },
                                          ),
                                  );
                                },
                              )
                            : const SizedBox(),
                      )
                    ],
                  ),
                ),
                if (guideRuleData.spineData != null)
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: SizedBox(
                      width: 216.rdp,
                      height: 216.rdp,
                      child: JoJoSpineAnimationWidget(
                        guideRuleData.spineData!.atlasFile,
                        guideRuleData.spineData!.skelFile,
                        LoadMode.file,
                        animationController,
                        onInitialized: (controller) {
                          controller.skeleton.setScaleX(0.83);
                          controller.skeleton.setScaleY(0.83);
                          animationController.playAnimation(
                            JoJoSpineAnimation(
                                animaitonName: "play",
                                trackIndex: 0,
                                loop: true,
                                delay: 0,
                                listener: (AnimationEventType type) {
                                  if (type == AnimationEventType.complete) {
                                    animationController.pauseAnimation();
                                    ctr.changePage(FeatureSection.skins);
                                  }
                                }),
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Positioned(
            right: 20.rdp,
            bottom: 40.rdp,
            child: GestureDetector(
              onTap: () {
                RunEnv.sensorsTrack('\$AppClick', {
                  '\$element_name': '新手引导弹窗按钮点击',
                  'custom_state': 'xsyd',
                  ...ctr.commonSensorParams(),
                });

                animationController.pauseAnimation();
                ctr.changePage(FeatureSection.skins);
              },
              child: Container(
                width: 88.rdp,
                height: 34.rdp,
                decoration: BoxDecoration(
                  color: HexColor('#FFFFFF'),
                  borderRadius: BorderRadius.all(Radius.circular(20.rdp)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ImageAssetWeb(
                      assetName: AssetsImg.SKIP,
                      width: 20.rdp,
                      height: 20.rdp,
                      package: Config.package,
                    ),
                    SizedBox(width: 2.rdp),
                    Text(
                      '跳过',
                      style: TextStyle(
                        color: HexColor('#8FAEBE'),
                        fontSize: 16.rdp,
                        fontWeight: FontWeight.w400,
                        height: 1.2,
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
          const BackWidget(),
        ],
      ),
    );
  }
}
