import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_func_entrance_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_info_contribution_empty_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_info_logo_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_info_week_challenge_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_info_contribution_widget.dart';
import '../../../utils/file_util.dart';
import '../model/space_home_team_data.dart';
import '../state.dart';

class SpaceHomeInfoWidget extends StatefulWidget {
  final SpaceHomePageState state;
  final double miniHeight;
  const SpaceHomeInfoWidget({
    Key? key,
    required this.state,
    required this.miniHeight,
  }) : super(key: key);

  @override
  SpaceHomeInfoWidgetState createState() => SpaceHomeInfoWidgetState();
}

class SpaceHomeInfoWidgetState extends State<SpaceHomeInfoWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //有战队信息
    List<Team>? teams = widget.state.teamData?.teams ?? [];
    bool hasTeamInfo = teams.isNotEmpty;
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      double minHeight = widget.miniHeight / constraints.maxHeight;
      double maxHeight = hasTeamInfo == false
          ? (minHeight + 0.001)
          : (550.rdp) / screen.screenHeight;
      if (minHeight >= maxHeight) {
        minHeight = maxHeight - 0.1;
      }
      if (isPadPortrait()) {
        maxHeight = 0.55;
      }
      return DraggableScrollableSheet(
        // 82+135+14+14+48+210=503
        //注意 maxChildSize >= initialChildSize >= minChildSize
        //初始化占用父容器高度
        initialChildSize: minHeight,
        //占用父组件的最小高度
        minChildSize: minHeight,
        //占用父组件的最大高度
        maxChildSize: maxHeight,
        //是否应扩展以填充其父级中的可用空间默认true 父组件是Center时设置为false,才会实现center布局，但滚动效果是向两边展开
        expand: true,
        //true：触发滚动则滚动到maxChildSize或者minChildSize，不在跟随手势滚动距离 false:滚动跟随手势滚动距离
        snap: true,
        // 当snapSizes接收的是一个数组[],数组内的数字必须是升序，而且取值范围必须在 minChildSize,maxChildSize之间
        //作用是可以控制每次滚动部件占父容器的高度，此时expand: true,
        snapSizes: [minHeight, maxHeight],
        snapAnimationDuration:
            const Duration(milliseconds: 150), // 设置动画持续时间为150毫秒
        builder: (BuildContext context, ScrollController scrollController) {
          return Padding(
              padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
              child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24.0.rdp),
                      topRight: Radius.circular(24.0.rdp),
                    ),
                    color: Colors.transparent,
                  ),
                  child: OverflowBox(
                      child: SingleChildScrollView(
                    clipBehavior: Clip.none,
                    controller: scrollController,
                    physics: const NeverScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        ///功能模块
                        SpaceHomeFuncEntranceWidget(
                          state: widget.state,
                        ),
                        SizedBox(height: 14.rdp),
                        if (hasTeamInfo)
                          Column(children: [
                            ///本周挑战模块
                            SpaceHomeInfoWeekChallengeWidget(
                              state: widget.state,
                            ),
                            SizedBox(height: 14.rdp),

                            ///贡献榜模块
                            SpaceHomeInfocontributionWidget(
                              state: widget.state,
                            ),
                            SizedBox(
                              height: 5.rdp,
                            ),
                            const SpaceHomeInfoLogoWidget() //logo模块
                          ])
                        else
                          Column(children: [
                            ///空白模块
                            SpaceHomeInfocontributionEmptyWidget(
                                state: widget.state),
                            SizedBox(
                              height: 5.rdp,
                            ),
                            const SpaceHomeInfoLogoWidget() //logo模块
                          ])
                      ],
                    ),
                  ))));
        },
      );
    });
  }
}
