import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';

import '../../../common/config/config.dart';
import '../../../static/img.dart';
import '../state.dart';

class SpaceHomeInfocontributionEmptyWidget extends StatefulWidget {
  final SpaceHomePageState state;
  const SpaceHomeInfocontributionEmptyWidget({
    Key? key,
    required this.state,
  }) : super(key: key);

  @override
  SpaceHomeInfocontributionEmptyWidgetState createState() =>
      SpaceHomeInfocontributionEmptyWidgetState();
}

class SpaceHomeInfocontributionEmptyWidgetState
    extends State<SpaceHomeInfocontributionEmptyWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        ImageAssetWeb(
          assetName: AssetsImg.SPACE_HOME_CONTRIBUTION_EMPTY,
          fit: BoxFit.fill,
          package: Config.package,
        ),
        Positioned(
          bottom: 30.rdp,
          child: SizedBox(
            width: 160.rdp,
            child: Text(
              widget.state.teamData?.notTeamsTip ?? '',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16.rdp,
                color: HexColor('#664314'),
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        )
      ],
    );
  }
}
