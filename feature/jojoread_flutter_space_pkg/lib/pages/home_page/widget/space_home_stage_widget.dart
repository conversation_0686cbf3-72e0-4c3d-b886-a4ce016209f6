import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_skin_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/common/config/config.dart';
import 'package:jojoread_flutter_space_pkg/common/host_env/host_env.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/controller.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_group_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';
import 'package:jojoread_flutter_space_pkg/utils/file_util.dart';
import 'space_home_num_widget.dart';
import 'package:jojo_flutter_base/static/img.dart' as baseImg;

/// 舞台widget
class SpaceHomeStageWidget extends StatefulWidget {
  final StageData stageData;
  final SpaceHomeGroupData? groupData;
  final String? bgFile; //背景文件
  final double bgHeight; //背景高度
  final bool isLoading;

  const SpaceHomeStageWidget({
    Key? key,
    required this.stageData,
    required this.groupData,
    required this.bgFile,
    required this.bgHeight,
    required this.isLoading,
  }) : super(key: key);

  @override
  SpaceHomeInfoWidgetState createState() => SpaceHomeInfoWidgetState();
}

class SpaceHomeInfoWidgetState extends State<SpaceHomeStageWidget> {
  final double containerWidth = screen.screenWidth;

  bool isLeftClick = false;
  bool isRightClick = false;

  var personScale = 1.0; // 人物缩放比例

  var isLoading = true;

  /// spine动画控制器
  Map<PersonType, JoJoSpineAnimationController> spineControllerMap = {
    PersonType.left: JoJoSpineAnimationController(),
    PersonType.center: JoJoSpineAnimationController(),
    PersonType.right: JoJoSpineAnimationController(),
  };

  //点赞动画控制器
  Map<PersonType, JoJoSpineAnimationController> likeControllerMap = {
    PersonType.left: JoJoSpineAnimationController(),
    PersonType.right: JoJoSpineAnimationController(),
  };
  //戳一戳动画控制器
  Map<PersonType, JoJoSpineAnimationController> touchControllerMap = {
    PersonType.left: JoJoSpineAnimationController(),
    PersonType.right: JoJoSpineAnimationController(),
  };

  //舞台动画控制器
  JoJoSpineAnimationController stageController = JoJoSpineAnimationController();

  //任务换肤状态
  Map<PersonType, SkinStatus> skinStatusMap = {
    PersonType.left: SkinStatus.start,
    PersonType.right: SkinStatus.start,
    PersonType.center: SkinStatus.start,
  };

  Timer? _timer;

  @override
  void initState() {
    super.initState();
    if (isPadPortrait()) {
      //pad 竖屏
      personScale = 1.5;
    }
    isLoading = widget.isLoading;

    if ((widget.stageData.otherPersonSpineData?.length ?? 0) == 0) {
      //没有其他人的时候,删除左右两个人
      skinStatusMap.remove(PersonType.left);
      skinStatusMap.remove(PersonType.right);
    }
  }

  @override
  void didUpdateWidget(covariant SpaceHomeStageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if ((widget.stageData.otherPersonSpineData?.length ?? 0) > 0) {
      //多人情况
      if ((oldWidget.stageData.otherPersonSpineData?.length ?? 0) == 0) {
        //如果之前是单人变成多人,则需要重置状态
        skinStatusMap[PersonType.left] = SkinStatus.start;
        skinStatusMap[PersonType.right] = SkinStatus.start;
      }
    } else {
      //没有其他人的时候,删除左右两个人
      skinStatusMap.remove(PersonType.left);
      skinStatusMap.remove(PersonType.right);
    }

    //记录我的人物是否有学习状态变化
    var nowState = widget.stageData.mineSpineData?.teamMember?.userStatus;
    if (oldWidget.stageData.mineSpineData?.spineData ==
            widget.stageData.mineSpineData?.spineData &&
        oldWidget.stageData.mineSpineData?.teamMember?.userStatus != nowState) {
      resetSkinAnimation();
    }

    isLoading = widget.isLoading;
  }

  //点击小人
  void _clickPerson(PersonType pos) {
    if (pos == PersonType.center) return;
    if (pos == PersonType.left && isLeftClick) return;
    if (pos == PersonType.right && isRightClick) return;
    //设置点击状态
    if (pos == PersonType.left) {
      isLeftClick = true;
    } else {
      isRightClick = true;
    }
    setState(() {});
    //设置延时
    Future.delayed(const Duration(seconds: 3), () {
      setState(() {
        if (pos == PersonType.left) {
          isLeftClick = false;
        } else {
          isRightClick = false;
        }
      });
    });
  }

  //点击播放spine动画
  void _clickAnimation(PersonType pos, StagePersonData? data) {
    if (data == null) return;
    if (data.teamMember?.userStatus == 1) {
      //点赞动画
      likeControllerMap[pos]?.playAnimation(JoJoSpineAnimation(
        animaitonName: "play",
        trackIndex: 0,
        loop: false,
      ));
    } else {
      //戳一戳动画
      touchControllerMap[pos]?.playAnimation(JoJoSpineAnimation(
        animaitonName: "play",
        trackIndex: 0,
        loop: false,
      ));
    }
    RunEnv.sensorsTrack('\$AppClick', {
      '\$element_name': '舞台区域功能点击',
      'custom_state': data.teamMember?.userStatus == 1 ? 'like' : 'remind',
      ...?pageController?.commonSensorParams(),
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: containerWidth,
      height: widget.bgHeight,
      child: Stack(
        children: [
          //背景图
          if (widget.bgFile != null)
            Image.file(
              File(widget.bgFile ?? ''),
              fit: BoxFit.cover,
              width: containerWidth,
              height: widget.bgHeight,
              errorBuilder: (BuildContext context, Object exception,
                  StackTrace? stackTrace) {
                return Container(
                  color: HexColor('#FFF6E0'),
                );
              },
            ),
          //背景动画
          SizedBox(
            width: containerWidth,
            height: widget.bgHeight,
            child: JoJoSpineAnimationWidget(
              widget.stageData.stageSpineData?.atlasFile ?? "",
              widget.stageData.stageSpineData?.skelFile ?? "",
              LoadMode.file,
              stageController,
              onInitialized: (controller) {
                //播放舞台动画
                stageController.playAnimation(JoJoSpineAnimation(
                    animaitonName: "play", trackIndex: 0, loop: true));
              },
            ),
          ),
          Center(
            child: SizedBox(
              width: containerWidth / personScale,
              child: Stack(
                children: [
                  // 居左的控件
                  if ((widget.stageData.otherPersonSpineData?.length ?? 0) > 0)
                    Positioned(
                      left: -25.rdp,
                      bottom: 90.rdp,
                      child: _buildMineWidget(
                          widget.stageData.otherPersonSpineData![0],
                          PersonType.left),
                    ),
                  // 居右的控件
                  if ((widget.stageData.otherPersonSpineData?.length ?? 0) > 1)
                    Positioned(
                      right: -25.rdp,
                      bottom: 90.rdp,
                      child: _buildMineWidget(
                          widget.stageData.otherPersonSpineData![1],
                          PersonType.right),
                    ),
                  // 居中的控件
                  Positioned(
                    right:
                        (containerWidth / personScale - 300.rdp * personScale) /
                            2,
                    bottom: 80.rdp,
                    child: _buildMineWidget(
                        widget.stageData.mineSpineData, PersonType.center),
                  ),
                  if (isLoading)
                    Align(
                      heightFactor: 0.8,
                      child: ImageAssetWeb(
                        assetName: baseImg.AssetsImg.SXZ,
                        width: 110.rdp,
                        package: 'jojo_flutter_base',
                      ),
                    )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  //我的小人
  _buildMineWidget(StagePersonData? personData, PersonType pos) {
    return Stack(
      children: [
        SizedBox(
          width: pos == PersonType.center
              ? 300.rdp * personScale
              : 200.rdp * personScale,
          child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (personData?.teamMember?.learningInfo != null)
                  Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ImageNetworkCached(
                          imageUrl:
                              personData?.teamMember?.learningInfo?.icon ?? "",
                          width: 22.rdp,
                          height: 22.rdp,
                        ),
                        SpaceHomeNumWidget(
                          num: (personData?.teamMember?.learningInfo
                                      ?.continuousLearningDays ??
                                  0)
                              .toString(),
                        )
                      ]),
                Text(
                  _getMaxLengthText(personData?.teamMember?.userName, 6) +
                      (pos == PersonType.center ? "(我)" : ""),
                  style: TextStyle(
                    fontSize: 14.rdp,
                    color: personData?.contributionUrl?.isNotEmpty == true
                        ? HexColor('#DBAF00')
                        : HexColor('#452270'),
                    fontWeight: FontWeight.w400,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.rdp),
                if (personData?.contributionUrl?.isNotEmpty == true)
                  ImageNetworkCached(
                    imageUrl: personData?.contributionUrl ?? "",
                    width: 52.rdp,
                    height: 17.rdp,
                  ),
                SizedBox(height: 20.rdp),
                SizedBox(
                    width: (pos == PersonType.center ? 300.rdp : 200.rdp) *
                        personScale,
                    height: (pos == PersonType.center ? 220.rdp : 175.rdp) *
                        personScale,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        if (personData?.spineData != null)
                          GestureDetector(
                            onTap: () {
                              //点击小人,出现戳一戳或者点赞,无操作后3s消失
                              _clickPerson(pos);
                            },
                            child: IgnorePointer(
                              ignoring: pos == PersonType.center,
                              child: JoJoSpineSkinWidget(
                                personData?.spineData?.atlasFile ?? "",
                                personData?.spineData?.skelFile ?? "",
                                "stage${pos.name}",
                                controller: spineControllerMap[pos],
                                skinList: personData?.spineData?.skinList,
                                loadSkinCallback: (status) {
                                  changeSkinStatus(pos, status);
                                },
                              ),
                            ),
                          ),
                        if ((pos == PersonType.left && isLeftClick) ||
                            (pos == PersonType.right && isRightClick))
                          Positioned(
                            bottom: 50.rdp,
                            child: GestureDetector(
                              onTap: () {
                                //播放动画
                                _clickAnimation(pos, personData);
                              },
                              child: Image.asset(
                                personData?.teamMember?.userStatus == 1
                                    ? AssetsImg.SPACE_PERSON_ZAN
                                    : AssetsImg.SPACE_PERSON_CHUO,
                                width: 48.rdp,
                                height: 48.rdp,
                                package: Config.package,
                              ),
                            ),
                          ),
                        if (pos == PersonType.center &&
                            personData?.teamMember?.userStatus != null &&
                            personData?.teamMember?.userStatus != 1)
                          Positioned(
                            bottom: 26.rdp,
                            child: Container(
                              width: 120.rdp,
                              height: 40.rdp,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.centerLeft, // 渐变开始的位置
                                  end: Alignment.centerRight, // 渐变结束的位置
                                  colors: [
                                    HexColor('#FFA000'),
                                    HexColor('#FF9045'),
                                  ],
                                ),
                                borderRadius:
                                    BorderRadius.circular(20.rdp), // 设置圆角
                                border: Border.all(
                                  color: HexColor('#FFFFFF'), // 设置边框颜色
                                  width: 2.rdp, // 设置边框宽度
                                ),
                              ),
                              child: GestureDetector(
                                onTap: () {
                                  RunEnv.jumpLink(personData
                                          ?.teamMember?.studyBtn?.router ??
                                      "");
                                  final ctr = context.read<SpaceHomePageCtrl>();
                                  RunEnv.sensorsTrack('\$AppClick', {
                                    '\$element_name': '舞台区域功能点击',
                                    'custom_state': "gotostudy",
                                    ...ctr.commonSensorParams(),
                                  });
                                },
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        personData?.teamMember?.studyBtn
                                                ?.btnName ??
                                            "去学习",
                                        style: TextStyle(
                                          fontSize: 18.rdp,
                                          color: HexColor('#FFFFFF'),
                                          fontWeight: FontWeight.w600,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      SizedBox(width: 1.rdp),
                                      Image.asset(
                                        AssetsImg.SPACE_STUDY_ICON,
                                        width: 22.rdp,
                                        height: 22.rdp,
                                        package: Config.package,
                                      ),
                                    ]),
                              ),
                            ),
                          ),
                      ],
                    ))
              ]),
        ),
        if (pos != PersonType.center)
          personData?.teamMember?.userStatus == 1
              ? Positioned(
                  top: 100.rdp,
                  left: 20.rdp,
                  width: 150.rdp,
                  height: 100.rdp,
                  child: IgnorePointer(
                      ignoring: true,
                      child: JoJoSpineAnimationWidget(
                        widget.stageData.likeSpineData?.atlasFile ?? "",
                        widget.stageData.likeSpineData?.skelFile ?? "",
                        LoadMode.file,
                        likeControllerMap[pos] ??
                            JoJoSpineAnimationController(),
                      )))
              : Positioned(
                  left: 50.rdp,
                  width: 150.rdp,
                  height: 150.rdp,
                  child: IgnorePointer(
                      ignoring: true,
                      child: JoJoSpineAnimationWidget(
                          widget.stageData.prickSpineData?.atlasFile ?? "",
                          widget.stageData.prickSpineData?.skelFile ?? "",
                          LoadMode.file,
                          touchControllerMap[pos] ??
                              JoJoSpineAnimationController(),
                          onInitialized: (controller) {
                        controller.skeleton.setScaleX(0.3);
                        controller.skeleton.setScaleY(0.3);
                      })))
      ],
    );
  }

  // 监听舞台人物换肤状态
  changeSkinStatus(PersonType pos, SkinStatus status) {
    skinStatusMap[pos] = status;
    //设置换肤成功的所有人物的动画
    resetSkinAnimation();

    if (skinStatusMap.values.every((value) => value == SkinStatus.success) &&
        isLoading) {
      setState(() {
        isLoading = false;
      });
    }
  }

  // 重置所有皮肤动画
  resetSkinAnimation() {
    skinStatusMap.forEach((key, value) {
      if (value == SkinStatus.success) {
        var userStatus = 0;
        switch (key) {
          case PersonType.center:
            userStatus =
                widget.stageData.mineSpineData?.teamMember?.userStatus ?? 0;
            break;
          case PersonType.left:
            if ((widget.stageData.otherPersonSpineData?.length ?? 0) > 0) {
              userStatus = widget.stageData.otherPersonSpineData?[0].teamMember
                      ?.userStatus ??
                  0;
            }
            break;
          case PersonType.right:
            if ((widget.stageData.otherPersonSpineData?.length ?? 0) > 1) {
              userStatus = widget.stageData.otherPersonSpineData?[1].teamMember
                      ?.userStatus ??
                  0;
            }
            break;
          default:
        }
        spineControllerMap[key]?.playAnimation(JoJoSpineAnimation(
            animaitonName: userStatus == 1 ? "dance" : "idle",
            trackIndex: 0,
            loop: true,
            delay: 0));
      }
    });
  }

  /// 获取最大数量的文本
  String _getMaxLengthText(String? name, int maxLength) {
    if (name == null || name.isEmpty) {
      return "";
    }
    if (name.length <= maxLength) {
      return name;
    } else {
      return '${name.substring(0, maxLength)}…';
    }
  }

  @override
  void setState(VoidCallback fn) {
    if (mounted) {
      super.setState(fn);
    }
  }

  SpaceHomePageCtrl? get pageController =>
      mounted ? context.read<SpaceHomePageCtrl>() : null;
}

enum PersonType { left, center, right }
