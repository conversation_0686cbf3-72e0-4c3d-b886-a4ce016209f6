import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojoread_flutter_space_pkg/common/config/config.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

/// 数字控件
class SpaceHomeNumWidget extends StatelessWidget {
  final String num;

  const SpaceHomeNumWidget({
    Key? key,
    required this.num,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: num.length * 10.rdp + 16.rdp,
      height: 20.rdp,
      child: Stack(
        alignment: Alignment.center,
        children: [
          for (var i = 0; i < num.length; i++)
            Positioned(
              left: i * 10.rdp,
              child: Image.asset(
                spaceNumMap[num[i].toString()] ?? "",
                width: 13.rdp,
                height: 18.rdp,
                package: Config.package,
              ),
            ),
          if (num.isNotEmpty)
            Positioned(
              left: num.length * 10.rdp,
              child: Image.asset(
                AssetsImg.SPACE_NUM_DAY,
                width: 16.rdp,
                height: 18.rdp,
                package: Config.package,
              ),
            )
        ],
      ),
    );
  }

// 数字和图片关系
  static const spaceNumMap = {
    "0": AssetsImg.SPACE_NUM_0,
    "1": AssetsImg.SPACE_NUM_1,
    "2": AssetsImg.SPACE_NUM_2,
    "3": AssetsImg.SPACE_NUM_3,
    "4": AssetsImg.SPACE_NUM_4,
    "5": AssetsImg.SPACE_NUM_5,
    "6": AssetsImg.SPACE_NUM_6,
    "7": AssetsImg.SPACE_NUM_7,
    "8": AssetsImg.SPACE_NUM_8,
    "9": AssetsImg.SPACE_NUM_9,
  };
}
