import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';

import '../../../common/config/config.dart';
import '../../../static/img.dart';

class SpaceHomeInfoLogoWidget extends StatefulWidget {
  const SpaceHomeInfoLogoWidget({
    Key? key,
  }) : super(key: key);

  @override
  SpaceHomeInfoLogoWidgetState createState() => SpaceHomeInfoLogoWidgetState();
}

class SpaceHomeInfoLogoWidgetState extends State<SpaceHomeInfoLogoWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ImageAssetWeb(
      assetName: AssetsImg.SPACE_HOME_LOGO,
      fit: BoxFit.contain,
      width: 154.rdp,
      height: 48,
      package: Config.package,
    );
  }
}
