import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_info_contribution_cell_widget.dart';
import '../model/space_home_group_data.dart';

import '../state.dart';

class SpaceHomeInfocontributionListViewWidget extends StatefulWidget {
  final SpaceHomePageState state;
  const SpaceHomeInfocontributionListViewWidget({
    Key? key,
    required this.state,
  }) : super(key: key);

  @override
  SpaceHomeInfocontributionListViewWidgetState createState() =>
      SpaceHomeInfocontributionListViewWidgetState();
}

class SpaceHomeInfocontributionListViewWidgetState
    extends State<SpaceHomeInfocontributionListViewWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<Contribution>? contributions =
        widget.state.groupData?.contributions ?? [];
    return SizedBox(
        height: 150.rdp,
        child: ListView.builder(
            itemCount: contributions.length,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(left: 16.rdp, right: 16.rdp),
            itemBuilder: (context, index) {
              Contribution contribution = contributions[index];
              return SpaceHomeInfocontributionCellWidget(
                contribution: contribution,
              );
            }));
  }
}
