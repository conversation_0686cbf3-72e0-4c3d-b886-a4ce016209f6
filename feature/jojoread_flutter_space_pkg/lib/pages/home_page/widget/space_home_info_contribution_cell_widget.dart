import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';

import '../../../common/config/config.dart';
import '../../../static/img.dart';
import '../model/space_home_group_data.dart';

class SpaceHomeInfocontributionCellWidget extends StatefulWidget {
  final Contribution contribution;
  const SpaceHomeInfocontributionCellWidget({
    Key? key,
    required this.contribution,
  }) : super(key: key);

  @override
  SpaceHomeInfocontributionCellWidgetState createState() =>
      SpaceHomeInfocontributionCellWidgetState();
}

class SpaceHomeInfocontributionCellWidgetState
    extends State<SpaceHomeInfocontributionCellWidget> {
  bool hasTags = false;
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  String getOrderTAG() {
    String assetNameTagString =
        widget.contribution.order == null ? "" : AssetsImg.HEAD_INDEX_3;
    if (widget.contribution.order == 1) {
      assetNameTagString = AssetsImg.HEAD_INDEX_1;
    }
    if (widget.contribution.order == 2) {
      assetNameTagString = AssetsImg.HEAD_INDEX_2;
    }
    if (widget.contribution.order == 3) {
      assetNameTagString = AssetsImg.HEAD_INDEX_3;
    }
    return assetNameTagString;
  }

//名字携带tags
  Widget buildNameWithTagsContainer(
      Contribution contribution, List<SeasonTitle> listTitles) {
    return Container(
      width: 112.rdp,
      height: 40.rdp,
      alignment: Alignment.center,
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 112.rdp,
            child: Text(
              widget.contribution.userName ?? "",
              style: TextStyle(
                fontSize: 14.rdp,
                color: HexColor('#544300'),
                fontWeight: FontWeight.w400,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Row(
            children: listTitles.map((seasonTitle) {
              return ImageNetworkCached(
                imageUrl: seasonTitle.titlePicUrl ?? "",
                fit: BoxFit.contain,
                width: 52.rdp,
                height: 16.rdp,
              );
            }).toList(),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    List<SeasonTitle> listTitles =
        (widget.contribution.seasonTitle ?? []).take(2).toList();
    if (listTitles.isNotEmpty) {
      hasTags = true;
    } else {
      hasTags = false;
    }

    return Container(
        // alignment: Alignment.center,
        height: 50.rdp,
        color: Colors.transparent,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                //用户头像
                ImageNetworkCached(
                    imageUrl: widget.contribution.userAvatar ?? "",
                    fit: BoxFit.contain,
                    borderRadius: 20.rdp,
                    width: 40.rdp,
                    height: 40.rdp),
                Positioned(
                  left: -8,
                  bottom: 0,
                  child: ImageAssetWeb(
                    //头像tag
                    assetName: getOrderTAG(),
                    width: 24.rdp,
                    height: 24.rdp,
                    fit: BoxFit.contain,
                    package: Config.package,
                  ),
                )
              ],
            ),
            SizedBox(
              width: 8.rdp,
            ),
            if (hasTags == false)
              //昵称没有tag的情况
              SizedBox(
                width: 112.rdp,
                child: Text(
                  widget.contribution.userName ?? "",
                  style: TextStyle(
                    fontSize: 16.rdp,
                    color: HexColor('#404040'),
                    fontWeight: FontWeight.w400,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            if (hasTags == true)
              //昵称没有tag的情况
              buildNameWithTagsContainer(widget.contribution, listTitles),
            const Spacer(),
            //用户今日分数
            Container(
              width: 60.rdp,
              height: 24.rdp,
              alignment: Alignment.center,
              color: Colors.transparent,
              child: Text(
                widget.contribution.currentTodayIntegral.toString(),
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14.rdp,
                  color: HexColor('#DBAF00'),
                  fontWeight: FontWeight.w400,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(
              width: 16.rdp,
            ),
            //用户本周分数
            Container(
              width: 60.rdp,
              height: 24.rdp,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: Text(
                widget.contribution.currentWeekIntegral.toString(),
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14.rdp,
                  color: HexColor('#DBAF00'),
                  fontWeight: FontWeight.w400,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            )
          ],
        ));
  }
}
