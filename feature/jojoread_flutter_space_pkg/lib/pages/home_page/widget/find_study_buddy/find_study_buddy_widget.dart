import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/common/config/config.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/find_study_buddy/matching_animation.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/find_study_buddy/target_entry_animation.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/find_study_buddy/team_avatar_animation_widget.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

///页面每句类型
enum FindStudyType {
  start,
  end,
}

/// 选择学伴页面
class FindStudyBuddyWidget extends StatefulWidget {
  final Extend? extendData;
  final Function onFindStudyBuddyClick;
  final Function? closeAudio;
  const FindStudyBuddyWidget({
    Key? key,
    required this.extendData,
    required this.onFindStudyBuddyClick,
    this.closeAudio,
  }) : super(key: key);

  @override
  State<FindStudyBuddyWidget> createState() => _FindStudyBuddyWidgetState();
}

class _FindStudyBuddyWidgetState extends State<FindStudyBuddyWidget>
    with TickerProviderStateMixin {
  FindStudyType status = FindStudyType.start;
  Timer? _timer;
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  bool _showSecondImage = false;
  late Animation<double> _opacityAnimation;
  late AnimationController _opacityController;

  @override
  void initState() {
    super.initState();
    if (widget.closeAudio != null) {
      widget.closeAudio!();
    }

    _startTimer();

    // 初始化 AnimationController
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );

    // 位移动画：整体向上移动 100px
    _slideAnimation =
        Tween<Offset>(begin: Offset.zero, end: const Offset(0, -0.2)).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    // 初始化 AnimationController
    _opacityController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _opacityAnimation = Tween<double>(begin: 0, end: 1.0).animate(
      CurvedAnimation(
        parent: _opacityController,
        curve: Curves.easeOut,
      ),
    );

    Future.delayed(const Duration(milliseconds: 5000), () {
      _opacityController.forward(); // 播放透明度动画
    });
  }

  /// 目标入场动画
  void targetEntryAnimation() {
    // 延迟2秒后开始动画
    Future.delayed(const Duration(seconds: 2), () {
      _controller.forward();
    });
  }

  // 启动定时器
  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      setState(() {
        status = FindStudyType.end;
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ImageAssetWeb(
          assetName: AssetsImg.INTRO_BG,
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
          package: Config.package,
        ),
        SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 103.rdp),
                  child: Align(
                    alignment: Alignment.center,
                    child: Text(
                      '新一轮挑战开始！',
                      style: TextStyle(
                        color: HexColor('#664314'),
                        fontSize: 24.rdp,
                        fontWeight: FontWeight.bold,
                        height: 1.5,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 185.rdp),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (status == FindStudyType.start)
                      const MatchingAnimationWidget(),
                    if (status == FindStudyType.end)
                      SlideTransition(
                        position: _slideAnimation,
                        child: SizedBox(
                          height: 500.rdp,
                          child: Column(
                            children: [
                              FadeTransition(
                                opacity: _opacityAnimation,
                                child: Padding(
                                  padding: EdgeInsets.only(bottom: 30.rdp),
                                  child: Text(
                                    '已经为你匹配好学伴！',
                                    style: TextStyle(
                                      color: HexColor('#C09660'),
                                      fontSize: 18.rdp,
                                      fontWeight: FontWeight.w400,
                                      height: 1.5,
                                    ),
                                  ),
                                ),
                              ),
                              if (widget.extendData?.multiUserLearn?.memberList
                                      ?.isNotEmpty ==
                                  true)
                                TeamAvatarAnimationWidget(
                                  memberList: widget
                                      .extendData!.multiUserLearn!.memberList!,
                                  onAnimation: () async {
                                    await _controller.forward();
                                    setState(() {
                                      _showSecondImage = true;
                                    });
                                  },
                                ),
                              if (_showSecondImage)
                                BottomToTopAnimation(
                                  preInfo: widget
                                      .extendData?.multiUserLearn?.preInfo,
                                  onFindStudyBuddyClick:
                                      widget.onFindStudyBuddyClick,
                                ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
