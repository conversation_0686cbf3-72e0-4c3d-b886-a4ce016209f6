
import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/common/config/config.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

class MatchingAnimationWidget extends StatefulWidget {
  const MatchingAnimationWidget({
    Key? key,
  }) : super(key: key);

  @override
  State<MatchingAnimationWidget> createState() =>
      _MatchingAnimationWidgetState();
}

class _MatchingAnimationWidgetState extends State<MatchingAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  // 用于控制省略号变化的文本
  String _ellipsis = '';
  int _dotCount = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();

    // 初始化 AnimationController
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(); // 无限循环

    // 动画从 0 到 2π，表示圆周角度
    _animation = Tween<double>(begin: 0, end: 2 * pi).animate(
      CurvedAnimation(parent: _controller, curve: Curves.linear),
    );

    // 定时器每 500 毫秒更新一次省略号
    _timer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      setState(() {
        _dotCount = (_dotCount + 1) % 4; // 0, 1, 2, 3 循环
        _ellipsis = '.' * _dotCount; // 生成相应数量的点
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              // 计算放大镜图标的位置
              final double radius = 10.rdp; // 圆形轨迹的半径
              final double x = radius * cos(_animation.value);
              final double y = radius * sin(_animation.value);
              return Stack(
                alignment: Alignment.center,
                children: [
                  // 放大镜图标沿圆形轨迹滑动
                  Transform.translate(
                    offset: Offset(x, y),
                    child: ImageAssetWeb(
                      assetName: AssetsImg.SEARCH_BUDDY,
                      width: 64.rdp,
                      height: 64.rdp,
                      package: Config.package,
                    ),
                  ),
                ],
              );
            },
          ),

          // 放大镜旋转动画

          const SizedBox(height: 20),
          // 动态显示匹配中的文本
          Text(
            '匹配中$_ellipsis',
            style: TextStyle(
              color: HexColor('#C09660'),
              fontSize: 18.rdp,
              fontWeight: FontWeight.bold,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
