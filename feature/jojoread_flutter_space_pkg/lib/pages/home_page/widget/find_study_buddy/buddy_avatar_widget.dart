import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';

import '../../../../common/config/config.dart';
import '../../../../common/host_env/host_env.dart';
import '../../../../static/audio.dart';
import '../../../../static/img.dart';

/// 学伴匹配头像
class BuddyAvatarWidget extends StatefulWidget {
  final MemberList avatarInfo;
  final VoidCallback onAnimationComplete;

  const BuddyAvatarWidget({
    Key? key,
    required this.avatarInfo,
    required this.onAnimationComplete,
  }) : super(key: key);

  @override
  BuddyAvatarWidgetState createState() => BuddyAvatarWidgetState();
}

class BuddyAvatarWidgetState extends State<BuddyAvatarWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isTextVisible = false;
  late AudioPlayer _audioPlayer;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    )..forward().whenComplete(() async {
        setState(() {
          _isTextVisible = true;
        });
        _audioPlayer = AudioPlayer();
        String? package = RunEnv.package;
        String path = AssetsAudio.FINDBUDDYAUDIO;
        String keyName = package == null ? path : 'packages/$package/$path';
        AudioPlayer.global.setGlobalAudioContext(
          const AudioContext(
            iOS: AudioContextIOS(
              category: AVAudioSessionCategory.playback,
              options: [
                AVAudioSessionOptions.mixWithOthers,
              ],
            ),
          ),
        );
        _audioPlayer.audioCache.prefix = '';
        _audioPlayer.play(AssetSource(keyName));
      }); // 动画完成时调用回调
  }

  @override
  void dispose() {
    _controller.dispose();
    _audioPlayer.stop();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 100.rdp,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Stack(
            children: [
              Container(
                width: 80.rdp,
                height: 80.rdp,
                margin: EdgeInsets.only(right: 5.rdp, left: 5.rdp),
                decoration: BoxDecoration(
                  color: HexColor('#FFFFFF'),
                  borderRadius: BorderRadius.circular(80.rdp),
                ),
                child: Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(73.rdp),
                    child: widget.avatarInfo.avatarUrl != null &&
                            widget.avatarInfo.avatarUrl != ''
                        ? ImageNetworkCached(
                            imageUrl: widget.avatarInfo.avatarUrl!,
                            width: 73.rdp,
                            height: 73.rdp,
                            borderRadius: 73.rdp,
                          )
                        : ImageAssetWeb(
                            assetName: AssetsImg.DEFAULT_AVATAR,
                            width: 73.rdp,
                            height: 73.rdp,
                            package: Config.package,
                            borderRadius: 73.rdp,
                          ),
                  ),
                ),
              ),
              Positioned(
                right: 0,
                top: 0,
                child: ImageAssetWeb(
                  assetName: AssetsImg.ICON_ALERT,
                  width: 24.rdp,
                  height: 24.rdp,
                  package: Config.package,
                ),
              )
            ],
          ),
          SizedBox(height: 10.rdp),
          if (_isTextVisible)
            SizedBox(
              width: 100.rdp,
              child: Text(
                '${widget.avatarInfo.nickname}${widget.avatarInfo.userType == 1 ? '(我)' : ''}',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: HexColor('#C09660'),
                  fontSize: 16.rdp,
                  fontWeight: FontWeight.w400,
                  height: 1.5,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            )
        ],
      ),
    );
  }
}
