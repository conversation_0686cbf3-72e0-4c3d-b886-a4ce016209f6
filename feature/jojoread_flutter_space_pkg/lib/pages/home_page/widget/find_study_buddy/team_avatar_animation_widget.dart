import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/find_study_buddy/buddy_avatar_widget.dart';

typedef AsyncFunction<T> = Future<T> Function();

class TeamAvatarAnimationWidget extends StatefulWidget {
  final AsyncFunction onAnimation;
  final List<MemberList> memberList;

  const TeamAvatarAnimationWidget({
    super.key,
    required this.onAnimation,
    required this.memberList,
  });

  @override
  State<TeamAvatarAnimationWidget> createState() =>
      _TeamAvatarAnimationWidgetState();
}

class _TeamAvatarAnimationWidgetState extends State<TeamAvatarAnimationWidget>
    with SingleTickerProviderStateMixin {
  // 用于跟踪哪些头像已显示
  int _currentAvatarIndex = 1;

  void _nextAvatar() {
    setState(() {
      _currentAvatarIndex++;
    });
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startAnimation();
    });
  }

  void _startAnimation() async {
    // 模拟逐个加载头像的过程
    for (int i = 1; i < 3; i++) {
      await Future.delayed(const Duration(milliseconds: 2000));
      _nextAvatar();
    }

    /// 等待所有头像加载完成后，2s后开始位移动画
    await Future.delayed(const Duration(milliseconds: 2000));
    await widget.onAnimation();
  }

  @override
  Widget build(BuildContext context) {
    // 按 userType 排序，将 userType 为 1 的排在最前面
    final _memberList = List.from(widget.memberList);
    _memberList.sort((a, b) {
      if (a?.userType == 1 && b?.userType != 1) {
        return -1;
      } else if (a?.userType != 1 && b?.userType == 1) {
        return 1;
      }
      return 0;
    });
    final _length = _memberList.length > 3 ? 3 : _memberList.length;

    return SizedBox(
      height: 115.rdp,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: List.generate(
          _length,
          (index) {
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              transitionBuilder: (child, animation) {
                return ScaleTransition(scale: animation, child: child);
              },
              child: _currentAvatarIndex > index
                  ? BuddyAvatarWidget(
                      key: ValueKey(index),
                      avatarInfo: _memberList[index],
                      onAnimationComplete: () {},
                    )
                  : const SizedBox(),
            );
          },
        ),
      ),
    );
  }
}
