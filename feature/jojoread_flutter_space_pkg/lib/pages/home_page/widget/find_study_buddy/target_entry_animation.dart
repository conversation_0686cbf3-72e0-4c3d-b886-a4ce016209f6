import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/btn.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/common/config/config.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

class BottomToTopAnimation extends StatefulWidget {
  final PreInfo? preInfo;
  final Function onFindStudyBuddyClick;

  const BottomToTopAnimation({
    Key? key,
    this.preInfo,
    required this.onFindStudyBuddyClick,
  }) : super(key: key);

  @override
  State<BottomToTopAnimation> createState() => _BottomToTopAnimationState();
}

class _BottomToTopAnimationState extends State<BottomToTopAnimation>
    with TickerProviderStateMixin {
  late AnimationController _contentController;
  late Animation<Offset> _contentOffsetAnimation;

  @override
  void initState() {
    super.initState();

    _contentController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _contentOffsetAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // 从底部开始（Y轴1表示完全在底部）
      end: Offset.zero, // 移动到原位置（Offset.zero）
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeOut, // 使用 easeOut 曲线
    ));

    // 启动内容动画
    _contentController.forward();
  }

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SlideTransition(
          position: _contentOffsetAnimation,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 30.rdp),
              Text(
                widget.preInfo?.title ?? '',
                style: TextStyle(
                  color: HexColor('#C09660'),
                  fontSize: 16.rdp,
                  fontWeight: FontWeight.w400,
                  height: 1.5,
                ),
              ),
              Text(
                widget.preInfo?.content ?? '',
                style: TextStyle(
                  color: HexColor('#C09660'),
                  fontSize: 18.rdp,
                  fontWeight: FontWeight.bold,
                  height: 1.5,
                ),
              ),
              SizedBox(height: 12.rdp),
              ImageAssetWeb(
                assetName: AssetsImg.SPACE_MYSTERY_ITEM,
                width: 100.rdp,
                height: 100.rdp,
                package: Config.package,
              ),
              Text(
                '神秘道具',
                style: TextStyle(
                  color: HexColor('#C09660'),
                  fontSize: 16.rdp,
                  fontWeight: FontWeight.w400,
                  height: 1.5,
                ),
              ),
              SizedBox(height: 34.rdp),
              JoJoBtn(
                text: '下一步',
                tapHandle: () {
                  widget.onFindStudyBuddyClick();
                },
                width: 132.rdp,
                height: 44.rdp,
              )
            ],
          ),
        ),
      ],
    );
  }
}
