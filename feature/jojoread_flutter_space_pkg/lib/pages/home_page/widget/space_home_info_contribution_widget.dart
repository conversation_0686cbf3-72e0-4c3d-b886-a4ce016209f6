import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_info_contribution_listview_widget.dart';

import '../state.dart';

class SpaceHomeInfocontributionWidget extends StatefulWidget {
  final SpaceHomePageState state;
  const SpaceHomeInfocontributionWidget({Key? key, required this.state})
      : super(key: key);

  @override
  SpaceHomeInfocontributionWidgetState createState() =>
      SpaceHomeInfocontributionWidgetState();
}

class SpaceHomeInfocontributionWidgetState
    extends State<SpaceHomeInfocontributionWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        alignment: Alignment.center,
        height: 210.rdp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(24.0.rdp)),
          color: Colors.white,
          border: Border.all(
            color: HexColor('#EDBF94'),
            width: 2.rdp,
          ),
        ),
        child: Column(
          children: [
            Padding(
                padding:
                    EdgeInsets.only(left: 20.rdp, top: 8.rdp, right: 20.rdp),
                child: SizedBox(
                    height: 31.rdp,
                    // color: Colors.pink,
                    child: Row(
                      children: [
                        Text(
                          "贡献排行榜",
                          style: TextStyle(
                            fontSize: 16.rdp,
                            color: HexColor('#664314'),
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        const Spacer(),
                        Text(
                          "今日得分",
                          style: TextStyle(
                            fontSize: 14.rdp,
                            color: HexColor('#544300'),
                            fontWeight: FontWeight.w400,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(
                          width: 16.rdp,
                        ),
                        Text(
                          "本周得分",
                          style: TextStyle(
                            fontSize: 14.rdp,
                            color: HexColor('#544300'),
                            fontWeight: FontWeight.w400,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ))),
            SizedBox(
              height: 8.rdp,
            ),
            SpaceHomeInfocontributionListViewWidget(
              state: widget.state,
            )

            //  ,
          ],
        ));
  }
}
