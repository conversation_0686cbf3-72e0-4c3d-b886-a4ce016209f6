import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/common/host_env/host_env.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

/// 页面返回按钮
class BackWidget extends StatelessWidget {
  const BackWidget({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: JoJoRouter.barHeight,
      child: GestureDetector(
        onTap: () {
          RunEnv.pop();
        },
        child: ImageAssetWeb(
          assetName: AssetsImg.BACK_ICON,
          width: 44.rdp,
          height: 44.rdp,
          fit: BoxFit.cover,
          package: RunEnv.package,
        ),
      ),
    );
  }
}
