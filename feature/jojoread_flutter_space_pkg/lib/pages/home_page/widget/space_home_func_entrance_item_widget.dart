import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/controller.dart';
import '../../../common/host_env/host_env.dart';
import '../state.dart';

class SpaceHomeFuncEntranceItemWidget extends StatefulWidget {
  final SpaceHomePageState state;
  const SpaceHomeFuncEntranceItemWidget({Key? key, required this.state})
      : super(key: key);

  @override
  SpaceHomeFuncEntranceItemWidgetState createState() =>
      SpaceHomeFuncEntranceItemWidgetState();
}

class SpaceHomeFuncEntranceItemWidgetState
    extends State<SpaceHomeFuncEntranceItemWidget> {
  String router = '';
  String buttonName = '';
  String iconUrl = '';
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(
          Radius.circular(24.0.rdp),
        ),
        color: Colors.white,
      ),
      child: Padding(
          padding: EdgeInsets.only(
              left: 14.rdp, top: 8.rdp, right: 14.rdp, bottom: 8.rdp),
          child: Row(
            children: List.generate(2, (index) {
              if (index == 0) {
                buttonName = widget.state.resourceData
                        ?.historyPerformanceButtonInfo?.buttonName ??
                    '';
                iconUrl = widget.state.resourceData
                        ?.historyPerformanceButtonInfo?.iconUrl ??
                    '';
              } else {
                buttonName =
                    widget.state.resourceData?.ruleButtonInfo?.buttonName ?? '';
                iconUrl =
                    widget.state.resourceData?.ruleButtonInfo?.iconUrl ?? '';
              }
              return GestureDetector(
                  onTap: () {
                    RunEnv.sensorsTrack('\$AppClick', {
                      '\$element_name': '功能入口点击',
                      'custom_state': index == 0 ? 'lszj' : 'gzms',
                      ...?pageController?.commonSensorParams()
                    });
                    router = index == 0
                        ? (widget.state.resourceData
                                ?.historyPerformanceButtonInfo?.buttonUrl ??
                            '')
                        : (widget.state.resourceData?.ruleButtonInfo
                                ?.buttonUrl ??
                            '');
                    RunEnv.jumpLink(router);
                  },
                  child: Container(
                      alignment: Alignment.center,
                      height: 18.rdp,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(
                          Radius.circular(0.0.rdp),
                        ),
                        color: Colors.transparent,
                      ),
                      child: Row(
                        children: [
                          ImageNetworkCached(
                            imageUrl: iconUrl,
                            fit: BoxFit.contain,
                            width: 18.rdp,
                            height: 18.rdp,
                          ),
                          SizedBox(
                            width: 4.rdp,
                          ),
                          SizedBox(
                              width: 50.rdp,
                              height: 18.rdp,
                              child: Center(
                                  child: Text(
                                buttonName,
                                style: TextStyle(
                                  fontSize: 12.rdp,
                                  color: HexColor('#544300'),
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ))),
                          if (index != 1)
                            SizedBox(
                              width: 14.rdp,
                            ),
                        ],
                      )));
            }),
          )),
    );
  }

  SpaceHomePageCtrl? get pageController =>
      mounted ? context.read<SpaceHomePageCtrl>() : null;
}
