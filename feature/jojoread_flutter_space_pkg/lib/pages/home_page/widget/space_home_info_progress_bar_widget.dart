import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import '../model/space_home_group_data.dart';
import '../model/space_home_resource_data.dart';
import '../state.dart';

class SpaceHomeInfoProgressBarWidget extends StatefulWidget {
  final SpaceHomePageState state;
  const SpaceHomeInfoProgressBarWidget(
      {Key? key, required this.state, required})
      : super(key: key);

  @override
  SpaceHomeInfoProgressBarWidgetState createState() =>
      SpaceHomeInfoProgressBarWidgetState();
}

class SpaceHomeInfoProgressBarWidgetState
    extends State<SpaceHomeInfoProgressBarWidget> {
  GroupUpgradeInfo groupUpgradeInfoFirst = const GroupUpgradeInfo();
  GroupUpgradeInfo groupUpgradeInfoSecond = const GroupUpgradeInfo();
  int groupCurrentIntegral = 0;
  DTeamIconInfo? goldTeamIconInfo = const DTeamIconInfo();
  DTeamIconInfo? legendTeamIconInfo = const DTeamIconInfo();
  double _progress = 0;
  double _progress_godlen = 0;
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  refreshData() {
    TeamInfo? groupInfo = widget.state.groupData?.teamInfo;
    List<GroupUpgradeInfo> groupUpgradeInfo = groupInfo?.upgradeInfo ?? [];
    if (groupUpgradeInfo.length > 1) {
      groupUpgradeInfoFirst = groupUpgradeInfo[0];
      groupUpgradeInfoSecond = groupUpgradeInfo[1];
    }
    goldTeamIconInfo = widget.state.resourceData?.goldTeamIconInfo;
    legendTeamIconInfo = widget.state.resourceData?.legendTeamIconInfo;
    groupCurrentIntegral = groupInfo?.currentIntegral ?? 0;
    int currentIntegral = groupInfo?.currentIntegral ?? 0;
    int totalIntegral = groupInfo?.totalIntegral ?? 1;
    double progress = currentIntegral / totalIntegral;
    _progress = progress > 1 ? 1 : progress;
    double upgradeTargetIntegralFirst =
        (groupUpgradeInfoFirst.upgradeTargetIntegral ?? 0).toDouble();
    double upgradeTargetIntegralSecond =
        (groupUpgradeInfoSecond.upgradeTargetIntegral ?? 0).toDouble();
    _progress_godlen = upgradeTargetIntegralSecond != 0
        ? upgradeTargetIntegralFirst / upgradeTargetIntegralSecond
        : 0;
  }

  @override
  Widget build(BuildContext context) {
    refreshData();
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      double progressWidth = constraints.maxWidth - 55.0.rdp;
      return Container(
        height: 85.rdp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(24.0.rdp),
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned(
                top: 19.rdp,
                left: 20.rdp,
                right: 35.rdp,
                child: Container(
                  height: 23.rdp,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(
                      Radius.circular(24.0.rdp),
                    ),
                    color: HexColor('#986C31'),
                  ),
                )),
            Positioned(
                top: 19.rdp,
                left: 20.rdp,
                child: Container(
                  clipBehavior: Clip.hardEdge,
                  width: progressWidth * 1,
                  height: 23.rdp,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(
                        Radius.circular(24.0.rdp),
                      ),
                      color: Colors.transparent),
                  child: Stack(
                    children: [
                      Positioned(
                          top: 0.rdp,
                          left: -20.rdp,
                          child: Container(
                              width: progressWidth * _progress + 20.rdp,
                              height: 23.rdp,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(24.0.rdp),
                                ),
                                color: HexColor('#FCDA00'),
                              )))
                    ],
                  ),
                )),
            Positioned(
                top: 19.rdp,
                left: 31.rdp,
                child: Container(
                    height: 24.rdp,
                    color: Colors.transparent,
                    child: Center(
                      child: Text(
                        groupCurrentIntegral.toString(),
                        style: TextStyle(color: Colors.black, fontSize: 16.rdp),
                      ),
                    ))),
            Positioned(
                top: 1.rdp,
                left: 20.rdp + progressWidth * _progress_godlen - 45.rdp,
                child: SizedBox(
                  width: 90.rdp,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ImageNetworkCached(
                        imageUrl: goldTeamIconInfo?.selectedIconUrl ?? "",
                        fit: BoxFit.contain,
                        width: 50.rdp,
                        height: 50.rdp,
                      ),
                      Text(
                        '${groupUpgradeInfoFirst.upgradeLevelDesc ?? ''}\n(${groupUpgradeInfoFirst.upgradeTargetIntegral ?? ''})',
                        style: TextStyle(
                          fontSize: 12.rdp,
                          color: HexColor('#544300'),
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                )),
            Positioned(
                top: 1.rdp,
                left: progressWidth - 45.rdp,
                child: SizedBox(
                  width: 90.rdp,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ImageNetworkCached(
                        imageUrl: legendTeamIconInfo?.selectedIconUrl ?? "",
                        fit: BoxFit.contain,
                        width: 50.rdp,
                        height: 50.rdp,
                      ),
                      Text(
                        '${groupUpgradeInfoSecond.upgradeLevelDesc ?? ''}\n(${groupUpgradeInfoSecond.upgradeTargetIntegral ?? ''})',
                        style: TextStyle(
                          fontSize: 12.rdp,
                          color: HexColor('#664314'),
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                )),
          ],
        ),
      );
    });
  }
}
