import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/settlement_avatar_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/settlement_chest_animation.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/settlement_result_widget.dart';

import '../../../../common/host_env/host_env.dart';
import '../../../../static/audio.dart';

/// 成就资源信息
class SettlementRewardAnimation extends StatefulWidget {
  final AchInfo achInfo;
  final List<MemberList> memberList;
  final AwardInfo? awardInfo;
  final StageSpineData? boxSpineData;
  final Function? onClose;

  const SettlementRewardAnimation({
    Key? key,
    required this.achInfo,
    required this.memberList,
    required this.boxSpineData,
    this.awardInfo,
    required this.onClose,
  }) : super(key: key);

  @override
  SettlementRewardAnimationState createState() =>
      SettlementRewardAnimationState();
}

class SettlementRewardAnimationState extends State<SettlementRewardAnimation>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _positionAnimation;
  late JoJoSpineAnimationController animationController;
  bool _isShowChest = false;
  late AudioPlayer _audioPlayer;

  late AnimationController _avatarController;
  late Animation<double> _opacityAnimation;

  bool isFinish = false;

  final GlobalKey<SettlementChestAnimationState> chestKey =
      GlobalKey<SettlementChestAnimationState>();

  @override
  void initState() {
    super.initState();

    animationController = JoJoSpineAnimationController(isHasMusic: true);

    // 初始化动画控制器
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600), // 动画时长
    );

    // 图片从大缩小
    _scaleAnimation = Tween<double>(begin: 1, end: 0.6).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut, // 缓动曲线
      ),
    );

    // 位移向上（从 0 移到 -100）
    _positionAnimation =
        Tween<double>(begin: 0, end: -screen.screenHeight * 0.2).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    // 初始化 AnimationController
    _avatarController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // 创建透明度动画
    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _avatarController, curve: Curves.easeIn),
    );

    playEntryAnimation(); // 播放头像动画

    _audioPlayer = AudioPlayer();
    AudioPlayer.global.setGlobalAudioContext(
      const AudioContext(
        iOS: AudioContextIOS(
          category: AVAudioSessionCategory.playback,
          options: [
            AVAudioSessionOptions.mixWithOthers,
          ],
        ),
      ),
    );
    String? package = RunEnv.package;
    String path = '';
    if (widget.achInfo.level == AchievementType.failed.value) {
      path = AssetsAudio.ADVANCEDFAILURE;
    }
    if (widget.achInfo.level == AchievementType.gold.value) {
      path = AssetsAudio.ADVANCEDGODLIKE;
    }
    if (widget.achInfo.level == AchievementType.godlike.value) {
      path = AssetsAudio.ADVANCEDGODLIKE;
    }
    String keyName = package == null ? path : 'packages/$package/$path';
    _audioPlayer.audioCache.prefix = '';
    _audioPlayer.play(AssetSource(keyName));

    /// 添加activity生命周期监听函数
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      if (_audioPlayer.state == PlayerState.playing) {
        await _audioPlayer.pause();
      }
      animationController.pauseAnimation();
    } else if (state == AppLifecycleState.resumed) {
      animationController.resumeAnimation();
      if (_audioPlayer.state == PlayerState.paused) {
        await _audioPlayer.resume();
      }
    }
  }

  /// 待机动画播放完成 播放位移动画
  void _playPositionAnimation() {
    if (widget.achInfo.level != AchievementType.failed.value) {
      _controller.forward();
      playExitAnimation();
      setState(() {
        _isShowChest = true;
      });
    } else {
      isFinish = true;
      Future.delayed(const Duration(seconds: 3), () {
        SmartDialog.dismiss();
      });
    }
  }

  ///  播放头像动画
  void playEntryAnimation() {
    _avatarController.forward();
  }

  // 头像出场动画
  void playExitAnimation() {
    _avatarController.reverse();
  }

  @override
  void dispose() {
    _controller.dispose();
    _avatarController.dispose();
    animationController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _audioPlayer.stop();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final _playAnimationList = [
      JoJoSpineAnimation(
        animaitonName: "in",
        trackIndex: 0,
        loop: false,
        delay: 0,
      ),
      JoJoSpineAnimation(
        animaitonName: "idle",
        trackIndex: 0,
        loop: false,
        delay: 0,
        listener: (AnimationEventType type) {
          if (type == AnimationEventType.complete &&
              widget.achInfo.level == AchievementType.failed.value) {
            _playPositionAnimation();
          }
        },
      ),
    ];
    if (widget.achInfo.level == AchievementType.godlike.value ||
        widget.achInfo.level == AchievementType.gold.value) {
      _playAnimationList.add(
        JoJoSpineAnimation(
          animaitonName: "stand",
          trackIndex: 0,
          loop: false,
          delay: 0,
          listener: (AnimationEventType type) {
            if (type == AnimationEventType.complete) {
              _playPositionAnimation();
            }
          },
        ),
      );
    }
    return GestureDetector(
      onTap: () {
        if (isFinish || chestKey.currentState?.animationFinish() == true) {
          SmartDialog.dismiss();
        }
      },
      child: Stack(
        children: [
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, _positionAnimation.value), // 控制位移
                child: Transform.scale(
                  scale: _scaleAnimation.value, // 控制缩放
                  child: child,
                ),
              );
            },
            child: Stack(
              children: [
                Container(
                  width: screen.screenWidth,
                  height: screen.screenHeight,
                  alignment: Alignment.topCenter,
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: screen.screenHeight * 0.3,
                    ), // 设置顶部偏移
                    child: JoJoSpineAnimationWidget(
                      widget.achInfo.resourceSpineData?.atlasFile ?? '',
                      widget.achInfo.resourceSpineData?.skelFile ?? "",
                      LoadMode.file,
                      animationController,
                      onInitialized: (controller) {
                        const _scale = 0.45;
                        controller.skeleton.setScaleX(_scale);
                        controller.skeleton.setScaleY(_scale);
                        animationController.playAnimationList([
                          ..._playAnimationList,
                        ]);
                      },
                    ),
                  ),
                ),
                Positioned(
                  top: screen.screenHeight * 0.5,
                  left: 0,
                  right: 0,
                  child: Text(
                    widget.achInfo.name ?? '',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: HexColor('#FFFFFF'),
                      fontSize: 36.rdp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          /// 宝箱动画
          if (_isShowChest)
            Positioned(
              top: screen.screenHeight * 0.15,
              child: SettlementChestAnimation(
                key: chestKey,
                level: widget.achInfo.level,
                awardInfo: widget.awardInfo,
                boxSpineData: widget.boxSpineData,
                onClose: widget.onClose,
              ),
            ),

          /// 头像
          Positioned(
            top: screen.screenHeight * 0.5 + 100.rdp,
            left: 0,
            right: 0,
            child: FadeTransition(
              opacity: _opacityAnimation,
              child: SettlementAvatarWidget(
                achInfo: widget.achInfo,
                memberList: widget.memberList,
              ),
            ),
          )
        ],
      ),
    );
  }
}
