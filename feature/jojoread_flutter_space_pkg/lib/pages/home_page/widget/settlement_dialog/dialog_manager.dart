import 'dart:async';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';

class DialogManager {
  final List<DialogManagerType> _dialogQueue = [];
  final List<DialogManagerType> _originDialogQueue = [];

  bool _isShowingDialog = false;

  /// 当前弹窗是否正在显示
  bool get isDialogShowing => _isDialogShowing;
  bool _isDialogShowing = false;

  /// dialog显示的页面，如果当前页面不是这个，不显示
  BaseState? get bindPage => _bindPage;
  BaseState? _bindPage;

  DialogManager({BaseState? bindPage}) : _bindPage = bindPage;

  void bindToPage(BaseState page) {
    _bindPage = page;
  }

  /// 清空数据，不影响bindPage
  void clear() {
    _dialogQueue.clear();
    _originDialogQueue.clear();
    _isShowingDialog = false;
    _isDialogShowing = false;
  }

  // 插入弹窗到队列
  void addDialog(List<DialogManagerType> dialog, {bool? isInsert}) {
    if (isInsert == true) {
      _dialogQueue.insertAll(1, dialog);
      _originDialogQueue.insertAll(1, dialog);
    } else {
      _dialogQueue.addAll(dialog);
      _originDialogQueue.addAll(dialog);
    }
  }

  void onStart() {
    if (!_isShowingDialog) {
      showNextDialog();
    }
  }

  // 中止弹窗方法
  void onStop() {
    _dialogQueue.clear();
    _isShowingDialog = false;
  }

  void onRestart() {
    _isShowingDialog = false;
    if (!_isShowingDialog) {
      _dialogQueue.addAll(_originDialogQueue);
      onStart();
    }
  }

  // 弹窗显示
  void showDialog(DialogManagerType dialog) {
    SmartDialog.dismiss();
    // 定时器是为了让弹窗展示的时候,接口数据已经更新
    // Timer(const Duration(milliseconds: 100), () {
    dialog.onShowDialog().then((value) {
      _isDialogShowing = true;
      l.d('弹窗显示成功', value);
    }).catchError((err) {
      l.e('弹窗显示失败', err);
      _isDialogShowing = false;
      hideDialog();
    });
    // });
  }

  // 显示下一个弹窗
  void showNextDialog() async {
    if (_dialogQueue.isNotEmpty) {
      _isShowingDialog = true;
      // 排序弹窗展示优先级
      _dialogQueue.sort((a, b) => a.priority.compareTo(b.priority));
      // 取第一个数据展示
      final dialog = _dialogQueue.first;

      if (dialog.isDialogVisibleAsync != null) {
        // 等待异步方法执行完成 判断弹窗是否要显示
        final isShow = await dialog.isDialogVisibleAsync!();
        if (isShow == true) {
          showDialog(dialog);
        } else {
          hideDialog();
        }
      } else {
        showDialog(dialog);
      }
    }
  }

  void hideDialog() {
    if (_dialogQueue.isNotEmpty) {
      _isDialogShowing = false;
      _dialogQueue.removeAt(0);
      showNextDialog();
    }
  }
}

class DialogManagerType {
  // 弹窗展示方法
  Future<dynamic> Function() onShowDialog;
  // 弹窗是否展示异步方法 不传递默认弹窗展示
  Future<bool> Function()? isDialogVisibleAsync;
  // 弹窗层级越低越先展示
  int priority;

  DialogManagerType({
    required this.onShowDialog,
    this.priority = 9999,
    this.isDialogVisibleAsync,
  });
}
