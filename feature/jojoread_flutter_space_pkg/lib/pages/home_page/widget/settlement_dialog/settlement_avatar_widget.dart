import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';

import '../../../../common/config/config.dart';
import '../../../../static/img.dart';

/// 学伴匹配头像
class SettlementAvatarWidget extends StatefulWidget {
  final AchInfo achInfo;
  final List<MemberList> memberList;

  const SettlementAvatarWidget({
    Key? key,
    required this.achInfo,
    required this.memberList,
  }) : super(key: key);

  @override
  SettlementAvatarWidgetState createState() => SettlementAvatarWidgetState();
}

class SettlementAvatarWidgetState extends State<SettlementAvatarWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          widget.achInfo.tip ?? '',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: HexColor('#FFFFFF'),
            fontSize: 16.rdp,
            fontWeight: FontWeight.bold,
            height: 1.5,
          ),
        ),
        SizedBox(height: 12.rdp),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ...widget.memberList.map(
              (member) => Column(
                children: [
                  Stack(
                    children: [
                      Container(
                        width: 74.rdp,
                        height: 74.rdp,
                        margin: EdgeInsets.only(right: 5.rdp, left: 5.rdp),
                        decoration: BoxDecoration(
                          color: HexColor('#FFFFFF'),
                          borderRadius: BorderRadius.circular(60.rdp),
                          border: Border.all(
                              color: member.achList != null &&
                                      member.achList!.isNotEmpty &&
                                      member.achList?.first.type == 1
                                  ? HexColor('#FFCD45')
                                  : HexColor('#FFFFFF'),
                              width: 4.rdp),
                        ),
                        child: Center(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(73.rdp),
                            child: member.avatarUrl != null &&
                                    member.avatarUrl != ''
                                ? ImageNetworkCached(
                                    imageUrl: member.avatarUrl!,
                                    width: 73.rdp,
                                    height: 73.rdp,
                                    borderRadius: 73.rdp,
                                  )
                                : ImageAssetWeb(
                                    assetName: AssetsImg.DEFAULT_AVATAR,
                                    width: 73.rdp,
                                    height: 73.rdp,
                                    package: Config.package,
                                    borderRadius: 73.rdp,
                                  ),
                          ),
                        ),
                      ),
                      if (member.achList != null &&
                          member.achList!.isNotEmpty &&
                          member.achList?.first.type == 1 &&
                          member.achList?.first.icon != null)
                        Positioned(
                          left: 0,
                          top: 0,
                          child: ImageNetworkCached(
                            imageUrl: member.achList?.first.icon ?? "",
                            width: 36.rdp,
                            height: 24.rdp,
                            fit: BoxFit.contain,
                          ),
                        )
                    ],
                  ),
                  SizedBox(height: 10.rdp),
                  SizedBox(
                    width: 80.rdp,
                    child: Text(
                      '${member.nickname}${member.userType == 1 ? '(我)' : ''}',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: HexColor('#FFFFFF'),
                        fontSize: 12.rdp,
                        fontWeight: FontWeight.w400,
                        height: 1.5,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
