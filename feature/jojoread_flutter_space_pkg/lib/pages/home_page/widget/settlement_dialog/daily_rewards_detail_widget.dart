import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/iterable_extension.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/common/host_env/host_env.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';

import '../../../../common/config/config.dart';
import '../../../../static/audio.dart';
import '../../../../static/img.dart';

/// 日结算组件
class DailyRewardsDetailWidget extends StatefulWidget {
  final Extend extendData;

  final Map<String, dynamic> sensorParams;

  final Function? closeAudio;
  final StageSpineData? flowerSpineData;

  const DailyRewardsDetailWidget({
    Key? key,
    required this.extendData,
    required this.sensorParams,
    this.closeAudio,
    this.flowerSpineData,
  }) : super(key: key);

  @override
  DailyRewardsDetailWidgetState createState() =>
      DailyRewardsDetailWidgetState();
}

class DailyRewardsDetailWidgetState extends State<DailyRewardsDetailWidget>
    with WidgetsBindingObserver {
  late JoJoSpineAnimationController animationController;
  late AudioPlayer _audioPlayer;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    AudioPlayer.global.setGlobalAudioContext(
      const AudioContext(
        iOS: AudioContextIOS(
          category: AVAudioSessionCategory.playback,
          options: [
            AVAudioSessionOptions.mixWithOthers,
          ],
        ),
      ),
    );
    String? package = RunEnv.package;
    String path = AssetsAudio.TODAYSCORE;
    String keyName = package == null ? path : 'packages/$package/$path';
    _audioPlayer.audioCache.prefix = '';
    _audioPlayer.play(AssetSource(keyName));
    animationController = JoJoSpineAnimationController(
      playProgressListener: (time) {},
      isHasMusic: true,
    );

    if (widget.closeAudio != null) {
      widget.closeAudio!();
    }

    /// 添加activity生命周期监听函数
    WidgetsBinding.instance.addObserver(this);
    RunEnv.sensorsTrack('ElementView', {
      'c_element_name': '记分弹窗曝光',
      ...widget.sensorParams,
    });
  }

  @override
  void dispose() {
    animationController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _audioPlayer.stop();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      if (_audioPlayer.state == PlayerState.playing) {
        await _audioPlayer.pause();
      }
      animationController.pauseAnimation();
    } else if (state == AppLifecycleState.resumed) {
      animationController.resumeAnimation();
      if (_audioPlayer.state == PlayerState.paused) {
        await _audioPlayer.resume();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 375.rdp,
      height: 419.rdp,
      alignment: Alignment.center,
      child: Stack(
        alignment: Alignment.center,
        children: [
          ImageAssetWeb(
            assetName: AssetsImg.REWARDS_BG,
            width: 314.rdp,
            height: 419.rdp,
            fit: BoxFit.cover,
            package: Config.package,
          ),
          if (widget.flowerSpineData != null)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: SizedBox(
                width: 200.rdp,
                height: 100.rdp,
                child: JoJoSpineAnimationWidget(
                  widget.flowerSpineData?.atlasFile ?? '',
                  widget.flowerSpineData?.skelFile ?? '',
                  LoadMode.file,
                  animationController,
                  onInitialized: (controller) {
                    controller.skeleton.setScaleX(2);
                    controller.skeleton.setScaleY(2);
                    animationController.playAnimation(
                      JoJoSpineAnimation(
                        animaitonName: "TX_vfx_Ribbon",
                        trackIndex: 0,
                        loop: false,
                        delay: 0,
                        listener: (AnimationEventType type) {},
                      ),
                    );
                  },
                ),
              ),
            ),
          SizedBox(
            width: 314.rdp,
            height: 419.rdp,
            child: Column(
              children: [
                SizedBox(height: 65.rdp),
                Text(
                  '今日得分',
                  style: TextStyle(
                    fontSize: 24.rdp,
                    color: HexColor('#986C31'),
                    fontWeight: FontWeight.bold,
                    height: 1.5,
                  ),
                ),
                SizedBox(height: 5.rdp),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ImageAssetWeb(
                      assetName: AssetsImg.PLUS_SIGN_ICON,
                      width: 33.rdp,
                      height: 96.rdp,
                      fit: BoxFit.contain,
                      package: Config.package,
                    ),
                    Text(
                      '${widget.extendData.multiUserLearn?.totalScore}',
                      style: TextStyle(
                        fontSize: 64.rdp,
                        color: HexColor('#FF9045'),
                        fontWeight: FontWeight.w400,
                        height: 1.4,
                        fontFamily: 'MohrRounded_Bold',
                        package: 'jojo_flutter_base',
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24.rdp),
                if (widget.extendData.multiUserLearn?.finishTaskList != null)
                  Expanded(
                    child: SizedBox(
                      width: 200.rdp,
                      child: ListView.builder(
                        itemCount: widget.extendData.multiUserLearn
                                ?.finishTaskList?.length ??
                            0,
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        itemBuilder: (BuildContext context, int index) {
                          final item = widget.extendData.multiUserLearn
                              ?.finishTaskList?[index];
                          return Column(
                            children: [
                              Row(
                                children: [
                                  ImageAssetWeb(
                                    assetName: AssetsImg.CHECK,
                                    width: 24.rdp,
                                    height: 24.rdp,
                                    package: Config.package,
                                  ),
                                  SizedBox(width: 8.rdp),
                                  Expanded(
                                    child: Text(
                                      item?.taskName ?? '',
                                      style: TextStyle(
                                        fontSize: 16.rdp,
                                        color: HexColor('#666666'),
                                        fontWeight: FontWeight.w400,
                                        height: 1.5,
                                      ),
                                    ),
                                  ),
                                  Text(
                                    '+${item?.taskScore}',
                                    style: TextStyle(
                                      fontSize: 18.rdp,
                                      color: HexColor('#FF9045'),
                                      fontWeight: FontWeight.bold,
                                      height: 1.5,
                                      fontFamily: 'PinFang_SC',
                                      package: 'jojo_flutter_base',
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 4.rdp),
                              SizedBox(
                                width: 200.rdp,
                                child: Divider(
                                  color: HexColor('#F5F4F4'), // 下边框颜色
                                  height: 2.rdp, // 下边框高度
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                if (isEarlyBird(
                    widget.extendData.multiUserLearn?.achInfo?.achList))
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ImageAssetWeb(
                        assetName: AssetsImg.BRID_ICON,
                        width: 48.rdp,
                        height: 48.rdp,
                        package: Config.package,
                      ),
                      Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.rdp, vertical: 2.rdp),
                        decoration: BoxDecoration(
                          color: HexColor('#C7D440'),
                          borderRadius: BorderRadius.circular(24.rdp),
                        ),
                        child: Text(
                          '恭喜抢到今天的早鸟王！',
                          style: TextStyle(
                            fontSize: 14.rdp,
                            color: HexColor('#FFFFFF'),
                            fontWeight: FontWeight.bold,
                            height: 1.2,
                          ),
                        ),
                      )
                    ],
                  ),
                SizedBox(height: 34.rdp),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 是否是早鸟王
  bool isEarlyBird(List<AchList>? achList) {
    final _achInfo = achList?.firstWhereOrNull((e) => e.type == 2);
    return _achInfo != null;
  }
}
