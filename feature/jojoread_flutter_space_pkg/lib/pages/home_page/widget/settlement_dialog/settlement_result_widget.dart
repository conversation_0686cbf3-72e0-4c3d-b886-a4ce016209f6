import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojoread_flutter_space_pkg/common/host_env/host_env.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/settlement_reward_animation_widgte.dart';

/// 成就等级类型
enum AchievementType {
  /// 失败
  @JsonValue('failed')
  failed(0),

  /// 黄金
  @JsonValue('gold')
  gold(1),

  /// 超神
  @JsonValue('godlike')
  godlike(2);

  const AchievementType(this.value);
  final int value;
}

/// 战队奖励弹窗-结算开始展示
class SettlementResultWidget extends StatefulWidget {
  final Extend extendData;
  final StageSpineData? boxSpineData;
  final Function? onClose;
  final Function? closeAudio;
  final Map<String, dynamic> sensorParams;

  const SettlementResultWidget({
    Key? key,
    required this.extendData,
    required this.boxSpineData,
    required this.onClose,
    this.closeAudio,
    required this.sensorParams,
  }) : super(key: key);

  @override
  SettlementResultWidgetState createState() => SettlementResultWidgetState();
}

class SettlementResultWidgetState extends State<SettlementResultWidget> {
  bool showAward = false;

  @override
  void initState() {
    super.initState();

    if (widget.closeAudio != null) {
      widget.closeAudio!();
    }

    RunEnv.sensorsTrack('ElementView', {
      'c_element_name': '周结算弹窗曝光',
      ...widget.sensorParams,
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final _memberList = widget.extendData.multiUserLearn?.memberList ?? [];
    return Container(
      width: screen.screenWidth,
      height: screen.screenHeight,
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          if (widget.extendData.multiUserLearn != null &&
              widget.extendData.multiUserLearn?.achInfo != null)
            SettlementRewardAnimation(
              achInfo: widget.extendData.multiUserLearn!.achInfo!,
              memberList: _memberList,
              awardInfo: widget.extendData.multiUserLearn?.awardInfo,
              boxSpineData: widget.boxSpineData,
              onClose: widget.onClose,
            ),
        ],
      ),
    );
  }
}
