import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';

import '../../../../common/config/config.dart';
import '../../../../static/img.dart';

/// 奖励信息
class SettlementAwardWidget extends StatefulWidget {
  final List<AwardItem> awardList;

  const SettlementAwardWidget({
    Key? key,
    required this.awardList,
  }) : super(key: key);

  @override
  SettlementAwardWidgetState createState() => SettlementAwardWidgetState();
}

class SettlementAwardWidgetState extends State<SettlementAwardWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  late AnimationController _opacityController;

  bool isShowText = false;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    Future.delayed(const Duration(milliseconds: 100), () {
      _controller.forward();
    });

    // 初始化 AnimationController
    _opacityController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _opacityController, curve: Curves.easeIn),
    );

    _controller.addListener(() {
      if (_controller.isCompleted) {
        setState(() {
          isShowText = true;
        });
        _opacityController.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _opacityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    /// 奖励列表每一行最多展示三个居中展示，第一行展示余数数据
    List<List<AwardItem>> rewardRows = buildRewardRows(
      widget.awardList,
      maxPerRow: 3,
    );

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: Column(
        children: [
          ...rewardRows.map(
            (row) => Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ...row.map(
                  (item) => Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 7.rdp,
                    ),
                    child: Column(
                      children: [
                        Stack(
                          children: [
                            ImageAssetWeb(
                              width: 100.rdp,
                              height: 100.rdp,
                              assetName: AssetsImg.SPACE_HOME_AWARD_BG,
                              package: Config.package,
                            ),
                            Positioned(
                              top: 0,
                              bottom: 0,
                              right: 0,
                              left: 0,
                              child: Center(
                                child: ImageNetworkCached(
                                  imageUrl: item.cover ?? '',
                                  width: 58.rdp,
                                  height: 71.rdp,
                                ),
                              ),
                            ),
                            if (item.count != null && item.count != 0)
                              Positioned(
                                bottom: 5.rdp,
                                right: 10.rdp,
                                child: Row(
                                  children: [
                                    ImageAssetWeb(
                                      assetName: AssetsImg.SPACE_NUM_X,
                                      width: 16.rdp,
                                      height: 30.rdp,
                                      package: Config.package,
                                    ),
                                    ...(item.count!).toString().split('').map(
                                          (String num) => ImageAssetWeb(
                                            assetName: spaceNumMap[num] ?? "",
                                            width: 16.rdp,
                                            height: 30.rdp,
                                            package: Config.package,
                                          ),
                                        )
                                  ],
                                ),
                              ),
                          ],
                        ),
                        FadeTransition(
                          opacity: _opacityAnimation,
                          child: Container(
                            width: 80.rdp,
                            margin: EdgeInsets.only(top: 8.rdp),
                            child: Text(
                              item.name ?? '',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: HexColor('#FFFFFF'),
                                fontSize: 16.rdp,
                                fontWeight: FontWeight.w400,
                                height: 1.5,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 16.rdp),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// 数字和图片关系
const spaceNumMap = {
  "0": AssetsImg.SPACE_NUM_0,
  "1": AssetsImg.SPACE_NUM_1,
  "2": AssetsImg.SPACE_NUM_2,
  "3": AssetsImg.SPACE_NUM_3,
  "4": AssetsImg.SPACE_NUM_4,
  "5": AssetsImg.SPACE_NUM_5,
  "6": AssetsImg.SPACE_NUM_6,
  "7": AssetsImg.SPACE_NUM_7,
  "8": AssetsImg.SPACE_NUM_8,
  "9": AssetsImg.SPACE_NUM_9,
};

/// 方法：将奖励数据分组为每行的数据
List<List<AwardItem>> buildRewardRows(List<AwardItem> rewards,
    {required int maxPerRow}) {
  List<List<AwardItem>> rewardRows = [];
  int remainder = rewards.length % maxPerRow;

  // 第一行：余数部分
  if (remainder > 0) {
    rewardRows.add(rewards.sublist(0, remainder));
  }

  // 后续行：完整行
  for (int i = 0; i < rewards.length ~/ maxPerRow; i++) {
    rewardRows.add(rewards.sublist(
        remainder + i * maxPerRow, remainder + (i + 1) * maxPerRow));
  }

  return rewardRows;
}
