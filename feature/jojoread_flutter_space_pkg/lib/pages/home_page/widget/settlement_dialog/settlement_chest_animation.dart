import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/settlement_award_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/settlement_result_widget.dart';

/// 宝箱动画资源
class SettlementChestAnimation extends StatefulWidget {
  final int? level;
  final AwardInfo? awardInfo;
  final StageSpineData? boxSpineData;
  final Function? onClose;

  const SettlementChestAnimation({
    Key? key,
    required this.level,
    this.awardInfo,
    required this.boxSpineData,
    required this.onClose,
  }) : super(key: key);

  @override
  SettlementChestAnimationState createState() =>
      SettlementChestAnimationState();
}

class SettlementChestAnimationState extends State<SettlementChestAnimation>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _opacityController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  late Animation<double> _textOpacityAnimation;
  late AnimationController _textController;

  late JoJoSpineAnimationController animationController;
  bool isClick = false;

  String text = '惊喜呈现！';
  bool showAward = false;

  bool isFlag = false;

  bool isStart = false;

  bool _isFinish = false;

  @override
  void initState() {
    super.initState();

    animationController = JoJoSpineAnimationController(isHasMusic: true);

    /// 文字动画
    _textController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 创建透明度动画
    _textOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeIn),
    );

    if (widget.level == AchievementType.godlike.value) {
      setState(() {
        text = '点击以升级！';
      });
    }

    // 入场动画控制器
    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _scaleController,
        curve: Curves.easeOutBack,
      ),
    );

    // 出场动画控制器
    _opacityController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _opacityController,
        curve: Curves.easeOut,
      ),
    );

    _playEntranceAnimation();
  }

  /// 播放入场动画
  void _playEntranceAnimation() {
    _scaleController.forward();
  }

  /// 播放出场动画
  Future<void> playExitAnimation() async {
    await _opacityController.forward(); // 播放透明度动画
  }

  /// 点击打开宝箱动画
  void openBoxAnimation(AnimationEventType type) {
    if (type == AnimationEventType.start) {
      _textController.reverse();
      setState(() {
        showAward = true;
      });
    } else if (type == AnimationEventType.complete) {
      if (widget.awardInfo?.awardItem?.isNotEmpty == true) {
        _scaleController.reverse();
      }
      handleClose();
    }
  }

  /// 如果没有宝箱数据 3秒自动关闭弹窗
  handleClose() {
    _isFinish = true;
    Future.delayed(
        Duration(
            seconds: widget.awardInfo?.awardItem?.isNotEmpty == true ? 4 : 3),
        () {
      SmartDialog.dismiss();
    });
  }

  /// 宝箱点击动画
  void playBoxClickAnimation() {
    if (isFlag == true) return;
    isFlag = true;
    if (isClick == true && widget.level == AchievementType.godlike.value) {
      animationController.playAnimationList([
        JoJoSpineAnimation(
            animaitonName: "click2",
            trackIndex: 0,
            loop: false,
            delay: 0,
            listener: (AnimationEventType type) {
              openBoxAnimation(type);
            })
      ]);
      return;
    }
    if (widget.level == AchievementType.godlike.value) {
      // 播放动画
      _textController.reverse();
      isClick = true;
      animationController.playAnimationList([
        JoJoSpineAnimation(
          animaitonName: "play",
          trackIndex: 0,
          loop: false,
          delay: 0,
          listener: (AnimationEventType type) {
            if (type == AnimationEventType.complete) {
              setState(() {
                text = '惊喜呈现！';
              });
              _textController.forward();
            }
          },
        ),
        JoJoSpineAnimation(
          animaitonName: "idle2",
          trackIndex: 0,
          loop: true,
          delay: 0,
          listener: (AnimationEventType type) {
            if (type == AnimationEventType.complete) {
              isFlag = false;
            }
          },
        )
      ]);
    } else if (widget.level == AchievementType.gold.value) {
      isClick = true;
      animationController.playAnimationList([
        JoJoSpineAnimation(
          animaitonName: "click1",
          trackIndex: 0,
          loop: false,
          delay: 0,
          listener: (AnimationEventType type) {
            openBoxAnimation(type);
          },
        )
      ]);
    }
  }

  /// 返回动画完成标识
  bool animationFinish() {
    return _isFinish;
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _opacityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final _playAnimationList = [
      JoJoSpineAnimation(
        animaitonName: "in",
        trackIndex: 0,
        loop: false,
        delay: 0,
        listener: (AnimationEventType type) {
          if (type == AnimationEventType.complete) {
            _textController.forward();
          }
        },
      ),
    ];
    if (widget.level == AchievementType.gold.value ||
        widget.level == AchievementType.godlike.value) {
      _playAnimationList.add(JoJoSpineAnimation(
        animaitonName: "idle1",
        trackIndex: 0,
        loop: true,
        delay: 0,
        listener: (AnimationEventType type) {
          isStart = true;
        },
      ));
    }

    return Stack(
      children: [
        Center(
          child: Stack(
            children: [
              AnimatedBuilder(
                animation:
                    Listenable.merge([_scaleController, _opacityController]),
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Opacity(
                      opacity: _opacityAnimation.value,
                      child: child,
                    ),
                  );
                },
                child: GestureDetector(
                  onTap: () {
                    /// 宝箱开始还不能被点击
                    if (isStart == true) {
                      playBoxClickAnimation();
                    }
                  },
                  child: SizedBox(
                    width: screen.screenWidth,
                    height: screen.screenHeight * 0.8,
                    child: JoJoSpineAnimationWidget(
                      widget.boxSpineData?.atlasFile ?? '',
                      widget.boxSpineData?.skelFile ?? "",
                      LoadMode.file,
                      animationController,
                      onInitialized: (controller) {
                        // controller.skeleton.setScaleX(0.5);
                        // controller.skeleton.setScaleY(0.5);
                        animationController.playAnimationList([
                          ..._playAnimationList,
                        ]);
                      },
                    ),
                  ),
                ),
              ),
              Positioned(
                top: screen.screenHeight * 0.5,
                right: 0,
                left: 0,
                child: FadeTransition(
                  opacity: _textOpacityAnimation,
                  child: Text(
                    text,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: HexColor('#FFFFFF'),
                      fontSize: 20.rdp,
                      fontWeight: FontWeight.bold,
                      height: 1.5,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (widget.awardInfo != null &&
            widget.awardInfo?.awardItem != null &&
            widget.awardInfo!.awardItem!.isNotEmpty &&
            showAward)
          Positioned(
            top: screen.screenHeight * 0.31,
            right: 0,
            left: 0,
            child: SettlementAwardWidget(
              awardList: widget.awardInfo!.awardItem!,
            ),
          ),
      ],
    );
  }
}
