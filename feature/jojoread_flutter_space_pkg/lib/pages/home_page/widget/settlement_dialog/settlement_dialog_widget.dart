import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/find_study_buddy/find_study_buddy_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/daily_rewards_detail_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/dialog_manager.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/settlement_dialog/settlement_result_widget.dart';

/// 显示弹窗
class PopupDialog {
  /// 弹窗管理器
  DialogManager dialogManager = DialogManager();

  /// popData 弹窗数据
  /// isFirstSeasonStarted 是否第一次是寻找学伴数据
  /// stageData 存储的宝箱 礼花spine下载数据
  /// hasEnteredRuleConfig 是否需要进入规则页面
  /// sensorParams 埋点公有数据
  /// handlePlay 打开关掉背景音乐

  showAwardDialog({
    required SpaceHomePopData popData,
    required bool isFirstSeasonStarted,
    required StageData? stageData,
    required bool hasEnteredRuleConfig,
    required Map<String, dynamic> sensorParams,
    required void Function(bool) handlePlay,
  }) {
    var popups = popData.popups;

    /// 如果在开始已经展示过寻找学伴，主页就不能展示学伴数据了
    if (popups != null && popups.isNotEmpty) {
      if (isFirstSeasonStarted) {
        popups = popups
            .where((item) => item.type != DialogType.startPopup.value)
            .toList();
      }
    }

    /// 过滤空数据
    final _filterPopups = [];
    popups?.forEach((popup) {
      if (popup.type == DialogType.dailyPopup.value) {
        if (popup.extend?.multiUserLearn?.finishTaskList != null) {
          _filterPopups.add(popup);
        }
      }
      if (popup.type == DialogType.weeklyPopup.value) {
        if (popup.extend?.multiUserLearn?.achInfo != null) {
          _filterPopups.add(popup);
        }
      }
      if (popup.type == DialogType.startPopup.value) {
        if (popup.extend?.multiUserLearn?.memberList != null) {
          _filterPopups.add(popup);
        }
      }
    });
    if (_filterPopups.isEmpty) {
      return;
    }
    final lastItem = _filterPopups.last;
    if (_filterPopups.isNotEmpty) {
      final _addDialog = _filterPopups.map((popup) {
        return DialogManagerType(
          onShowDialog: () {
            return SmartDialog.show(
              clickMaskDismiss:
                  popup.type == DialogType.dailyPopup.value ? true : false,
              onDismiss: () {
                dialogManager.hideDialog();

                /// 如果是最后一个弹窗关闭 需要打开背景音乐
                final isLast = popup == lastItem;
                if (isLast) {
                  handlePlay(true);
                }
              },
              builder: (context) {
                if (popup.type == DialogType.dailyPopup.value) {
                  return DailyRewardsDetailWidget(
                    extendData: popup.extend!,
                    sensorParams: sensorParams,
                    closeAudio: () {
                      handlePlay(false);
                    },
                    flowerSpineData: stageData?.flowerSpineData,
                  );
                }
                if (popup.type == DialogType.weeklyPopup.value) {
                  return SettlementResultWidget(
                    extendData: popup.extend!,
                    sensorParams: sensorParams,
                    boxSpineData: stageData?.boxSpineData,
                    onClose: () {},
                    closeAudio: () {
                      handlePlay(false);
                    },
                  );
                }
                if (popup.type == DialogType.startPopup.value) {
                  return FindStudyBuddyWidget(
                    extendData: popup.extend!,
                    onFindStudyBuddyClick: () {
                      SmartDialog.dismiss();
                    },
                    closeAudio: () {
                      handlePlay(false);
                    },
                  );
                }
                return const SizedBox();
              },
            );
          },
          priority: popup.order ?? 1,
        );
      }).toList();
      dialogManager.addDialog(_addDialog);
      dialogManager.onStart();
    }
  }
}
