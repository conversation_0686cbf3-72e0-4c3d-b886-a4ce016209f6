import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/controller.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/space_home_info_progress_bar_widget.dart';

import '../../../common/config/config.dart';
import '../../../common/host_env/host_env.dart';
import '../../../page.dart';
import '../../../static/img.dart';
import '../model/space_home_team_data.dart';
import '../state.dart';

class SpaceHomeInfoWeekChallengeWidget extends StatefulWidget {
  final SpaceHomePageState state;
  const SpaceHomeInfoWeekChallengeWidget({Key? key, required this.state})
      : super(key: key);

  @override
  SpaceHomeInfoWeekChallengeWidgetState createState() =>
      SpaceHomeInfoWeekChallengeWidgetState();
}

class SpaceHomeInfoWeekChallengeWidgetState
    extends State<SpaceHomeInfoWeekChallengeWidget> {
  int startTime = 0;
  int endTime = 0;
  bool hasBeenEnd = false;
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  String formatRemainingTime(int deadlineTimestamp) {
    // deadlineTimestamp = 1735117777000;
    // 获取当前时间的时间戳（毫秒）
    int currentTimestamp = DateTime.now().millisecondsSinceEpoch;
    // 计算剩余时间（毫秒）
    int remainingMilliseconds = deadlineTimestamp - currentTimestamp;
    if (remainingMilliseconds <= 0) {
      hasBeenEnd = true;
      return '已过期';
    }
    hasBeenEnd = false;
    // 将剩余时间转换为秒
    int remainingSeconds = remainingMilliseconds ~/ 1000;
    // 计算天、小时和分钟
    int days = remainingSeconds ~/ (24 * 3600);
    remainingSeconds %= (24 * 3600);
    int hours = remainingSeconds ~/ 3600;
    remainingSeconds %= 3600;
    int minutes = remainingSeconds ~/ 60;
    // 格式化字符串
    String result = '';
    if (days >= 0) {
      result += '$days天';
    }
    if (hours >= 0) {
      result += '$hours小时';
    }
    if (minutes > 0) {
      result += '$minutes分';
    }
    return '剩余$result';
  }

  @override
  Widget build(BuildContext context) {
    startTime = widget.state.groupData?.seasonInfo?.startTime ?? 0;
    endTime = widget.state.groupData?.seasonInfo?.endTime ?? 0;

    return Container(
      alignment: Alignment.center,
      height: 145.rdp,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(
          Radius.circular(24.0.rdp),
        ),
        border: Border.all(
          color: HexColor('#EDBF94'),
          width: 2.rdp,
        ),
        color: Colors.white,
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: -7.rdp,
            left: 23.rdp,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start, // 对齐方式
              children: [
                ImageAssetWeb(
                  assetName: AssetsImg.SPACE_PREGRESS_TITLE,
                  width: 182.rdp,
                  height: 30.rdp,
                  fit: BoxFit.contain,
                  package: Config.package,
                ),
                SizedBox(
                  height: 5.rdp,
                ),
                RichText(
                  text: TextSpan(
                    style: TextStyle(
                      fontSize: 14.rdp,
                      color: HexColor('#544300'),
                      fontWeight: FontWeight.w500,
                    ), // 使用默认文本样式
                    children: <TextSpan>[
                      TextSpan(
                        text: formatRemainingTime(endTime),
                        style: const TextStyle(color: Colors.red),
                      ),
                      TextSpan(
                        text: hasBeenEnd == true ? '' : '后结束',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 12.rdp,
            right: 20.rdp,
            child: GestureDetector(
                onTap: () {
                  List<Team> teams = widget.state.teamData?.teams ?? [];
                  RunEnv.sensorsTrack('\$AppClick', {
                    '\$element_name': '功能入口点击',
                    'custom_state': 'dfmx',
                    ...?pageController?.commonSensorParams()
                  });
                  if (teams.isNotEmpty) {
                    RunEnv.jumpLink(
                        "tinman-router://cn.tinman.jojoread/flutter/space/teamscore?teamId=${teams.first.teamId}");
                  }
                },
                child: Row(
                  children: [
                    Text(
                      '查看明细',
                      style: TextStyle(
                        fontSize: 14.rdp,
                        color: HexColor('#DBAF00'),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Image.asset(
                      AssetsImg.TRIANGLE,
                      width: 18.rdp,
                      height: 18.rdp,
                      package: Config.package,
                    ),
                  ],
                )),
          ),
          Positioned(
              top: 50.rdp,
              left: 0.rdp,
              right: 0.rdp,
              child: SpaceHomeInfoProgressBarWidget(
                state: widget.state,
              ))
        ],
      ),
    );
  }

  SpaceHomePageCtrl? get pageController =>
      mounted ? context.read<SpaceHomePageCtrl>() : null;
}
