import 'dart:async';
import 'dart:convert';

import 'package:jojo_flutter_base/utils/event_bus.dart';

import '../my_dress/state.dart';

EventBus eventBus = EventBus();

class CustomEventSyncUserDress {
  // 服装同步
  final List<String> dress;
  CustomEventSyncUserDress(this.dress);

  // 添加 toJson 方法，返回 JSON 字符串
  String toJson() {
    return jsonEncode({
      'dress': dress,
    });
  }

  // 添加 fromJson 方法
  factory CustomEventSyncUserDress.fromJson(String jsonString) {
    Map<String, dynamic> jsonMap = jsonDecode(jsonString);
    return CustomEventSyncUserDress(
      jsonMap['dress'] != null ? List<String>.from(jsonMap['dress']) : [],
    );
  }
}

class MallSnapshootnEvent {
  //商城的舞台快照
  final List<AnimationData>? animationList;
  Completer<List<Map<String, dynamic>>>? completer = Completer();
  MallSnapshootnEvent(this.animationList, {this.completer});
}

class MainPageDefaultSnapshootnEvent {
  //主页的默认形象选择的快照
  final List<AnimationData>? animationList;
  Completer<List<Map<String, dynamic>>>? completer = Completer();
  MainPageDefaultSnapshootnEvent(this.animationList, {this.completer});
}

class MallDefaultSnapshootnEvent {
  //商城的默认形象选择的快照
  final List<AnimationData>? animationList;
  Completer<List<Map<String, dynamic>>>? completer = Completer();
  MallDefaultSnapshootnEvent(this.animationList, {this.completer});
}

class DefaultPageSnapshootnEvent {
  //默认形象单页面的选择的快照
  final List<AnimationData>? animationList;
  Completer<List<Map<String, dynamic>>>? completer = Completer();
  DefaultPageSnapshootnEvent(this.animationList, {this.completer});
}
