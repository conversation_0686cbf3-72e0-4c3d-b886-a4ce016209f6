import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_skin_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/iterable_extension.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojoread_flutter_space_pkg/common/config/config.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_resource_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/widget/back_widget.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

import '../../utils/snap_shoot_util.dart';
import '../eventBus/space_home_custom_eventbus.dart';
import '../my_dress/state.dart';
import 'controller.dart';

/// 选择皮肤页面
class SpaceMallCharacterCustomizationWidgetPage extends StatefulWidget {
  const SpaceMallCharacterCustomizationWidgetPage({
    Key? key,
  }) : super(key: key);

  @override
  State<SpaceMallCharacterCustomizationWidgetPage> createState() =>
      _SpaceMallCharacterCustomizationState();
}

class _SpaceMallCharacterCustomizationState
    extends State<SpaceMallCharacterCustomizationWidgetPage> {
  final SwiperController _swiperController = SwiperController();
  final SwiperController _swiperBoxController = SwiperController();
  final GlobalKey _repaintBoundaryKey = GlobalKey();
  int currentIndex = 0;

  // 用于保存每个类别中选中的数据
  Map<String, SkinModel> selectedSkins = {};

  List<String> categoryKeys = [];

  Map<String, List<DefaultSkinList>> skinData = {};

  /// spine动画控制器
  JoJoSpineAnimationController animationController =
      JoJoSpineAnimationController();
  // spine控制器截图使用
  JoJoSpineAnimationController snapshotController =
      JoJoSpineAnimationController();

  StreamSubscription? _ChangeSkinAnimation;

  @override
  void initState() {
    super.initState();
    final ctr = context.read<SpaceCharacterPageCtrl>();
    final skinListData = ctr.state.guideSkinData?.defaultSkinList ?? [];
    final Map<String, List<DefaultSkinList>> _skinData =
        groupByCategoryShowName(skinListData);
    if (_skinData.isEmpty) {
      l.e("创建默认形象失败",
          '默认装扮defaultSkinList:: $skinListData, 装扮分组数据_skinData:::$_skinData');
      return;
    }

    List<String> _categoryKeys = _skinData.keys.toList();

    /// 初始化皮肤数据
    final currentCategory = _categoryKeys[0];
    final _currentSkins = _skinData[currentCategory]!;
    final _currentSkin = _currentSkins.first;
    selectedSkins[currentCategory] = SkinModel(
      id: _currentSkin.skinId,
      zipLocalFile: _currentSkin.resourcesZipUrl ?? '',
    );

    setState(() {
      selectedSkins = selectedSkins;
      categoryKeys = _categoryKeys;
      skinData = _skinData;
    });

    _ChangeSkinAnimation =
        jojoEventBus.on<DefaultPageSnapshootnEvent>().listen((event) {
      List<AnimationData>? animationList = event.animationList;
      startSnapshoots(animationList, snapshotController, _repaintBoundaryKey)
          .then((listOfMaps) {
        event.completer?.complete(listOfMaps);
      }).catchError((e) {
        event.completer?.completeError(e);
      });
    });
  }

  @override
  void dispose() {
    _swiperController.dispose();
    _swiperBoxController.dispose();
    _ChangeSkinAnimation?.cancel();
    super.dispose();
  }

  /// 切换数据
  void handleSwitchCategory() {
    final ctr = context.read<SpaceCharacterPageCtrl>();
    if (currentIndex == categoryKeys.length - 1) {
      final skinList = selectedSkins.values.toList();
      final List<int> skins = skinList.map((e) => e.id ?? 0).toList();
      final List<String> skinFiles =
          skinList.map((e) => e.zipLocalFile ?? "").toList();
      ctr.saveDefalutSkin(skins, skinFiles, _repaintBoundaryKey);
      // 保存当前装扮存缓存
    } else {
      _swiperBoxController.next();
    }
  }

  /// 选择皮肤
  handleSelectSkin(bool isNext) {
    if (isNext) {
      _swiperController.next(); // 切换到下一张图片
    } else {
      _swiperController.previous();
    }
  }

  /// swiper 切换更新spine动画
  handleUpdateSpine(String key, DefaultSkinList skin) {
    showLoadinDialog();
    final _skinInfo = SkinModel(
      id: skin.skinId,
      zipLocalFile: skin.resourcesZipUrl ?? '',
    );
    selectedSkins[key] = _skinInfo;
    setState(() {
      selectedSkins = selectedSkins;
    });
  }

  showLoadinDialog() {
    JoJoLoading.show(
      msg: "请稍候",
      textStyle: TextStyle(
        fontSize: 16.rdp,
        color: HexColor('#ACB2BB'),
        fontWeight: FontWeight.w500,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final ctr = context.read<SpaceCharacterPageCtrl>();
    final _spineData = ctr.state.guideSkinData?.spineData;
    final skinList = selectedSkins.values.toList();
    final _skinListData = ctr.state.guideSkinData?.defaultSkinList ?? [];
    if (skinList.isEmpty) {
      return Container();
    }
    return Stack(
      children: [
        Container(
            width: 375.rdp,
            height: 280.rdp,
            margin: EdgeInsets.only(top: 70.rdp, bottom: 40.rdp),
            child: RepaintBoundary(
                key: _repaintBoundaryKey,
                child: JoJoSpineSkinWidget(
                  _spineData!.atlasFile,
                  _spineData.skelFile,
                  "characterCustomization",
                  controller: snapshotController,
                  skinList: skinList,
                  loadSkinCallback: (status) {
                    if (status == SkinStatus.success) {
                      snapshotController.spineController?.skeleton
                          .setScaleX(0.95);
                      snapshotController.spineController?.skeleton
                          .setScaleY(0.95);
                      snapshotController.playAnimation(JoJoSpineAnimation(
                        animaitonName: "idle",
                        trackIndex: 0,
                        loop: true,
                        delay: 0,
                      ));
                    }

                    if (status != SkinStatus.start) {
                      JoJoLoading.dismiss();
                    }
                  },
                ))),
        ImageAssetWeb(
          assetName: AssetsImg.SKIN_BG,
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
          package: Config.package,
        ),
        SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 375.rdp,
                  height: 280.rdp,
                  margin: EdgeInsets.only(top: 70.rdp, bottom: 40.rdp),
                  child: JoJoSpineSkinWidget(
                    _spineData.atlasFile,
                    _spineData.skelFile,
                    "characterCustomization",
                    controller: animationController,
                    skinList: skinList,
                    loadSkinCallback: (status) {
                      if (status == SkinStatus.success) {
                        animationController.spineController?.skeleton
                            .setScaleX(0.95);
                        animationController.spineController?.skeleton
                            .setScaleY(0.95);
                        animationController.playAnimation(JoJoSpineAnimation(
                          animaitonName: "idle",
                          trackIndex: 0,
                          loop: true,
                          delay: 0,
                        ));
                      }
                    },
                  ),
                ),
                Text(
                  '创建你的形象！',
                  style: TextStyle(
                    color: HexColor('#664314'),
                    fontSize: 24.rdp,
                    fontWeight: FontWeight.bold,
                    height: 1.5,
                  ),
                ),
                SizedBox(height: 20.rdp),
                Text(
                  categoryShowNameByCode(
                    _skinListData,
                    categoryKeys[currentIndex],
                  ),
                  style: TextStyle(
                    color: HexColor('#C09660'),
                    fontSize: 20.rdp,
                    fontWeight: FontWeight.bold,
                    height: 1.5,
                  ),
                ),
                SizedBox(height: 20.rdp),
                SizedBox(
                  width: double.infinity,
                  height: 162.rdp,
                  child: Swiper(
                      controller: _swiperBoxController,
                      itemCount: categoryKeys.length,
                      physics: const NeverScrollableScrollPhysics(), // 禁用滑动
                      scrollDirection: Axis.horizontal,
                      onIndexChanged: (int outerIndex) {
                        final currentCategory = categoryKeys[outerIndex];
                        final currentSkins = skinData[currentCategory]!;
                        final skin = currentSkins[0];
                        handleUpdateSpine(
                          currentCategory,
                          skin,
                        );
                        setState(() {
                          currentIndex = outerIndex;
                        });
                      },
                      itemBuilder: (BuildContext context, int outerIndex) {
                        final currentCategory = categoryKeys[outerIndex];
                        final currentSkins = skinData[currentCategory]!;

                        return FractionallySizedBox(
                          widthFactor: 0.9, // 宽度占外层的 90%
                          child: Container(
                            width: 335.rdp,
                            height: 162.rdp,
                            padding: EdgeInsets.all(20.rdp),
                            decoration: BoxDecoration(
                              color: HexColor('#FFFFFF'),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(52.rdp)),
                              border: Border.all(
                                color: HexColor('#E0BF94'),
                                width: 2.rdp,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    handleSelectSkin(false);
                                  },
                                  child: Transform.rotate(
                                    angle: pi,
                                    child: ImageAssetWeb(
                                      assetName: AssetsImg.TRIANGLE,
                                      width: 48.rdp,
                                      height: 48.rdp,
                                      package: Config.package,
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  width: 190.rdp,
                                  height: 120.rdp,
                                  child: Swiper(
                                    key: ValueKey(currentIndex),
                                    autoplay: false,
                                    loop: false,
                                    controller: _swiperController,
                                    scrollDirection: Axis.horizontal,
                                    itemCount: currentSkins.length,
                                    onIndexChanged: (int index) {
                                      final skin = currentSkins[index];
                                      handleUpdateSpine(
                                        currentCategory,
                                        skin,
                                      );
                                    },
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      final skin = currentSkins[index];
                                      final _spineSkinUrl =
                                          skin.dressShowUrl ?? '';
                                      bool isHttp =
                                          _spineSkinUrl.startsWith('http');
                                      return Center(
                                        child: Stack(
                                          children: [
                                            SizedBox(
                                              width: 190.rdp,
                                              height: 120.rdp,
                                              child: isHttp
                                                  ? ImageNetworkCached(
                                                      height: 190.rdp,
                                                      width: 120.rdp,
                                                      imageUrl:
                                                          skin.dressShowUrl ??
                                                              '',
                                                    )
                                                  : Image(
                                                      image: FileImage(
                                                        File(_spineSkinUrl),
                                                      ),
                                                      width: 190.rdp,
                                                      fit: BoxFit.fitWidth,
                                                      frameBuilder: (context,
                                                          child,
                                                          frame,
                                                          wasSynchronouslyLoaded) {
                                                        // 如果图像在同步加载中完成，则直接返回图像组件
                                                        if (frame == null) {
                                                          return const SizedBox();
                                                        }
                                                        return child;
                                                      },
                                                    ),
                                            ),
                                            // 左侧渐变遮罩
                                            Positioned.fill(
                                              child: Align(
                                                alignment: Alignment.centerLeft,
                                                child: Container(
                                                  width: 50.rdp, // 设置渐变遮罩宽度
                                                  decoration: BoxDecoration(
                                                    gradient: LinearGradient(
                                                      begin:
                                                          Alignment.centerLeft,
                                                      end:
                                                          Alignment.centerRight,
                                                      colors: [
                                                        Colors.white, // 左侧白色
                                                        Colors.white
                                                            .withOpacity(
                                                                0), // 渐变到透明
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            // 右侧渐变遮罩
                                            Positioned.fill(
                                              child: Align(
                                                alignment:
                                                    Alignment.centerRight,
                                                child: Container(
                                                  width: 50.rdp, // 设置渐变遮罩宽度
                                                  decoration: BoxDecoration(
                                                    gradient: LinearGradient(
                                                      begin:
                                                          Alignment.centerRight,
                                                      end: Alignment.centerLeft,
                                                      colors: [
                                                        Colors.white,
                                                        Colors.white
                                                            .withOpacity(
                                                                0), // 渐变到透明
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () {
                                    handleSelectSkin(true);
                                  },
                                  child: ImageAssetWeb(
                                    assetName: AssetsImg.TRIANGLE,
                                    width: 48.rdp,
                                    height: 48.rdp,
                                    package: Config.package,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }),
                ),
                SizedBox(height: 20.rdp),
                GestureDetector(
                  onTap: () {
                    handleSwitchCategory();
                  },
                  child: Container(
                    width: 132.rdp,
                    padding: EdgeInsets.symmetric(
                      vertical: 8.rdp,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(30.rdp)),
                      color: const Color(0xffFFD738),
                    ),
                    child: Center(
                      child: Text(
                        currentIndex == categoryKeys.length - 1 ? '完成!' : '下一步',
                        style: TextStyle(
                          fontSize: 18.rdp,
                          color: HexColor('#544300'),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const BackWidget(),
      ],
    );
  }
}

/// 装扮分组
Map<String, List<DefaultSkinList>> groupByCategoryShowName(
    List<DefaultSkinList> skins) {
  Map<String, List<DefaultSkinList>> grouped = {};

  for (var skin in skins) {
    String? category = skin.categoryShowCode.toString();
    if (category != '') {
      if (!grouped.containsKey(category)) {
        grouped[category] = [];
      }
      grouped[category]!.add(skin);
    }
  }

  return grouped;
}

/// 寻找分类名称
String categoryShowNameByCode(List<DefaultSkinList> list, String code) {
  final _info = list.firstWhereOrNull(
      (element) => element.categoryShowCode == int.parse(code));
  return _info?.categoryShowName ?? '';
}
