import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';

import 'controller.dart';
import 'state.dart';
import 'view.dart';

/// 多人学我的装扮/商城页面
class SpaceCharacterPageModel extends BasePage {
  const SpaceCharacterPageModel({
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _SpaceCharacterPageModelState();
}

class _SpaceCharacterPageModelState extends BaseState<SpaceCharacterPageModel>
    with BasicInitPage {
  late final SpaceCharacterPageCtrl _controller = SpaceCharacterPageCtrl();
  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => _controller,
      child: BlocBuilder<SpaceCharacterPageCtrl, SpaceCharacterPageState>(
          builder: (context, state) {
        return SpaceHistoryPageView(state: state);
      }),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
