import 'package:jojo_flutter_base/widgets/common/page_loading.dart';

import '../home_page/model/space_home_resource_data.dart';
import '../home_page/model/space_mall_resource_data.dart';
import '../my_dress/state.dart';

class SpaceCharacterPageState {
  PageStatus pageStatus;
  SpaceMallResourceData? spaceMallResourceData;
  SpaceHomeResourceData? spaceHomeResourceData;
  ShopStagePersonData? stagePersonData; //舞台小人数据
  List<AnimationData>? animationList;

  NewMallGuideSkinData? guideSkinData; //引导换肤弹窗数据
  Exception? error;
  Exception? exception;

  SpaceCharacterPageState(
      {required this.pageStatus,
      this.error,
      this.spaceHomeResourceData,
      this.stagePersonData,
      this.spaceMallResourceData,
      this.guideSkinData,
      this.animationList,
      this.exception});

  SpaceCharacterPageState copyWith() {
    return SpaceCharacterPageState(
        pageStatus: pageStatus,
        exception: exception,
        spaceHomeResourceData: spaceHomeResourceData,
        spaceMallResourceData: spaceMallResourceData,
        stagePersonData: stagePersonData,
        animationList: animationList,
        guideSkinData: guideSkinData,
        error: error);
  }
}
