import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_skin_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojoread_flutter_space_pkg/service/space_home_api.dart';
import 'package:jojoread_flutter_space_pkg/utils/file_util.dart';
import 'package:jojoread_flutter_space_pkg/utils/snap_shoot_util.dart';

import '../../common/host_env/host_env.dart';
import '../eventBus/space_home_custom_eventbus.dart';
import '../home_page/controller.dart';
import '../home_page/model/space_home_resource_data.dart';
import '../home_page/model/space_mall_resource_data.dart';
import '../home_page/state.dart';
import '../my_dress/state.dart';
import 'state.dart';

class SpaceCharacterPageCtrl extends Cubit<SpaceCharacterPageState> {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  late final SpaceHomeApi _homeApiService;
  late final JoJoResourceManager? _resourceManager;

  SpaceCharacterPageCtrl({
    JoJoResourceManager? manager,
    SpaceHomeApi? homeApiService,
  }) : super(
          SpaceCharacterPageState(
            pageStatus: PageStatus.loading,
            guideSkinData: NewMallGuideSkinData(),
          ),
        ) {
    _homeApiService = homeApiService ?? spaceHomeApiService;
    _resourceManager = manager ?? JoJoResourceManager();
    onRefresh();
  }

  onRefresh() async {
    //请求接口
    try {
      //请求商城资源 分类数据
      SpaceMallResourceData? spaceMallResourceData =
          const SpaceMallResourceData();
      spaceMallResourceData = await _homeApiService.requestMallResourceInfo();
      // JSON字符串
      // String jsonString =
      //     '{"animationList":[{"type":"daily","animationgName":"idle"},{"type":"daily","animationgName":"pose1"},{"type":"activity","animationgName":"pose2"}]}';
      // jsonString = spaceMallResourceData.dressImgGenerateConfig ?? '';
      // 将JSON字符串反序列化为Dart对象
      String jsonString = spaceMallResourceData.dressImgGenerateConfig ?? '';
      List<AnimationData> animationList = [];
      if (jsonString.isNotEmpty) {
        final Map<String, dynamic> jsonData = jsonDecode(jsonString);
        // 将List<dynamic>转换为List<Animation>
        animationList = (jsonData['animationList'] as List)
            .map((item) => AnimationData.fromJson(item))
            .toList();
      }
      SpaceHomeResourceData? spaceHomeResourceData;
      //首页资源(请求默认皮肤)
      spaceHomeResourceData =
          await _homeApiService.requestSpaceHomeResourceInfo();
      state.pageStatus = PageStatus.success;
      state.spaceMallResourceData = spaceMallResourceData;
      state.spaceHomeResourceData = spaceHomeResourceData;
      state.animationList = animationList;
      //资源下载
      await downLoadResource();
    } catch (e) {
      //请求失败
      var exception = Exception("请求接口失败,$e");
      if (e is Exception) {
        exception = e;
      }
      final newState = state.copyWith()
        ..pageStatus = PageStatus.error
        ..exception = exception;
      l.e(logTag, "请求接口失败,$e");
      print(
          '💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣-报错了$e');
      emit(newState);
    }
  }

//资源下载
  downLoadResource() {
    List<String> urls = [];
    //舞台背景，骨骼
    urls.addListsIfNotEmpty([
      state.spaceMallResourceData?.memberBonesZipUrl,
      // state.spaceMallResourceData?.backGroundBonesZipUrl,
    ]);
    //默认皮肤
    state.spaceHomeResourceData?.ownAndDefaultSkinList?.forEach((element) {
      urls.addIfNotEmpty(element.resourcesZipUrl);
    });

    _resourceManager?.downloadUrl(urls, progressListener: (progress) {
      //下载进度
      print("下载进度:$progress");
    }, successListener: (map) async {
      // 处理下载之后的资源
      await handleGuideSkinData(map);
      await handlePersonSpineData(map);

      emit(state.copyWith());
    }, failListener: (_url) {
      //下载失败
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      l.e(logTag, "资源下载失败 $_url");
    });
  }

  ///处理默认皮肤数据
  handleGuideSkinData(Map map) {
    if (state.spaceHomeResourceData?.ownAndDefaultSkinList == null ||
        state.spaceHomeResourceData?.ownAndDefaultSkinList?.isEmpty == true) {
      //没有默认皮肤数据,显示加载失败
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      return;
    }
    state.guideSkinData?.defaultSkinList = [];
    state.spaceHomeResourceData?.ownAndDefaultSkinList?.forEach((element) {
      state.guideSkinData?.defaultSkinList?.add(DefaultSkinList(
        skinId: element.skinId,
        categoryShowCode: element.categoryShowCode,
        categoryShowName: element.categoryShowName,
        resourcesZipUrl: map[element.resourcesZipUrl],
        dressShowUrl: element.dressShowUrl,
      ));
    });

    print('object');
  }

  ///处理人物模型数据
  handlePersonSpineData(Map map) async {
    var memberBonesZipUrl = map[state.spaceMallResourceData?.memberBonesZipUrl];
    if (memberBonesZipUrl != null) {
      var unzipFile = await unzip(memberBonesZipUrl);
      var skinZipFile = findFilesByName(unzipFile, "defaultSkin.zip");
      var atlasFile = findFilesByExtensionByDic(unzipFile, "atlas");
      var skelFile = findFilesByExtensionByDic(unzipFile, "skel");
      skelFile ??= findFilesByExtensionByDic(unzipFile, "json");
      if (atlasFile != null && skelFile != null) {
        //找到atlas文件和skel文件
        List<SkinModel> skinList = [];
        //默认皮肤
        if (skinZipFile != null) {
          skinList.add(SkinModel(id: 0, zipLocalFile: skinZipFile.path));
        }
        //默认皮肤
        state.guideSkinData?.spineData = StageSpineData(
          skelFile: skelFile.path,
          atlasFile: atlasFile.path,
        );
        final stageData = ShopStagePersonData(
            spineData: StageSpineData(
                skelFile: skelFile.path,
                atlasFile: atlasFile.path,
                skinList: skinList),
            defaultSkinZip: skinZipFile?.path);
        state.stagePersonData = stageData;
      } else {
        final newState = state.copyWith()..pageStatus = PageStatus.error;
        emit(newState);
        l.e(logTag, "handlePersonSpineData 找不到文件 $atlasFile,$skelFile");
      }
    } else {
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      l.e(logTag,
          "handlePersonSpineData 找不到压缩包,${state.spaceMallResourceData?.memberBonesZipUrl}");
    }
  }

  Future<void> _snapshot(List<int> skins) async {
    //开始人物形象快照，并上传图片获取oss地址
    Completer<List<Map<String, dynamic>>>? completer = Completer();
    jojoEventBus.fire(DefaultPageSnapshootnEvent(
      state.animationList,
      completer: completer,
    ));
    List<Map<String, dynamic>> resultMap = await completer.future.timeout(
      const Duration(seconds: 5),
      onTimeout: () {
        print("等待超时");
        return [{}]; // 返回一个空的结果
      },
    );
    // 找出本地默认的截图
    String? ossPath = resultMap.firstWhere(
      (map) => map['type'] == 'daily',
      orElse: () => {},
    )['dressImg'] as String?;
    await _homeApiService.saveSkinInfo(skins, ossPath ?? '', resultMap);
  }

  /// 保存换肤
  void saveDefalutSkin(List<int> skins, List<String> skinLocalFiles,
      GlobalKey? repaintBoundaryKey) async {
    try {
      JoJoLoading.show();
      await _snapshot(skins);
      JoJoLoading.dismiss();
      RunEnv.pop();
    } catch (e) {
      deleteSnapshotWhenError();
      JoJoLoading.dismiss();
      l.e(logTag, '保存皮肤错误：$e');
    }
  }

  void dispose() {}
}

const historyTag = "SpaceHistoryController";
