import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_lce.dart';
import 'package:jojoread_flutter_space_pkg/pages/character_customization/space_character_customization_tempwidget.dart';
import 'package:jojoread_flutter_space_pkg/pages/character_customization/state.dart';

import 'controller.dart';

class SpaceHistoryPageView extends StatefulHookWidget {
  final SpaceCharacterPageState state;
  const SpaceHistoryPageView({Key? key, required this.state}) : super(key: key);
  @override
  SpaceHistoryPageViewState createState() => SpaceHistoryPageViewState();
}

class SpaceHistoryPageViewState extends State<SpaceHistoryPageView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        primary: !JoJoRouter.isWindow,
        body: JoJo<PERSON>ageLecLoading(
            status: widget.state.pageStatus,
            emptyText: '暂无历史战绩',
            retry: () {
              context.read<SpaceCharacterPageCtrl>().onRefresh();
            },
            exception: widget.state.error,
            child: const SpaceMallCharacterCustomizationWidgetPage()));
  }
}
