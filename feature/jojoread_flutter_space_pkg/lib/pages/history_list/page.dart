import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojoread_flutter_space_pkg/pages/history_list/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/history_list/view.dart';

import 'controller.dart';

/// 多人学个人历史战绩
class SpaceHistoryPageModel extends BasePage {
  const SpaceHistoryPageModel({
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _SpaceHistoryPageModelState();
}

class _SpaceHistoryPageModelState extends BaseState<SpaceHistoryPageModel>
    with BasicInitPage {
  late final SpaceHistoryPageCtrl _controller = SpaceHistoryPageCtrl();
  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => _controller,
      child: BlocBuilder<SpaceHistoryPageCtrl, SpaceHistoryPageState>(
          builder: (context, state) {
        return SpaceHistoryPageView(state: state);
      }),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
