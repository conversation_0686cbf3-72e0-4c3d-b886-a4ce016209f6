import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/btn.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_history_data.dart';

import '../../../../common/config/config.dart';
import '../../../../static/img.dart';

/// 奖励信息
class AwardDialogWidget extends StatefulWidget {
  final List<AwardList> awardList;

  const AwardDialogWidget({
    Key? key,
    required this.awardList,
  }) : super(key: key);

  @override
  AwardDialogWidgetState createState() => AwardDialogWidgetState();
}

class AwardDialogWidgetState extends State<AwardDialogWidget> {
  @override
  Widget build(BuildContext context) {
    /// 奖励列表每一行最多展示三个居中展示，第一行展示余数数据
    List<List<AwardList>> rewardRows = buildRewardRows(
      widget.awardList,
      maxPerRow: 3,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '获得的奖励！',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: HexColor('#FFFFFF'),
            fontSize: 28.rdp,
            fontWeight: FontWeight.w400,
            height: 1.5,
          ),
        ),
        SizedBox(height: 50.rdp),
        ...rewardRows.map(
          (row) => Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ...row.map(
                (item) => Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 7.rdp,
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(top: 16.rdp),
                    child: Column(
                      children: [
                        Stack(
                          children: [
                            ImageAssetWeb(
                              width: 100.rdp,
                              height: 100.rdp,
                              assetName: AssetsImg.SPACE_HOME_AWARD_BG,
                              package: Config.package,
                            ),
                            Positioned(
                              top: 0,
                              bottom: 0,
                              right: 0,
                              left: 0,
                              child: Center(
                                child: ImageNetworkCached(
                                  imageUrl: item.awardCover ?? "",
                                  width: 58.rdp,
                                  height: 71.rdp,
                                ),
                              ),
                            ),
                            if (item.awardCount != null && item.awardCount != 0)
                              Positioned(
                                bottom: 5.rdp,
                                right: 10.rdp,
                                child: Row(
                                  children: [
                                    ImageAssetWeb(
                                      assetName: AssetsImg.SPACE_NUM_X,
                                      width: 16.rdp,
                                      height: 30.rdp,
                                      package: Config.package,
                                    ),
                                    ...(item.awardCount!)
                                        .toString()
                                        .split('')
                                        .map(
                                          (String num) => ImageAssetWeb(
                                            assetName: spaceNumMap[num] ?? "",
                                            width: 16.rdp,
                                            height: 30.rdp,
                                            package: Config.package,
                                          ),
                                        )
                                  ],
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: 8.rdp),
                        Text(
                          item.awardName ?? "",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: HexColor('#FFFFFF'),
                            fontSize: 16.rdp,
                            fontWeight: FontWeight.w400,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 30.rdp),
        JoJoBtn(
          text: '好的',
          width: 132.rdp,
          height: 44.rdp,
          tapHandle: () {
            SmartDialog.dismiss();
          },
        )
      ],
    );
  }
}

// 数字和图片关系
const spaceNumMap = {
  "0": AssetsImg.SPACE_NUM_0,
  "1": AssetsImg.SPACE_NUM_1,
  "2": AssetsImg.SPACE_NUM_2,
  "3": AssetsImg.SPACE_NUM_3,
  "4": AssetsImg.SPACE_NUM_4,
  "5": AssetsImg.SPACE_NUM_5,
  "6": AssetsImg.SPACE_NUM_6,
  "7": AssetsImg.SPACE_NUM_7,
  "8": AssetsImg.SPACE_NUM_8,
  "9": AssetsImg.SPACE_NUM_9,
};

/// 方法：将奖励数据分组为每行的数据
List<List<AwardList>> buildRewardRows(List<AwardList> rewards,
    {required int maxPerRow}) {
  List<List<AwardList>> rewardRows = [];
  int remainder = rewards.length % maxPerRow;

  // 第一行：余数部分
  if (remainder > 0) {
    rewardRows.add(rewards.sublist(0, remainder));
  }

  // 后续行：完整行
  for (int i = 0; i < rewards.length ~/ maxPerRow; i++) {
    rewardRows.add(rewards.sublist(
        remainder + i * maxPerRow, remainder + (i + 1) * maxPerRow));
  }

  return rewardRows;
}
