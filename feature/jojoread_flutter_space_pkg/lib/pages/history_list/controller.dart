import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_history_data.dart';
import 'package:jojoread_flutter_space_pkg/service/space_home_api.dart';

import 'state.dart';

class SpaceHistoryPageCtrl extends Cubit<SpaceHistoryPageState> {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  late final SpaceHomeApi _homeApiService;

  SpaceHistoryPageCtrl({
    SpaceHomeApi? homeApiService,
  }) : super(
          SpaceHistoryPageState(
            pageStatus: PageStatus.loading,
          ),
        ) {
    _homeApiService = homeApiService ?? spaceHomeApiService;
    onRefresh();
  }

  onRefresh() async {
    //请求接口
    try {
      var spaceHomeHistoryData =
          await _homeApiService.requestHistoryPerformancesInfo();
      List<HistoryPerformance>? contributions =
          spaceHomeHistoryData.historyPerformances ?? [];
      if (contributions.isNotEmpty) {
        emit(
          state.copyWith()
            ..pageStatus = PageStatus.success
            ..historyData = spaceHomeHistoryData,
        );
      } else {
        final newState = state.copyWith()..pageStatus = PageStatus.empty;
        emit(newState);
      }
    } on Exception catch (e) {
      //请求失败
      final newState = state.copyWith()
        ..pageStatus = PageStatus.error
        ..error = e;
      emit(newState);
      l.e(historyTag, "error: ${e.toString()}");
    } on Error catch (e) {
      l.e(historyTag, "error: ${e.stackTrace}");
    }
  }

  void dispose() {}
}

const historyTag = "SpaceHistoryController";
