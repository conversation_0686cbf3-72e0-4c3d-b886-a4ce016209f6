import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_lce.dart';
import 'package:jojoread_flutter_space_pkg/common/config/config.dart';
import 'package:jojoread_flutter_space_pkg/pages/history_list/award_dialog.dart';
import 'package:jojoread_flutter_space_pkg/pages/history_list/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_history_data.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

import '../home_page/widget/back_widget.dart';
import 'controller.dart';

class SpaceHistoryPageView extends StatefulHookWidget {
  final SpaceHistoryPageState state;
  const SpaceHistoryPageView({Key? key, required this.state}) : super(key: key);
  @override
  PlanHistoryViewState createState() => PlanHistoryViewState();
}

class PlanHistoryViewState extends State<SpaceHistoryPageView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void showAwardDialog(List<AwardList> awardList) {
    SmartDialog.show(
      clickMaskDismiss: false,
      builder: (context) {
        return AwardDialogWidget(awardList: awardList);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    List<HistoryPerformance> historyList =
        widget.state.historyData?.historyPerformances ?? [];
    return Scaffold(
      backgroundColor: Colors.white,
      primary: !JoJoRouter.isWindow,
      appBar: const JoJoAppBar(
          title: '历史战绩', backgroundColor: Colors.white, centerTitle: true),
      body: JoJoPageLecLoading(
        status: widget.state.pageStatus,
        emptyText: '暂无历史战绩',
        retry: () {
          context.read<SpaceHistoryPageCtrl>().onRefresh();
        },
        exception: widget.state.error,
        child: ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: historyList.length,
          itemBuilder: (context, index) {
            var historyItem = historyList[index];
            return Container(
              margin: EdgeInsets.fromLTRB(20.rdp, 15.rdp, 20.rdp, 0),
              alignment: Alignment.center,
              height: 160.rdp,
              color: Colors.white,
              child: Stack(
                children: [
                  Image.asset(
                    historyItem.teamStatus == 2
                        ? AssetsImg.SPACE_HISTORY_GOD_BG
                        : (historyItem.teamStatus == 1
                            ? AssetsImg.SPACE_HISTORY_GARDEN_BG
                            : AssetsImg.SPACE_HISTORY_FAIL_BG),
                    height: 160.rdp,
                    width: double.infinity,
                    fit: BoxFit.fill,
                    package: Config.package,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(width: 15.rdp),
                      Expanded(
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: 18.rdp),
                              Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    if ((historyItem.infoTags?.length ?? 0) > 0)
                                      Padding(
                                        padding: EdgeInsets.only(left: 10.rdp),
                                        child: _buildItemContent(
                                            historyItem.infoTags?[0]),
                                      ),
                                    SizedBox(width: 14.rdp),
                                    if ((historyItem.infoTags?.length ?? 0) > 1)
                                      Padding(
                                        padding: EdgeInsets.only(left: 10.rdp),
                                        child: _buildItemContent(
                                            historyItem.infoTags?[1]),
                                      ),
                                  ]),
                              SizedBox(height: 8.rdp),
                              if ((historyItem.infoTags?.length ?? 0) > 2)
                                Padding(
                                  padding: EdgeInsets.only(left: 10.rdp),
                                  child: _buildItemContent(
                                      historyItem.infoTags?[2]),
                                ),
                              SizedBox(height: 8.rdp),
                              GestureDetector(
                                onTap: () {
                                  if (historyItem.awardInfos?.isNotEmpty ==
                                      true) {
                                    showAwardDialog(historyItem.awardInfos!);
                                  }
                                },
                                child: Container(
                                    width: 116.rdp,
                                    height: 30.rdp,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        color: historyItem
                                                    .awardInfos?.isNotEmpty ==
                                                true
                                            ? HexColor('#FCDA00')
                                            : HexColor('#F5F4F4'),
                                        borderRadius:
                                            BorderRadius.circular(15.rdp)),
                                    child: Text(
                                        historyItem.awardInfos?.isNotEmpty ==
                                                true
                                            ? "查看奖励"
                                            : "无奖励",
                                        style: TextStyle(
                                          fontSize: 16.rdp,
                                          color: historyItem
                                                      .awardInfos?.isNotEmpty ==
                                                  true
                                              ? HexColor('#6B430B')
                                              : HexColor('#B2B2B2'),
                                          fontWeight: FontWeight.w500,
                                        ))),
                              )
                            ]),
                      ),
                      SizedBox(
                        width: 146.rdp,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            ImageNetworkCached(
                              imageUrl: historyItem.teamResUrl ?? '',
                              fit: BoxFit.contain,
                              width: 126.rdp,
                              height: 126.rdp,
                            ),
                            Text(
                              "第${historyItem.order}次 ${historyItem.teamStatusDesc}",
                              style: TextStyle(
                                fontSize: 16.rdp,
                                color: historyItem.teamStatus == 2
                                    ? HexColor('#452270')
                                    : (historyItem.teamStatus == 1
                                        ? HexColor('#544300')
                                        : HexColor('#404040')),
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 15.rdp)
                    ],
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildItemContent(InfoTag? tag) {
    if (tag == null) {
      return Container();
    }
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            tag.title ?? '',
            style: TextStyle(
              fontSize: 12.rdp,
              color: HexColor('#9B989F'),
              fontWeight: FontWeight.w400,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 5.rdp),
          Text(
            tag.content ?? '',
            style: TextStyle(
              fontSize: 14.rdp,
              color: HexColor('#404040'),
              fontWeight: FontWeight.w400,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ]);
  }
}
