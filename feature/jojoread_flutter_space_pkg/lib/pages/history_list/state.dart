import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_history_data.dart';

class SpaceHistoryPageState {
  PageStatus pageStatus;
  SpaceHistoryData? historyData;
  Exception? error;

  SpaceHistoryPageState(
      {required this.pageStatus, this.historyData, this.error});

  SpaceHistoryPageState copyWith() {
    return SpaceHistoryPageState(
        pageStatus: pageStatus, historyData: historyData, error: error);
  }
}
