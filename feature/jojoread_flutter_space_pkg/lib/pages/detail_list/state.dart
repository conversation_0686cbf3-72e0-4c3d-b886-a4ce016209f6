import 'package:jojo_flutter_base/widgets/common/page_loading.dart';

import '../home_page/model/space_home_contributions_data.dart';

class SpaceTeamDetailsPageState {
  PageStatus pageStatus;
  Exception? exception;
  SpaceHomeContributionsData? spaceHomeContributionsData;
  SpaceTeamDetailsPageState({
    required this.pageStatus,
    required this.spaceHomeContributionsData,
  });

  SpaceTeamDetailsPageState copyWith() {
    return SpaceTeamDetailsPageState(
      pageStatus: pageStatus,
      spaceHomeContributionsData: spaceHomeContributionsData,
    );
  }
}
