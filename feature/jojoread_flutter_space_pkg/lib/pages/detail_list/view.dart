import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_lce.dart';

import '../home_page/model/space_home_contributions_data.dart';
import '../home_page/widget/back_widget.dart';
import 'controller.dart';
import 'state.dart';

class SpaceTeanDetailsPageView extends StatefulHookWidget {
  final SpaceTeamDetailsPageState state;
  const SpaceTeanDetailsPageView({Key? key, required this.state})
      : super(key: key);
  @override
  DetailsViewState createState() => DetailsViewState();
}

class DetailsViewState extends State<SpaceTeanDetailsPageView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  String formatTimestampToYmd(int timestamp) {
    if (timestamp <= 0) {
      return '0000.00.00';
    }
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    int year = dateTime.year;
    int month = dateTime.month;
    int day = dateTime.day;
    String formattedDate =
        '$year.${month.toString().padLeft(2, '0')}.${day.toString().padLeft(2, '0')}';
    return formattedDate;
  }

  @override
  Widget build(BuildContext context) {
    List<ContributionDetail> contributions =
        widget.state.spaceHomeContributionsData?.contributions ?? [];
    return Scaffold(
      backgroundColor: Colors.white,
      primary: !JoJoRouter.isWindow,
      appBar: const JoJoAppBar(
          title: '得分明细', backgroundColor: Colors.white, centerTitle: true),
      body: JoJoPageLecLoading(
        status: widget.state.pageStatus,
        exception: widget.state.exception,
        emptyText: '暂无数据',
        retry: () {
          context.read<SpaceTeamDetailsPageCtrl>().onRefresh();
        },
        child: ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: contributions.length,
          itemBuilder: (context, index) {
            ContributionDetail contributionDetail = contributions[index];
            String date =
                formatTimestampToYmd(contributionDetail.contributionDate ?? 0);
            String des = contributionDetail.contributionDesc ?? '';
            String desString = '$date  $des';
            return Container(
              padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
              height: 73.rdp,
              color: Colors.white,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ImageNetworkCached(
                    imageUrl: contributionDetail.userAvatar ?? '',
                    fit: BoxFit.contain,
                    width: 40.rdp,
                    height: 40.rdp,
                    borderRadius: 20.rdp,
                  ),
                  SizedBox(width: 8.rdp),
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        contributionDetail.userName ?? '',
                        style: TextStyle(
                          fontSize: 16.rdp,
                          color: HexColor('#404040'),
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.rdp),
                      Text(
                        desString,
                        style: TextStyle(
                          fontSize: 14.rdp,
                          color: HexColor('#B2B2B2'),
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                      )
                    ],
                  )),
                  SizedBox(
                    width: 70.rdp,
                    child: Text(
                      '+${contributionDetail.contributionVal?.toString() ?? '0'}分',
                      style: TextStyle(
                        fontSize: 16.rdp,
                        color: HexColor('#FF9045'),
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                    ),
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
