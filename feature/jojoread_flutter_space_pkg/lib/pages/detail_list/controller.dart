import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';

import '../../service/space_home_api.dart';
import '../home_page/model/space_home_contributions_data.dart';
import 'state.dart';

class SpaceTeamDetailsPageCtrl extends Cubit<SpaceTeamDetailsPageState> {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  late final SpaceHomeApi _homeApiService;
  final int teamId;
  SpaceTeamDetailsPageCtrl({
    JoJoResourceManager? manager,
    SpaceHomeApi? homeApiService,
    required this.teamId,
  }) : super(
          SpaceTeamDetailsPageState(
            pageStatus: PageStatus.loading,
            spaceHomeContributionsData: null,
          ),
        ) {
    _homeApiService = homeApiService ?? spaceHomeApiService;

    onRefresh();
  }

  onRefresh() async {
    //请求接口
    try {
      //请求舞台上的当前皮肤
      SpaceHomeContributionsData? spaceHomeContributionsData =
          const SpaceHomeContributionsData();
      spaceHomeContributionsData =
          await _homeApiService.requestTeamContributionsInfo(teamId);
      List<ContributionDetail>? contributions =
          spaceHomeContributionsData.contributions ?? [];
      if (contributions.isNotEmpty) {
        emit(
          state.copyWith()
            ..pageStatus = PageStatus.success
            ..spaceHomeContributionsData = spaceHomeContributionsData,
        );
      } else {
        final newState = state.copyWith()..pageStatus = PageStatus.empty;
        emit(newState);
      }
    } catch (e) {
      //请求失败
      var exception = Exception("请求接口失败,$e");
      if (e is Exception) {
        exception = e;
      }
      final newState = state.copyWith()
        ..pageStatus = PageStatus.error
        ..exception = exception;
      print(
          '💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣-报错了$e');
      emit(newState);
    }
  }

  void dispose() {}
}
