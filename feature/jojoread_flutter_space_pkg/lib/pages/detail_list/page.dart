import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';

import 'controller.dart';
import 'state.dart';
import 'view.dart';

/// 多人学战队贡献,积分明细
class SpaceTeamDetailsPageModel extends BasePage {
  final int teamId;
  const SpaceTeamDetailsPageModel(
    this.teamId, {
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _SpaceTeamDetailsPageModelState();
}

class _SpaceTeamDetailsPageModelState
    extends BaseState<SpaceTeamDetailsPageModel> with BasicInitPage {
  late final SpaceTeamDetailsPageCtrl _controller =
      SpaceTeamDetailsPageCtrl(teamId: widget.teamId);
  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => _controller,
      child: BlocBuilder<SpaceTeamDetailsPageCtrl, SpaceTeamDetailsPageState>(
          builder: (context, state) {
        return SpaceTeanDetailsPageView(state: state);
      }),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
