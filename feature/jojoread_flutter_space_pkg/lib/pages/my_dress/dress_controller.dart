import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/models/exception_data.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_skin_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/utils/unified_exception_util.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import 'package:jojo_flutter_base/widgets/jojo_dialog.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojoread_flutter_space_pkg/generated/intl/messages_zh_Hans.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/controller.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/model/my_dress_balance_data.dart';
import 'package:jojoread_flutter_space_pkg/utils/file_util.dart';
import 'package:jojo_flutter_base/utils/iterable_extension.dart';
import 'package:path/path.dart';

import '../../common/host_env/host_env.dart';
import '../../service/space_home_api.dart';
import '../../utils/snap_shoot_util.dart';
import '../eventBus/space_home_custom_eventbus.dart';
import '../home_page/model/space_home_currentskin_data.dart';
import '../home_page/model/space_home_resource_data.dart';
import '../home_page/model/space_mall_goods_data.dart';
import '../home_page/model/space_mall_resource_data.dart';
import 'state.dart';

const int refreshpageNum = 18;

class SpaceMyDressPageCtrl extends Cubit<SpaceMyDressPageState> {
  late final SpaceHomeApi _homeApiService;
  late final JoJoResourceManager? _resourceManager;
  late PayMethodType payMethodType; // 商品支付方式：默认学豆
  int currentPageIndex = 0; // 当前选中页页码
  bool isLandscape = false;
  bool isChangePayMethodType = false; // 是否正在切换支付方式（会有接口调用，需要请求完之后才呢点击）
  CustomCurrentUserDressUp? _currentSelectedUserDressUp; // 当前选中的组合皮肤信息;
  bool isSelectedUnReachGroupDress = false; // 是否选中了未达标不能选的组合皮肤

  final Map<String, List<CustomGoodsSkin>> _skinGroupGoodIdMap = {};
  List<CurrentUserDressUps?>? _dressUpsCaches = []; // 服务器获取到或者保存后到装扮

  SpaceMyDressPageCtrl({
    JoJoResourceManager? manager,
    SpaceHomeApi? homeApiService,
  }) : super(
          SpaceMyDressPageState(
            pageStatus: PageStatus.loading,
            customSpaceHomeCurrentskinData: CustomSpaceHomeCurrentskinData(
                spaceHomeCurrentskinData: null,
                userId: 0,
                userName: '',
                totalPrice: 0,
                customDressUps: []),
            customSpaceMallResourceData: CustomSpaceMallResourceData(
                backGroundBonesZipUrl: '',
                categoryButtons: [],
                memberBonesZipUrl: '',
                seletedCategoryId: 0,
                dressUpRarityBackImgConfig: null,
                bestContinuousLearnDays: 0),
            guideSkinData: NewMallGuideSkinData(),
            ossPath: '',
            balance: null,
            repaintBoundaryKey: GlobalKey(),
          ),
        ) {
    _homeApiService = homeApiService ?? spaceHomeApiService;
    _resourceManager = manager ?? JoJoResourceManager();
    payMethodType = PayMethodType.beans;

    onRefresh();
  }

  onRefresh() async {
    //请求接口
    try {
      // 获取我的资产
      LiabilitiesInfo? myBalance =
          await _homeApiService.getLiabilitiesInfo("", "STORE");

      // 请求舞台上的当前皮肤
      SpaceHomeCurrentskinData? spaceHomeCurrentskinData =
          SpaceHomeCurrentskinData();
      spaceHomeCurrentskinData = await _homeApiService.requestCurrentSkinInfo();
      _dressUpsCaches = spaceHomeCurrentskinData.dressUps;
      // 请求商城资源 分类数据
      SpaceMallResourceData? spaceMallResourceData =
          const SpaceMallResourceData();
      spaceMallResourceData = await _homeApiService.requestMallResourceInfo();

      // JSON字符串
      // String jsonString =
      //     '{"animationList":[{"type":"daily","animationgName":"idle"},{"type":"daily","animationgName":"pose1"},{"type":"activity","animationgName":"pose2"}]}';
      String jsonString = spaceMallResourceData.dressImgGenerateConfig ?? '';
      List<AnimationData> animationList = [];
      if (jsonString.isNotEmpty) {
        final Map<String, dynamic> jsonData = jsonDecode(jsonString);
        // 将List<dynamic>转换为List<Animation>
        animationList = (jsonData['animationList'] as List)
            .map((item) => AnimationData.fromJson(item))
            .toList();
      }

      SpaceHomeResourceData? spaceHomeResourceData;
      //首页资源(请求默认皮肤)
      spaceHomeResourceData =
          await _homeApiService.requestSpaceHomeResourceInfo();

      List<CategoryButton> categoryButtons =
          spaceMallResourceData.categoryButtons ?? [];
      SpaceMallGoodsData? spaceMallGoodsDataFirst;
      if (categoryButtons.isNotEmpty) {
        //默认请求第一个分类的皮肤数据
        CategoryButton categoryButton = categoryButtons[0];
        spaceMallGoodsDataFirst = const SpaceMallGoodsData();
        spaceMallGoodsDataFirst = await _homeApiService.requestMallShopInfo(
            categoryButton.categoryId ?? 0, refreshpageNum, 1);
      }
      // 判断用哪个资产
      payMethodType = PayMethodType.beans; // 默认使用成长豆
      if (spaceMallGoodsDataFirst != null) {
        payMethodType = spaceMallGoodsDataFirst.useAssetType ==
                BalanceAssetType.ASSET_TYPE_GEMSTONE
            ? PayMethodType.gemstone
            : PayMethodType.beans;
      }

      List<CustomGoodsSkin>? customGoodsSkin = [];
      //去除皮肤数据组装自定义皮肤数据
      for (var GoodsSkin in spaceMallGoodsDataFirst?.dressUps ?? []) {
        customGoodsSkin.add(CustomGoodsSkin(
          goodsSkin: GoodsSkin,
          status: GoodsSkin.status,
        ));
      }
      int? seletedSkinId = 0; //默认选中的皮肤id
      for (GoodsSkin goodsSkin in spaceMallGoodsDataFirst?.dressUps ?? []) {
        if (goodsSkin.status == 2) {
          seletedSkinId = goodsSkin.goodId;
          break;
        }
      }
      //初始化自定义分类数据，将对应分类的皮肤放在第一个分类的位置
      List<CustomCategoryButton>? customCategoryButtons = [];
      int seletedCategoryId = 0; //默认选中的分类id
      for (int i = 0; i < categoryButtons.length; i++) {
        CategoryButton categoryButton = categoryButtons[i];
        if (i == 0) {
          seletedCategoryId = categoryButton.categoryId ?? 0;
        }
        CustomCategoryButton customCategoryButton = CustomCategoryButton(
            categoryButton: categoryButton,
            customSpaceMallGoodsData: i == 0
                ? CustomSpaceMallGoodsData(
                    seletedSkinId: seletedSkinId,
                    page: spaceMallGoodsDataFirst?.pageNum,
                    size: spaceMallGoodsDataFirst?.pageSize,
                    skins: customGoodsSkin,
                    total: spaceMallGoodsDataFirst?.total,
                  )
                : null,
            pageNum: 1,
            resetRefreshToolStatus: false); //第一个分类默认给第一个分类的皮肤方便默认显示
        customCategoryButtons.add(customCategoryButton);
      }
      //最终组装数据
      CustomSpaceMallResourceData customSpaceMallResourceData =
          CustomSpaceMallResourceData(
              memberBonesZipUrl: spaceMallResourceData.memberBonesZipUrl,
              backGroundBonesZipUrl: spaceMallResourceData.backGroundZipUrl,
              categoryButtons: customCategoryButtons,
              seletedCategoryId: seletedCategoryId,
              animationList: animationList,
              dressUpRarityBackImgConfig: spaceMallResourceData.dressUpRarityBackImgConfig,
              bestContinuousLearnDays: spaceMallResourceData.bestContinuousLearnDays,
              purchaseSwitch: spaceMallResourceData.purchaseSwitch);

      //组装当前皮肤的自定义数据
      List<CurrentUserDressUps>? dressUps =
          spaceHomeCurrentskinData.dressUps ?? [];
      List<CustomCurrentUserDressUp>? customDressUps = [];
      for (CurrentUserDressUps dressUp in dressUps) {
        customDressUps.add(CustomCurrentUserDressUp(
            currenTdressUp: dressUp, price: '', status: 2));
      }

      CustomSpaceHomeCurrentskinData customSpaceHomeCurrentskinData =
          CustomSpaceHomeCurrentskinData(
              spaceHomeCurrentskinData: spaceHomeCurrentskinData,
              userId: spaceHomeCurrentskinData.userId,
              userName: spaceHomeCurrentskinData.userName,
              totalPrice: 0,
              customDressUps: customDressUps);

      state.pageStatus = PageStatus.success;
      state.balance = myBalance;
      state.customSpaceMallResourceData = customSpaceMallResourceData;
      state.customSpaceHomeCurrentskinData = customSpaceHomeCurrentskinData;
      state.spaceHomeResourceData = spaceHomeResourceData;
      state.hasDefalutSkin = dressUps.isNotEmpty;

      //资源下载
      await downLoadResource();
    } catch (e) {
      JoJoLoading.dismiss();
      //请求失败
      var exception = Exception("请求接口失败,$e");
      if (e is Exception) {
        exception = e;
      }
      final newState = state.copyWith()
        ..pageStatus = PageStatus.error
        ..exception = exception;
      l.e(logTag, "请求接口失败,$e");
      print(
          '💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣💣-报错了$e');
      emit(newState);
    }
  }

//资源下载
  downLoadResource() {
    List<String> urls = [];
    //舞台背景，骨骼
    urls.addListsIfNotEmpty([
      state.customSpaceMallResourceData?.memberBonesZipUrl,
      state.customSpaceMallResourceData?.backGroundBonesZipUrl,
    ]);
    //默认皮肤
    state.spaceHomeResourceData?.ownAndDefaultSkinList?.forEach((element) {
      urls.addIfNotEmpty(element.resourcesZipUrl);
    });
    //当前身上穿的皮肤
    state.customSpaceHomeCurrentskinData?.customDressUps?.forEach((element) {
      urls.addIfNotEmpty(element.currenTdressUp?.resourcesZipUrl);
    });
    _resourceManager?.downloadUrl(urls, progressListener: (progress) {
      //下载进度
      print("下载进度:$progress");
    }, successListener: (map) async {
      // 处理下载之后的资源
      await handleGuideSkinData(map);
      await handlePersonSpineData(map);
      await handleBackgroundData(map);
      JoJoLoading.dismiss();
      emit(state.copyWith());
    }, failListener: (e) {
      JoJoLoading.dismiss();
      final exception = NativeDownloadError.convert(e);
      //下载失败
      final newState = state.copyWith()
                            ..pageStatus = PageStatus.error
                            ..exception = exception;
      emit(newState);
      l.e(logTag, "资源下载失败 $e");
    });
  }

  Future<LiabilitiesInfo> requestMyBalanceData() async {
    return _homeApiService.getLiabilitiesInfo("", "STORE");
  }
 
  // 清理用户操作数据
  clearUserAction() {
    _currentSelectedUserDressUp = null;
  }

  // 重新请求皮肤，替换到舞台上
  Future<void> requestCurrentskinData() async {
    state.customSpaceHomeCurrentskinData?.spaceHomeCurrentskinData =
        await _homeApiService.requestCurrentSkinInfo();
    _dressUpsCaches = state.customSpaceHomeCurrentskinData?.spaceHomeCurrentskinData?.dressUps;

    state.customSpaceHomeCurrentskinData?.totalPrice = 0;
    List<CurrentUserDressUps>? dressUps = state.customSpaceHomeCurrentskinData
            ?.spaceHomeCurrentskinData?.dressUps ??
        [];
    List<CustomCurrentUserDressUp>? customDressUps = [];
    for (CurrentUserDressUps dressUp in dressUps) {
      customDressUps.add(CustomCurrentUserDressUp(
          currenTdressUp: dressUp, price: '', status: 2));
    }
    // 更新我选中的套装
    state.customSpaceHomeCurrentskinData?.customDressUps = customDressUps;
    _updateSeletedSkinWithDressUps(customDressUps);

    for (CustomCategoryButton element
        in state.customSpaceMallResourceData?.categoryButtons ?? []) {
      element.pageNum = 1;
      element.resetRefreshToolStatus = true;
    }

    // 更新舞台上显示的内容
    updateUserCurrentSkinList(customDressUps);
  }

//请求当前分类下的商品列表
  Future<bool> requestMallShopInfoData(
      bool isLoadMore,
      bool needRequest,

      /// 是否需要强制发起请求（即使存在缓存数据）
      RefreshController? refreshController,
      int categoryId,
      int pageSize,
      int pageNum) async {
    try {
      CustomSpaceMallResourceData? customSpaceMallResourceData =
          state.customSpaceMallResourceData;
      CustomCategoryButton currentCategoryButton = CustomCategoryButton(
          categoryButton: null,
          customSpaceMallGoodsData: null,
          pageNum: 1,
          resetRefreshToolStatus: false);
      for (CustomCategoryButton customCategoryButton
          in customSpaceMallResourceData?.categoryButtons ?? []) {
        if (customCategoryButton.categoryButton?.categoryId == categoryId) {
          if (customCategoryButton.customSpaceMallGoodsData != null &&
              isLoadMore == false &&
              needRequest == false) {
            //如果商品数据不为空，且当前请求的页数为第一页，则视为已经请求过数据，只是切换tab操作，不请求接口，区别上拉刷新，页数是大于1的
            return false;
          }
          currentCategoryButton = customCategoryButton;
        }
      }
      showLoadinDialog();
      if (isLoadMore == true) {
        pageNum++;
      }
      SpaceMallGoodsData? spaceMallGoodsData = await _homeApiService
          .requestMallShopInfo(categoryId, pageSize, pageNum);
      if (isLoadMore) {
        refreshController?.loadComplete();
      }
      List<GoodsSkin>? dressUps = spaceMallGoodsData.dressUps ?? [];
      if (dressUps.isNotEmpty && isLoadMore) {
        currentCategoryButton.pageNum++;
      }
      if (dressUps.isEmpty) {
        refreshController?.loadNoData();
      }
      List<CustomGoodsSkin>? customGoodsSkin = [];
      //去除皮肤数据组装自定义皮肤数据
      for (var GoodsSkin in spaceMallGoodsData.dressUps ?? []) {
        final goods = CustomGoodsSkin(
          goodsSkin: GoodsSkin,
          status: GoodsSkin.status,
        );
        customGoodsSkin.add(goods);
        _cacheGroupSkins(goods);
      }

      for (CustomCategoryButton customCategoryButton
          in customSpaceMallResourceData?.categoryButtons ?? []) {
        if (customCategoryButton.categoryButton?.categoryId == categoryId) {
          CustomSpaceMallGoodsData? currentCustomSpaceMallGoodsData =
              customCategoryButton.customSpaceMallGoodsData;
          List<CustomGoodsSkin> skins =
              currentCustomSpaceMallGoodsData?.skins ?? [];
          if (!needRequest) {
            skins.addAll(customGoodsSkin);
          } else {
            skins = customGoodsSkin;
          }

          int? seletedSkinId =
              customCategoryButton.customSpaceMallGoodsData?.seletedSkinId;
          for (CustomGoodsSkin goodsSkin in skins) {
            if (isLoadMore) {
              break;
            } else {
              // 已经选中了组合商品，再拉取单品时，需要将选中设置为组合商品中该分类下children单品
              if (!isLoadMore &&
                  !needRequest &&
                  !goodsSkin.isGroup &&
                  _currentSelectedUserDressUp != null) {
                final dressup = _currentSelectedUserDressUp
                    ?.currenTdressUp?.children
                    ?.firstWhereOrNull(
                        (element) => element?.dressUpCategoryId == categoryId);

                seletedSkinId = dressup?.dressUpId ?? seletedSkinId;
                break;
              }

              if (goodsSkin.goodsSkin?.status == 2) {
                seletedSkinId = goodsSkin.goodsSkin?.goodId;
                break;
              }
            }
          }
          customCategoryButton.customSpaceMallGoodsData =
              CustomSpaceMallGoodsData(
            seletedSkinId: seletedSkinId,
            page: spaceMallGoodsData.pageNum,
            size: spaceMallGoodsData.pageSize,
            skins: skins,
            total: spaceMallGoodsData.total,
          );
        }
      }
      state.customSpaceMallResourceData = customSpaceMallResourceData;
      JoJoLoading.dismiss();
      return true;
    } catch (e) {
      JoJoLoading.dismiss();
      if (isLoadMore) {
        refreshController?.loadComplete();
      }
      return true;
    }
  }

// 检查组合的全部商品是不是全部在单品中被选中
  void _checkGroupDressUpStatus() {
    List<CustomCurrentUserDressUp>? customDressUps = state
        .customSpaceHomeCurrentskinData?.customDressUps ?? [];
    bool currentIsGroup = false;
    for (CustomCurrentUserDressUp dressUp in customDressUps) {
      if (dressUp.currenTdressUp?.isGroup ?? false) {
        currentIsGroup = true;
        break;
      }
    }
    if (!currentIsGroup) {
            final childrenKeys = customDressUps
            .map((child) => child.currenTdressUp?.dressUpId.toString())
            .where((id) => id != null && id.isNotEmpty)
            .join("_");
    
        if (_skinGroupGoodIdMap.containsKey(childrenKeys)) {
          final groups = _skinGroupGoodIdMap[childrenKeys] ?? [];
          for (CustomGoodsSkin goodsSkin in groups) {
            goodsSkin.status = 2;
          }
        }
    }
  }

  void _cacheGroupSkins(CustomGoodsSkin goods) {
    if (goods.isGroup) {
      final childrenKeys = goods.goodsSkin?.children
              ?.map((child) => child?.goodId.toString())
              .where((id) => id != null && id.isNotEmpty)
              .join("_") ??
          "";
      if (childrenKeys.isNotEmpty && (goods.goodsSkin?.goodId != null)) {
        if (_skinGroupGoodIdMap.containsKey(childrenKeys)) {
          List<CustomGoodsSkin> existingGoodsList =
              _skinGroupGoodIdMap[childrenKeys] ?? [];
          if (!existingGoodsList
              .any((g) => g.goodsSkin?.goodId == goods.goodsSkin?.goodId)) {
            existingGoodsList.add(goods);
          }
          _skinGroupGoodIdMap[childrenKeys] = existingGoodsList;
        } else {
          _skinGroupGoodIdMap[childrenKeys] = [goods];
        }
      }
    }
  }

  int _calcTotalPrice(List<CustomCurrentUserDressUp>? dressUps) {
    int totalPrice = 0;
    if (dressUps != null) {
      for (CustomCurrentUserDressUp dressUp in dressUps) {
        String? priceStr = dressUp.price ?? '';
        if (priceStr.isNotEmpty && dressUp.status == 0) {
          try {
            int price = int.parse(priceStr);
            totalPrice += price;
          } catch (e) {
            l.e(logTag, "解析价格失败: $priceStr, 错误信息: $e");
          }
        }
      }
    }
    return totalPrice;
  }

  void _replaceDressUpInCategory(List<CustomCurrentUserDressUp>? dressUps,
      CustomCurrentUserDressUp currentUserDressUp) {
    if (dressUps == null) return;
    int replaceDressOfCategoryId =
        currentUserDressUp.currenTdressUp?.dressUpCategoryId ?? 0;
        bool didFind = false;
    for (int i = 0; i < dressUps.length; i++) {
      if (replaceDressOfCategoryId ==
          dressUps[i].currenTdressUp?.dressUpCategoryId) {
            didFind = true;
        dressUps[i].currenTdressUp = currentUserDressUp.currenTdressUp;
        dressUps[i].status = currentUserDressUp.status;
        dressUps[i].price = currentUserDressUp.price;
        break;
      }
    }

    if (!didFind) {
      dressUps.add(currentUserDressUp);
    }
  }

  void _updateSeletedSkinWithDressUps(List<CustomCurrentUserDressUp>? dressUps,
      {bool isClear = false}) {
    if (state.customSpaceMallResourceData?.categoryButtons == null) return;

    state.customSpaceMallResourceData?.categoryButtons
        ?.forEach((categoryButton) {
      final categoryId = categoryButton.categoryButton?.categoryId ?? 0;
      final reslut = dressUps?.firstWhereOrNull(
        (element) => element.currenTdressUp?.dressUpCategoryId == categoryId,
      );

      categoryButton.customSpaceMallGoodsData?.seletedSkinId =
          isClear ? 0 : reslut?.currenTdressUp?.dressUpId;
    });
  }

  List<CustomCurrentUserDressUp>? _getChildrenCurstionCurrentUserDressUps(
      CustomCurrentUserDressUp currentUserDressUp) {
    final childDressUps = currentUserDressUp.currenTdressUp?.children
        ?.map((child) => CustomCurrentUserDressUp(
              currenTdressUp: child,
              status: child?.status,
              price: child?.price,
            ))
        .toList();
    return childDressUps;
  }

  // 单品替换限定组合
  updateSinleReplaceGroupCurrentSkinInfo(
      CustomCurrentUserDressUp currentUserDressUp) {
    CustomSpaceHomeCurrentskinData? customSpaceHomeCurrentskinData =
        state.customSpaceHomeCurrentskinData;
    List<CustomCurrentUserDressUp>? dressUps =
        customSpaceHomeCurrentskinData?.customDressUps;

    dressUps?.clear();

    // user-dress-ups返回的装扮都是单品，不会返回限定
    final oldDressup = _dressUpsCaches;
    oldDressup?.forEach((dressup) {
      dressUps?.add(CustomCurrentUserDressUp(
        currenTdressUp: dressup,
        status: 2,
        price: '',
      ));
    });

    _replaceDressUpInCategory(dressUps, currentUserDressUp);
    _updateSeletedSkinWithDressUps(dressUps);
    customSpaceHomeCurrentskinData?.totalPrice = _calcTotalPrice(dressUps);
    updateUserCurrentSkinList(dressUps);
  }

  // 限定组合替换单品
  updateGroupRelaceSingleCurrentSkinInfo(
      CustomCurrentUserDressUp currentUserDressUp) {
    updateGroupReplaceGroupCurrentSkinInfo(currentUserDressUp);
  }

  // 当前无皮肤添加限定组合
  updateGroupCurrentSkinInfo(CustomCurrentUserDressUp currentUserDressUp) {
    updateGroupReplaceGroupCurrentSkinInfo(currentUserDressUp);
  }

  // 限定组合替换限定组合
  updateGroupReplaceGroupCurrentSkinInfo(
      CustomCurrentUserDressUp currentUserDressUp) {
    CustomSpaceHomeCurrentskinData? customSpaceHomeCurrentskinData =
        state.customSpaceHomeCurrentskinData;
    List<CustomCurrentUserDressUp>? dressUps =
        customSpaceHomeCurrentskinData?.customDressUps;
    dressUps?.clear();
    dressUps?.add(currentUserDressUp);

    // 计算所选皮肤的总价格
    int totalPrice = _calcTotalPrice(dressUps);
    customSpaceHomeCurrentskinData?.totalPrice = totalPrice;

    List<CustomCurrentUserDressUp>? childDressUps =
        _getChildrenCurstionCurrentUserDressUps(currentUserDressUp);
    _updateSeletedSkinWithDressUps(childDressUps);
    updateUserCurrentSkinList(childDressUps);

    _currentSelectedUserDressUp = currentUserDressUp;
  }

  // 单品替换单品
  updateSignleSkinInfo(CustomCurrentUserDressUp currentUserDressUp) {
    CustomSpaceHomeCurrentskinData? customSpaceHomeCurrentskinData =
        state.customSpaceHomeCurrentskinData;
    List<CustomCurrentUserDressUp>? dressUps =
        customSpaceHomeCurrentskinData?.customDressUps;

    if (dressUps != null) {
      bool currentCaterogyHasSkin =
          false; //判断要替换的分类在用户身上是有已经有了，如果有的话，进行替换，否则，添加到上
      int replaceDressOfCategoryId =
          currentUserDressUp.currenTdressUp?.dressUpCategoryId ?? 0;
      for (CustomCurrentUserDressUp dressUp in dressUps) {
        if (replaceDressOfCategoryId ==
            dressUp.currenTdressUp?.dressUpCategoryId) {
          currentCaterogyHasSkin = true;
        }
      }
      if (currentCaterogyHasSkin) {
        //如果要添加的皮肤对应分类下有皮肤，进行替换操作
        _replaceDressUpInCategory(dressUps, currentUserDressUp);
      } else {
        //如果要添加的皮肤对应分类下没有皮肤，进行添加操作
        print(
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻当前分类下还没有皮肤，需要添加该皮肤 ${currentUserDressUp.currenTdressUp?.dressUpId}');
        dressUps.add(currentUserDressUp);
      }
      // 计算所选皮肤的总价格
      int totalPrice = _calcTotalPrice(dressUps);
      customSpaceHomeCurrentskinData?.totalPrice = totalPrice;
    }
    updateUserCurrentSkinList(dressUps);
  }

  //更新用户当前皮肤信息
  updateUserCurrentSkinInfo(CustomCurrentUserDressUp currentUserDressUp) {
    showLoadinDialog();

    _currentSelectedUserDressUp = null;

    final customCategoryButton =
        state.customSpaceMallResourceData?.categoryButtons?[currentPageIndex];
    isSelectedUnReachGroupDress =
        !(customCategoryButton?.categoryButton?.reachBuyThreshold ?? false) &&
            customCategoryButton?.shouldShowReachTips == true;

    List<CustomCurrentUserDressUp>? dressUps =
        state.customSpaceHomeCurrentskinData?.customDressUps; // 获取到当前人物穿上的选中商品
    if (dressUps != null) {
      for (CustomCurrentUserDressUp dressUp in dressUps) {
        if (dressUp.currenTdressUp?.dressUpId ==
            currentUserDressUp.currenTdressUp?.dressUpId) {
          print(
              '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 当前替换皮肤用户身上已穿戴，不做操作');
          JoJoLoading.dismiss();
          return; //身上有的服装部做操作
        }
      }

      bool newIsGroup = currentUserDressUp.currenTdressUp?.isGroup ?? false;
      bool oldIsGroup = false;
      for (CustomCurrentUserDressUp dressUp in dressUps) {
        if (dressUp.currenTdressUp?.isGroup ?? false) {
          oldIsGroup = true;
          break;
        }
      }
      if (newIsGroup && oldIsGroup) {
        updateGroupReplaceGroupCurrentSkinInfo(currentUserDressUp);
      } else if (newIsGroup && !oldIsGroup) {
        updateGroupRelaceSingleCurrentSkinInfo(currentUserDressUp);
      } else if (!newIsGroup && oldIsGroup) {
        updateSinleReplaceGroupCurrentSkinInfo(currentUserDressUp);
      } else {
        updateSignleSkinInfo(currentUserDressUp);
      }
    } else {
      if (currentUserDressUp.currenTdressUp?.isGroup == true) {
        updateGroupCurrentSkinInfo(currentUserDressUp);
      } else {
        updateSignleSkinInfo(currentUserDressUp);
      }
    }
  }

  //更新用户当前皮肤信息
  updateUserCurrentSkinList(
      List<CustomCurrentUserDressUp>? customCurrentUserDressUps) {
    List<String> urls = [];
    //当前身上穿的皮肤
    customCurrentUserDressUps?.forEach((element) {
      urls.addIfNotEmpty(element.currenTdressUp?.resourcesZipUrl);
    });
    _resourceManager?.downloadUrl(urls, progressListener: (progress) {
      //下载进度
      print("下载进度:$progress");
    }, successListener: (map) async {
      List<SkinModel> skinList = [];
      //当前身上皮肤
      customCurrentUserDressUps?.forEach((element) {
        var shinZip = map[element.currenTdressUp?.resourcesZipUrl];
        skinList.add(SkinModel(
            id: element.currenTdressUp?.dressUpId, zipLocalFile: shinZip));
      });
      state.stagePersonData?.spineData?.skinList = skinList;
      final newState = state.copyWith();
      emit(newState);
    }, failListener: (e) {
      //下载失败
      JoJoLoading.dismiss();
       final exception = NativeDownloadError.convert(e);
      //下载失败
      final newState = state.copyWith()
                            ..pageStatus = PageStatus.error
                            ..exception = exception;
      emit(newState);
    });
  }

  //上拉刷新
  Future<bool> pullUpRefresh(RefreshController refreshController,
      int categoryId, int pageSize, int pageNum) async {
    try {
      await requestMallShopInfoData(
          true, false, refreshController, categoryId, pageSize, pageNum);
      return true;
    } catch (e) {
      return true;
    }
  }

  //购买皮肤
  Future<bool> actionBuySkin(int categoryId, int goodId, int skinId, BuildContext context) async {
    int totalPrice = state.customSpaceHomeCurrentskinData?.totalPrice ?? 0;
    Completer<bool> completer = Completer<bool>();
    if (totalPrice > 0) {
      JoJoDesignDialog.dismiss();
      // 开始购买流程
      await processBuySkin(categoryId, goodId, skinId);
      completer.complete(true);
    } else {
      print(
          '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 所有皮肤总价不超过0，不谈弹窗，直接走流程');
      // 开始购买流程
      await processBuySkin(categoryId, goodId, skinId);
      completer.complete(true);
    }

    return completer.future;
  }

  Future<bool> processBuySkin(int categoryId, int goodId, int skinId) async {
    print(
        '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 开始购买保存流程');

    //获取当前所有皮肤，确认是否含有需要购买的皮肤，如果有，则请求接口购买，如果没有，走保存逻辑
    CustomSpaceHomeCurrentskinData? customSpaceHomeCurrentskinData =
        state.customSpaceHomeCurrentskinData;
    List<CustomCurrentUserDressUp>? allDressUps =
        customSpaceHomeCurrentskinData?.customDressUps;
    //判断是否有需要购买的皮肤
    bool hasBuySkin = false;
    //需要购买的皮肤id列表
    List<int>? needBuyDressUpId = [];
    //需要保存的皮肤id列表
    List<int>? needSaveDressUpId = [];
    // 组合ID
    List<CustomCurrentUserDressUp>? needSaveGroupDreesUps  = [];

    for (CustomCurrentUserDressUp dressUp in allDressUps ?? []) {
      if (dressUp.status == 0) {
        //存在未购买的皮肤
        hasBuySkin = true;
        //取出需要购买的皮肤id列表
        needBuyDressUpId.add(dressUp.currenTdressUp?.dressUpId ?? 0);
      }

      //取出需要保存的皮肤id列表
      if (dressUp.currenTdressUp?.isGroup ?? false) {
        needSaveGroupDreesUps.add(dressUp);

        final childrenIds = dressUp.currenTdressUp?.children
            ?.map((e) => e?.dressUpId ?? 0)
            .where((id) => id != 0)
            .toList();
        if (childrenIds != null && childrenIds.isNotEmpty) {
          needSaveDressUpId.addAll(childrenIds);
        }
      } else {
        needSaveDressUpId.add(dressUp.currenTdressUp?.dressUpId ?? 0);
      }
    }

    showLoadinDialog();

    //如果有需要购买的皮肤
    if (hasBuySkin) {
      print(
          '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 有需要购买的皮肤，开始走购买保存流程');
      try {
        //请求购买接口
        print(
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 开始请求购买接口');
        await _homeApiService.requestBuySkinInfo(needBuyDressUpId);
        RunEnv.sensorsTrack(
            '\$AppClick', {'\$element_name': '兑换按钮点击', 'custom_state': 'xdgm'});
        RunEnv.sensorsTrack(
            '\$AppClick', {'\$element_name': '兑换结果', 'custom_state': 'gmcg'});
        print(
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 购买接口请求成功，开始轮询请求保存接口');
        //后买接口请求成功后轮询请求保存接口
        int maxAttempts = 4; // 最大尝试次数
        int attempt = 0; // 当前尝试次数
        while (attempt < maxAttempts) {
          try {
            print(
                '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 开始第$attempt次轮询请求保存接口');
            //开始人物形象快照，并上传图片获取oss地址
            await _snapshot(needSaveDressUpId, 1);
            JoJoToast.showSuccess('保存成功');
            state.customSpaceHomeCurrentskinData?.totalPrice = 0;
            RunEnv.sensorsTrack('\$AppClick',
                {'\$element_name': '兑换结果', 'custom_state': 'bccg'});
            print(
                '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 第$attempt次轮询请求保存接口请求成功');
            //需要传入本次保存的皮肤，和购买皮肤，处理请求结束后本地数据的状态，如从未购买变为使用中，从使用中变为已拥有
            print(
                '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 保存成功，开始更新本地数据');

            refreshLoaclData(needBuyDressUpId, needSaveDressUpId);
            if (needSaveGroupDreesUps.isNotEmpty) {
              refreshGroupLocalData(needSaveGroupDreesUps);
            }
            break; // 如果请求成功，跳出循环
          } catch (e) {
            print(
                '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 第$attempt次轮询请求保存接口失败，原因：$e');
            attempt++;
            if (attempt < maxAttempts) {
              await Future.delayed(const Duration(seconds: 3));
            } else {
              // 如果达到最大尝试次数，可以记录错误或抛出异常
              deleteSnapshotWhenError();
              print(
                  '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 请求失败，已达到最大尝试次数 $e');
              var dioError = e as DioException?;
              handleError(dioError, '网络异常，请稍后再试');
            }
          }
        }
      } catch (e) {
        print(
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 购买接口请求失败 $e');
        deleteSnapshotWhenError();
        RunEnv.sensorsTrack(
            '\$AppClick', {'\$element_name': '兑换结果', 'custom_state': 'gmsb'});
        var dioError = e as DioException?;
        handleError(dioError, '网络异常，请稍后再试');
      }
      //有购买皮肤，先购买后保存，再刷新学豆信息
      await refreshXuedouCount();
    } else {
      //如果没有购买的皮肤直接保存
      print(
          '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 没有有需要购买的皮肤，直接开始保存流程');
      try {
        print(
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 开始请求保存接口');

        //开始人物形象快照，并上传图片获取oss地址
        await _snapshot(needSaveDressUpId, 1);
        print(
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 保存接口请求成功');
        //需要传入本次保存的皮肤，和购买皮肤，处理请求结束后本地数据的状态，如从未购买变为使用中，从使用中变为已拥有
        //todo：部分购买成功的情况？？？从接口获取失败的id，对比上传的数组做剔除操作
        print(
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 保存成功，开始更新本地数据');

        refreshLoaclData(needBuyDressUpId, needSaveDressUpId);
        if (needSaveGroupDreesUps.isNotEmpty) {
          refreshGroupLocalData(needSaveGroupDreesUps);
        }
        JoJoToast.showSuccess('保存成功');
        RunEnv.sensorsTrack(
            '\$AppClick', {'\$element_name': '兑换按钮点击', 'custom_state': 'zjbc'});
        RunEnv.sensorsTrack(
            '\$AppClick', {'\$element_name': '兑换结果', 'custom_state': 'bccg'});
      } catch (e) {
        print(
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 保存接口请求失败 $e');
        var dioError = e as DioException?;
        handleError(dioError, '网络异常，请稍后再试');
        deleteSnapshotWhenError();
      }
    }
    JoJoLoading.dismiss();

    return true;
  }

//处理错误信息
  void handleError(DioException? dioError, String defaultErrorMessage) {
    String errorMessage;
    if (dioError?.response != null && dioError?.response?.data is Map) {
      Map<String, dynamic> responseData = dioError?.response!.data;
      errorMessage = responseData['message'] ?? defaultErrorMessage;
    } else {
      errorMessage = defaultErrorMessage;
    }
    JoJoToast.showError(errorMessage);
  }

refreshGroupLocalData(
    List<CustomCurrentUserDressUp> groupDreesUps,
  ) {
    final childrenIds = [];
    final groupIds = [];
    for (final element in groupDreesUps) {
      groupIds.add(element.currenTdressUp?.dressUpId ?? 0);
      final children = element.currenTdressUp?.children;
      if (children != null) {
        for (final e in children) {
          childrenIds.add(e?.dressUpId ?? 0);
        }
      }
    }
    for (CustomCategoryButton element
        in state.customSpaceMallResourceData?.categoryButtons ?? []) {
      CustomSpaceMallGoodsData? customSpaceMallGoodsData =
          element.customSpaceMallGoodsData;
      for (CustomGoodsSkin skin in customSpaceMallGoodsData?.skins ?? []) {
        if (skin.status == 2) {
          //之前使用中的皮肤改为已拥有
          skin.status = 1;
        }
        if (groupIds.contains(skin.goodsSkin?.goodId ?? 0)) {
          skin.status = 2; //使用中
        }
        if (childrenIds.contains(skin.goodsSkin?.goodId ?? 0)) {
          skin.status = 2; //使用中
        }
      }
    }
  }

  refreshLoaclData(
    List<int> needBuyDressUpId,
    List<int> needSaveDressUpId,
  ) async {
    // 0：未购买，1：已购买(拥有)，2：已使用）
    CustomSpaceMallResourceData? customSpaceMallResourceData =
        state.customSpaceMallResourceData;
    for (CustomCategoryButton element
        in customSpaceMallResourceData?.categoryButtons ?? []) {
      CustomSpaceMallGoodsData? customSpaceMallGoodsData =
          element.customSpaceMallGoodsData;
      for (CustomGoodsSkin skin in customSpaceMallGoodsData?.skins ?? []) {
        if (skin.status == 2) {
          //之前使用中的皮肤改为已拥有
          skin.status = 1;
        }
        if (needSaveDressUpId.contains(skin.goodsSkin?.goodId ?? 0)) {
          skin.status = 2; //使用中
        }
      }
    }

    //更新身上的皮肤状态，如从从未购买变为使用中
    CustomSpaceHomeCurrentskinData? customSpaceHomeCurrentskinData =
        state.customSpaceHomeCurrentskinData;
    List<CustomCurrentUserDressUp>? allDressUps =
        customSpaceHomeCurrentskinData?.customDressUps;
    //通知到主页更新舞台小人
    List<String> currentUserDressUpsPath = [];
    for (CustomCurrentUserDressUp dressUp in allDressUps ?? []) {
      dressUp.status = 2;
      dressUp.price = '';
      String? localPath = await _resourceManager
          ?.getFileLocalPath(dressUp.currenTdressUp?.resourcesZipUrl ?? '');
      currentUserDressUpsPath.add(localPath ?? '');
    }
    // 发送事件，改成存储数据
    jojoNativeBridge.operationNativeValueSet(
        key: skinValueKey,
        value: CustomEventSyncUserDress(currentUserDressUpsPath).toJson());

    List<CurrentUserDressUps?> dressUpsCaches = [];
    for (CustomCurrentUserDressUp dressUp in allDressUps ?? []) {
      if (dressUp.status != 2) {
        break;
      }

      if (dressUp.currenTdressUp?.isGroup == true) {
        dressUpsCaches.addAll(dressUp.currenTdressUp?.children ?? []);
      } else {
        dressUpsCaches.add(dressUp.currenTdressUp);
      }
    }

    if (dressUpsCaches.isNotEmpty) {
      _dressUpsCaches = dressUpsCaches;
    }
    
  }

//更新学豆信息
  Future<bool> refreshXuedouCount() async {
    print(
        '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 开始请求学豆刷新接口');
    try {
      SpaceHomeCurrentskinData? spaceHomeCurrentskinData =
          SpaceHomeCurrentskinData();
      spaceHomeCurrentskinData = await _homeApiService.requestCurrentSkinInfo();
      emit(state.copyWith());
      print(
          '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 求学豆刷新接口成功');
      return true;
    } catch (e) {
      print(
          '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻 请求学豆刷新接口失败 $e');
      return true;
    }
  }

  ///处理默认皮肤数据
  handleGuideSkinData(Map map) {
    if (state.spaceHomeResourceData?.ownAndDefaultSkinList == null ||
        state.spaceHomeResourceData?.ownAndDefaultSkinList?.isEmpty == true) {
      //没有默认皮肤数据,显示加载失败
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      return;
    }
    state.guideSkinData?.defaultSkinList = [];
    state.spaceHomeResourceData?.ownAndDefaultSkinList?.forEach((element) {
      state.guideSkinData?.defaultSkinList?.add(DefaultSkinList(
        skinId: element.skinId,
        categoryShowCode: element.categoryShowCode,
        categoryShowName: element.categoryShowName,
        resourcesZipUrl: map[element.resourcesZipUrl],
        dressShowUrl: element.dressShowUrl,
      ));
    });

    print('object');
  }

  ///处理背景数据
  handleBackgroundData(Map map) async {
    var bgZipUrl =
        map[state.customSpaceMallResourceData?.backGroundBonesZipUrl];
    if (bgZipUrl != null) {
      var unzipFile = await unzip(bgZipUrl);
      var stageBg = findFilesByName(unzipFile, "stagebg.png");
      if (stageBg != null) {
        state.stagePersonData?.bgImageFile = stageBg.path;
      } else {
        final newState = state.copyWith()..pageStatus = PageStatus.error;
        emit(newState);
        l.e(logTag, "handleBackgroundData 找不到背景文件 $bgZipUrl");
      }
    } else {
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      l.e(logTag, "handleBackgroundData 找不到zip $bgZipUrl");
    }
  }

  Future<void> _snapshot(List<int> skins, int type) async {
    //开始人物形象快照，并上传图片获取oss地址
    Completer<List<Map<String, dynamic>>>? completer = Completer();
    if (type == 0) {
      jojoEventBus.fire(MallDefaultSnapshootnEvent(
        state.customSpaceMallResourceData?.animationList,
        completer: completer,
      ));
    } else if (type == 1) {
      jojoEventBus.fire(MallSnapshootnEvent(
        state.customSpaceMallResourceData?.animationList,
        completer: completer,
      ));
    }

    List<Map<String, dynamic>> resultMap = await completer.future.timeout(
      const Duration(seconds: 6),
      onTimeout: () {
        print("等待超时");
        return []; // 返回一个空的结果
      },
    );
    // 找出本地默认的截图
    String? ossPath = resultMap.firstWhere(
      (map) => map['type'] == 'daily',
      orElse: () => {},
    )['dressImg'] as String?;
    await _homeApiService.saveSkinInfo(skins, ossPath ?? '', resultMap);
  }

  /// 保存换肤
  void saveDefalutSkin(List<int> skins, List<String> skinLocalFiles,
      GlobalKey? repaintBoundaryKey) async {
    try {
      JoJoLoading.show();
      await _snapshot(skins, 0);
      if (state.stagePersonData?.spineData?.skelFile != null) {
        //如果模型已经赋值过了,则把新的皮肤换上
        // state.isNeedChangeSkin = true;
        var newSkinList =
            skinLocalFiles.map((e) => SkinModel(zipLocalFile: e)).toList();
        state.stagePersonData = state.stagePersonData?.copyWith()
          ?..spineData = StageSpineData(
              skelFile: state.stagePersonData?.spineData?.skelFile ?? "",
              atlasFile: state.stagePersonData?.spineData?.atlasFile ?? "",
              skinList: newSkinList);
      }
      await onRefresh();
      JoJoLoading.dismiss();
    } catch (e) {
      deleteSnapshotWhenError();
      JoJoLoading.dismiss();
      l.e(logTag, '保存皮肤错误：$e');
    }
  }

  ///处理人物模型数据
  handlePersonSpineData(Map map) async {
    var memberBonesZipUrl =
        map[state.customSpaceMallResourceData?.memberBonesZipUrl];
    if (memberBonesZipUrl != null) {
      var unzipFile = await unzip(memberBonesZipUrl);
      var skinZipFile = findFilesByName(unzipFile, "defaultSkin.zip");
      var atlasFile = findFilesByExtensionByDic(unzipFile, "atlas");
      var skelFile = findFilesByExtensionByDic(unzipFile, "skel");
      skelFile ??= findFilesByExtensionByDic(unzipFile, "json");
      if (atlasFile != null && skelFile != null) {
        //找到atlas文件和skel文件
        List<SkinModel> skinList = [];
        //默认皮肤
        if (skinZipFile != null) {
          skinList.add(SkinModel(id: 0, zipLocalFile: skinZipFile.path));
        }
        //当前身上皮肤
        state.customSpaceHomeCurrentskinData?.customDressUps
            ?.forEach((element) {
          var shinZip = map[element.currenTdressUp?.resourcesZipUrl];
          skinList.add(SkinModel(
              id: element.currenTdressUp?.dressUpId, zipLocalFile: shinZip));
        });
        //默认皮肤
        state.guideSkinData?.spineData = StageSpineData(
          skelFile: skelFile.path,
          atlasFile: atlasFile.path,
        );

        final stageData = ShopStagePersonData(
            spineData: StageSpineData(
                skelFile: skelFile.path,
                atlasFile: atlasFile.path,
                skinList: skinList),
            animationList: state.customSpaceMallResourceData?.animationList,
            defaultSkinZip: skinZipFile?.path);
        state.stagePersonData = stageData;
      } else {
        final newState = state.copyWith()..pageStatus = PageStatus.error;
        emit(newState);
        l.e(logTag, "handlePersonSpineData 找不到文件");
      }
    } else {
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
      l.e(logTag, "handlePersonSpineData 找不到压缩包");
    }
  }

  Future<bool> reportBalanceSwitch(String type) async {
    try {
      Map<String, dynamic> map = {
        "bizIdList": ["assetType"],
        "type": "71",
        "classId": "0",
        "extendData": jsonEncode({"status": type})
      };
      await spaceHomeApiService.balanceSwitchCallBack(map);
      return true;
    } catch (e) {
      l.e(logTag, "reportBalanceSwitch $e");
      return false;
    }
  }

  showLoadinDialog() {
    JoJoLoading.show(
      msg: "请稍候",
      textStyle: TextStyle(
        fontSize: 16.rdp,
        color: HexColor('#ACB2BB'),
        fontWeight: FontWeight.w500,
      ),
    );
  }

  void refreshState() {
    final newState = state.copyWith();
    emit(newState);
  }

  // 恢复对应分类的装扮到缓存中的默认装扮
  void _restoreCategoryDressUp(List<CustomCurrentUserDressUp> customDressUps, int dressUpCategoryId) {
    final oldDressup = _dressUpsCaches;
    if (oldDressup != null) {
      final cachedDressUp = _dressUpsCaches?.firstWhereOrNull(
          (dress) => dress?.dressUpCategoryId == dressUpCategoryId);

      if (cachedDressUp != null) {
        customDressUps.add(CustomCurrentUserDressUp(
          currenTdressUp: cachedDressUp,
          status: 2,
          price: '',
        ));
      } else {
        customDressUps.clear();
        for (var dressup in oldDressup) {
          customDressUps.add(CustomCurrentUserDressUp(
            currenTdressUp: dressup,
            status: 2,
            price: '',
          ));
        }
      }
    }
  }

  void resetPriceForChangeAssetsType() {
    final customSpaceHomeCurrentskinData = state.customSpaceHomeCurrentskinData;
    final customDressUps = customSpaceHomeCurrentskinData?.customDressUps;
    final categoryButtons = state.customSpaceMallResourceData?.categoryButtons;

      // 需要移除的装扮列表（在商城中找不到对应商品的装扮）
      List<CustomCurrentUserDressUp> itemsToRemove = [];
    if (customDressUps != null && categoryButtons != null) {
      for (CustomCurrentUserDressUp dressUp in customDressUps) {
        final dressUpCategoryId = dressUp.currenTdressUp?.dressUpCategoryId;
        final dressUpId = dressUp.currenTdressUp?.dressUpId;
        bool foundInMall = dressUp.status != 0;

        if (dressUpCategoryId != null && dressUpId != null) {
          // 在 categoryButtons 中找到对应的 categoryButton
          for (CustomCategoryButton categoryButton in categoryButtons) {
            if (categoryButton.categoryButton?.categoryId == dressUpCategoryId) {
              // 在该分类的 skins 中找到对应的商品
              final skins = categoryButton.customSpaceMallGoodsData?.skins;
              if (skins != null) {
                for (CustomGoodsSkin skin in skins) {
                  if (skin.goodsSkin?.goodId == dressUpId) {
                    // 找到匹配的商品，更新价格
                    dressUp.price = skin.goodsSkin?.price ?? '';
                    foundInMall = true;
                    break;
                  }
                }
              }
              break;
            }
          }
        }

        // 如果在商城中没有找到对应的商品，标记为需要移除
        if (!foundInMall) {
          itemsToRemove.add(dressUp);
        }
      }
    }

    final didChangeCustomDressUps = itemsToRemove.isNotEmpty;
    if (didChangeCustomDressUps) {
            // 移除找不到的商品并恢复对应分类的装扮
      for (CustomCurrentUserDressUp itemToRemove in itemsToRemove) {
        final dressUpCategoryId = itemToRemove.currenTdressUp?.dressUpCategoryId;

        // 移除商品
        customDressUps?.removeWhere((item) =>
            item.currenTdressUp?.dressUpId == itemToRemove.currenTdressUp?.dressUpId);

        // 恢复对应分类的装扮
        if (dressUpCategoryId != null && customDressUps != null) {
          _restoreCategoryDressUp(customDressUps, dressUpCategoryId);
        }
      }
    }

    customSpaceHomeCurrentskinData?.totalPrice =
        _calcTotalPrice(customDressUps);
    _updateSeletedSkinWithDressUps(customDressUps);

    if (didChangeCustomDressUps) {
      updateUserCurrentSkinList(customDressUps);
    }
  }

  // 从购物车中删除商品
  void removeItemFromCart(CustomCurrentUserDressUp itemToRemove) {
    final customSpaceHomeCurrentskinData = state.customSpaceHomeCurrentskinData;
    final customDressUps = customSpaceHomeCurrentskinData?.customDressUps;

    if (customDressUps != null) {
      // 找到要删除的商品并移除
      customDressUps.removeWhere((item) =>
          item.currenTdressUp?.dressUpId ==
          itemToRemove.currenTdressUp?.dressUpId);

      // 恢复对应分类的装扮
      final dressUpCategoryId = itemToRemove.currenTdressUp?.dressUpCategoryId;
      final oldDressup = _dressUpsCaches;
      if (dressUpCategoryId != null && oldDressup != null) {
        final cachedDressUp = _dressUpsCaches?.firstWhereOrNull(
            (dress) => dress?.dressUpCategoryId == dressUpCategoryId);

        if (cachedDressUp != null) {
          customDressUps.add(CustomCurrentUserDressUp(
            currenTdressUp: cachedDressUp,
            status: 2,
            price: '',
          ));
        } else {
          customDressUps.clear();
          for (var dressup in oldDressup) {
            customDressUps.add(CustomCurrentUserDressUp(
              currenTdressUp: dressup,
              status: 2,
              price: '',
            ));
          }
        }
      }

      // 重新计算总价
      customSpaceHomeCurrentskinData?.totalPrice = _calcTotalPrice(customDressUps);

      // 更新选中状态 - 将对应分类的选中状态重置
      _updateSeletedSkinWithDressUps(customDressUps);

      // 更新舞台显示
      updateUserCurrentSkinList(customDressUps);

      // 刷新状态
      refreshState();
    }
  }

  void dispose() {}
}

const logTag = "MyDressController";
