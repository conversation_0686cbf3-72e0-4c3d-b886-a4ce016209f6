
import 'dart:convert';

import 'package:jojo_flutter_base/base.dart';

part 'my_dress_balance_data.freezed.dart';
part 'my_dress_balance_data.g.dart';

LiabilitiesInfo liabilitiesInfoFromJson(String str) => LiabilitiesInfo.fromJson(json.decode(str));

String liabilitiesInfoToJson(LiabilitiesInfo data) => json.encode(data.toJson());

@freezed
class LiabilitiesInfo with _$LiabilitiesInfo {
  const factory LiabilitiesInfo({
    List<AssetList>? assetList,
  }) = _LiabilitiesInfo;

  factory LiabilitiesInfo.fromJson(Map<String, dynamic> json) => _$LiabilitiesInfoFromJson(json);
}

@freezed
class AssetList with _$AssetList {
  const factory AssetList({
    int? assetType,
    int? balance,
    bool? hideBalance,
    String? router,
    String? icon,
    List<Limit>? limits,
    Extra? extra,
  }) = _AssetList;

  factory AssetList.fromJson(Map<String, dynamic> json) => _$AssetListFromJson(json);
}

@freezed
class Extra with _$Extra {
  const factory Extra({
    int? guideType,
  }) = _Extra;

  factory Extra.fromJson(Map<String, dynamic> json) => _$ExtraFromJson(json);
}

@freezed
class Limit with _$Limit {
  const factory Limit({
    String? limitType,
    int? limitCount,
    int? curCount,
  }) = _Limit;

  factory Limit.fromJson(Map<String, dynamic> json) => _$LimitFromJson(json);
}

class BalanceAssetType {
  static int ASSET_TYPE_BEANS = 1;
  static int ASSET_TYPE_GEMSTONE = 4;
}
