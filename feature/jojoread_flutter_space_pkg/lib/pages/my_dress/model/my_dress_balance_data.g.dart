// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_dress_balance_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LiabilitiesInfo _$$_LiabilitiesInfoFromJson(Map<String, dynamic> json) =>
    _$_LiabilitiesInfo(
      assetList: (json['assetList'] as List<dynamic>?)
          ?.map((e) => AssetList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_LiabilitiesInfoToJson(_$_LiabilitiesInfo instance) =>
    <String, dynamic>{
      'assetList': instance.assetList,
    };

_$_AssetList _$$_AssetListFromJson(Map<String, dynamic> json) => _$_AssetList(
      assetType: json['assetType'] as int?,
      balance: json['balance'] as int?,
      hideBalance: json['hideBalance'] as bool?,
      router: json['router'] as String?,
      icon: json['icon'] as String?,
      limits: (json['limits'] as List<dynamic>?)
          ?.map((e) => Limit.fromJson(e as Map<String, dynamic>))
          .toList(),
      extra: json['extra'] == null
          ? null
          : Extra.fromJson(json['extra'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_AssetListToJson(_$_AssetList instance) =>
    <String, dynamic>{
      'assetType': instance.assetType,
      'balance': instance.balance,
      'hideBalance': instance.hideBalance,
      'router': instance.router,
      'icon': instance.icon,
      'limits': instance.limits,
      'extra': instance.extra,
    };

_$_Extra _$$_ExtraFromJson(Map<String, dynamic> json) => _$_Extra(
      guideType: json['guideType'] as int?,
    );

Map<String, dynamic> _$$_ExtraToJson(_$_Extra instance) => <String, dynamic>{
      'guideType': instance.guideType,
    };

_$_Limit _$$_LimitFromJson(Map<String, dynamic> json) => _$_Limit(
      limitType: json['limitType'] as String?,
      limitCount: json['limitCount'] as int?,
      curCount: json['curCount'] as int?,
    );

Map<String, dynamic> _$$_LimitToJson(_$_Limit instance) => <String, dynamic>{
      'limitType': instance.limitType,
      'limitCount': instance.limitCount,
      'curCount': instance.curCount,
    };
