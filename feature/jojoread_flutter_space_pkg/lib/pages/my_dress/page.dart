import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';

import 'dress_controller.dart';
import 'state.dart';
import 'view.dart';

/// 多人学我的装扮/商城页面
class SpaceMyDressPageModel extends BasePage {
  const SpaceMyDressPageModel({
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _SpaceMyDressPageModelState();
}

class _SpaceMyDressPageModelState extends BaseState<SpaceMyDressPageModel>
    with BasicInitPage {
  late final SpaceMyDressPageCtrl _controller = SpaceMyDressPageCtrl();
  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => _controller,
      child: BlocBuilder<SpaceMyDressPageCtrl, SpaceMyDressPageState>(
          builder: (context, state) {
        return AdaptiveOrientationLayout(portrait: (BuildContext context) {
          _controller.isLandscape = false;
          return SpaceMyDressPageView(state: state);
        }, landscape: (BuildContext context) {
          _controller.isLandscape = true;
          return SpaceMyDressPageView(state: state);
        });
      }),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
