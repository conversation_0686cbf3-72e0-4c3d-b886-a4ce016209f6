import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_base/widgets/incentive/continuous_days_fire.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/widget/space_balance_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/widget/space_character_customization_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/widget/space_mall_goods_preview_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/widget/space_mall_stage_snapshoot_widget.dart';
import 'package:jojoread_flutter_space_pkg/utils/file_util.dart';

import '../../common/host_env/host_env.dart';
import '../home_page/widget/back_widget.dart';
import 'dress_controller.dart';
import 'state.dart';
import 'widget/space_mall_stage_widget.dart';

class SpaceMyDressPageView extends StatefulHookWidget {
  final SpaceMyDressPageState state;
  const SpaceMyDressPageView({Key? key, required this.state}) : super(key: key);
  @override
  DressViewState createState() => DressViewState();
}

class DressViewState extends State<SpaceMyDressPageView> {
  final double containerWidth = screen.screenWidth;
  final double containerHeight = screen.screenHeight;
  final double flameHeight = 48.rdp;
  static final double offset = 24.rdp;
  //舞台高度
  var stageHeight = 362 / 375 * screen.screenWidth;
  //内容高度
  var contentHeight =
      screen.screenHeight - (362 / 375 * screen.screenWidth) + offset;
  @override
  void initState() {
    super.initState();
    if (isPadPortrait()) {
      stageHeight = screen.screenWidth * 9 / 16;
      contentHeight = screen.screenHeight - stageHeight + offset;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return JoJoPageLoadingV25(
        status: widget.state.pageStatus,
        exception: widget.state.exception,
        backWidget: const JoJoAppBar(backgroundColor: Colors.transparent,),
        emptyText: '暂无数据',
        retry: () {
          context.read<SpaceMyDressPageCtrl>().onRefresh();
        },
        child: Scaffold(
          primary: !JoJoRouter.isWindow,
          body: VisibilityObserve(
            onShow: () {
              RunEnv.sensorsTrack('ElementView', {
                'c_element_name': "装扮主页曝光",
              });
            },
            child: Container(
                height: containerHeight,
                color: Colors.white,
                child: _buildWidget()),
          ),
        ));
  }

  Widget _buildWidget() {
    final safeTop = MediaQuery.of(context).viewPadding.top;
    if (widget.state.hasDefalutSkin == false) {
      return Stack(children: const [SpaceMallCharacterCustomizationWidget()]);
    } else {
      return Stack(
        children: [
          SpaceShopSnapshootStageWidget(
            stageData: widget.state.stagePersonData,
            bgHeight: stageHeight,
            repaintBoundaryKey: widget.state.repaintBoundaryKey,
          ),
          SpaceShopStageWidget(
            stageData: widget.state.stagePersonData,
            bgHeight: stageHeight,
          ),
          Positioned(
              top: stageHeight -
                  context.dimensions.mediumSpacing.rdp -
                  flameHeight -
                  offset,
              right: context.dimensions.mediumSpacing.rdp,
              child: _buildFlameWidget()),
          Positioned(
            bottom: 0,
            child: SpaceMallGoodsPreviewWidget(
              state: widget.state,
              height: contentHeight,
            ),
          ),
          const JoJoAppBar(
              title: '',
              backgroundColor: Colors.transparent,
              centerTitle: true),
          Positioned(
              top: (JoJoRouter.isWindow ? 20.rdp : safeTop),
              right: 10.rdp,
              child: _buildBeanWidget()),
        ],
      );
    }
  }

  // 学豆布局
  _buildBeanWidget() {
    if (mounted == false) return Container();
    SpaceMyDressPageCtrl _ctrl = context.read<SpaceMyDressPageCtrl>();
    return SpaceBalanceWidget(data: _ctrl.state.balance);
  }

  _buildFlameWidget() {
    final continuousDays = widget.state.customSpaceMallResourceData?.bestContinuousLearnDays ?? 0;
    return ContinuousDaysFire(continuousDays: continuousDays, size: flameHeight,);
  }
}
