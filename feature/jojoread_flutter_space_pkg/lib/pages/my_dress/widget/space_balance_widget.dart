import 'dart:async';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojoread_flutter_space_pkg/common/config/config.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/dress_controller.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/model/my_dress_balance_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/state.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

import '../../../generated/l10n.dart';

const String _beansTypeCode = "1";   // 成长豆
const String _gemstoneTypeCode = "4";    // 宝石


class SpaceBalanceWidget extends StatefulWidget {
  final LiabilitiesInfo? data;
  const SpaceBalanceWidget({Key? key,required this.data,}): super(key: key);

  @override
  SpaceBalanceWidgetState createState() => SpaceBalanceWidgetState();
}

class SpaceBalanceWidgetState extends State<SpaceBalanceWidget> {

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _beansCntOnTap() async {
 if (mounted == false) return;
    SpaceMyDressPageCtrl _ctrl = context.read<SpaceMyDressPageCtrl>();
    try {
      if (_ctrl.isChangePayMethodType) return;  // 防重处理
      _ctrl.isChangePayMethodType = true;
      // 上报资产切换接口
      JoJoLoading.show(
        msg: S.of(context).pleaseWait,
        textStyle: context.textstyles.bodyTextEmphasis.pf.copyWith(
          color: HexColor('#ACB2BB'),
        ),
      );
      String type = _ctrl.payMethodType == PayMethodType.gemstone ? _beansTypeCode : _gemstoneTypeCode;
      bool success = await _ctrl.reportBalanceSwitch(type);
      // 根据请求状态判断是否应该保存资产兑换方式
      if (success) {
        _ctrl.payMethodType = _ctrl.payMethodType == PayMethodType.beans ? PayMethodType.gemstone : PayMethodType.beans;
        // 重新加载所有分类的已加载页面数据，保持分页状态
        await _ctrl.reloadAllCategoriesWithPagination();
        _ctrl.resetPriceForChangeAssetsType();
        _ctrl.refreshState();
        // await _ctrl.requestCurrentskinData();
      }
    }finally {
      JoJoLoading.dismiss();
      _ctrl.isChangePayMethodType = false;
      _checkBalance(_ctrl);
    }
  }

  void _checkBalance(SpaceMyDressPageCtrl _ctrl) {
    final assetType = _ctrl.payMethodType == PayMethodType.beans
        ? _beansTypeCode
        : _gemstoneTypeCode;
    final asset = widget.data?.assetList
        ?.firstWhere((element) => element.assetType.toString() == assetType);
    final totalPrice =
        _ctrl.state.customSpaceHomeCurrentskinData?.totalPrice ?? 0;
    if ((asset?.balance ?? 0) < totalPrice) {
      final text = _ctrl.payMethodType == PayMethodType.beans
          ? S.of(context).beans
          : S.of(context).gemstone;
      JoJoToast.showError('$text不足');
    }
  }

  Widget _buidBalanceIconWidget(String icon, PayMethodType type) {
    if (icon.isEmpty) {
      return ImageAssetWeb(
        assetName: type == PayMethodType.beans ? AssetsImg.MY_DRESS_MY_DRESS_CURRENCY_BEAN : AssetsImg.MY_DRESS_MY_DRESS_CURRENCY_GEM_STONE,
        width: 24.rdp,
        height: 24.rdp,
        fit: BoxFit.cover,
        package: Config.package,
      );
    } else {
      return ImageNetworkCached(
        height: 24.rdp,
        width: 24.rdp,
        fit: BoxFit.cover,
        imageUrl: icon,
      );
    }
  }

  Widget _buildItemWidget(String? icon, String? count, bool isSelected, PayMethodType type) {
    return GestureDetector(
      onTap: () {
        if (isSelected == false) {
          _beansCntOnTap();
        }
      },
      child: Container(
        constraints: BoxConstraints(
          minWidth: 88.rdp,
          minHeight: 32.rdp,
        ),
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 8.rdp, right: 8.rdp),
        color: isSelected ? null : Colors.transparent,
        decoration: isSelected ? BoxDecoration(
          color: Colors.white, // 设置背景色
          borderRadius: BorderRadius.circular(24.rdp), // 设置圆角
        ) : null,
        child: IntrinsicWidth(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buidBalanceIconWidget(icon ?? "", type),
              SizedBox(width: 4.rdp,),
              Flexible(
                child: Text(
                  count ?? "",
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                    fontSize: 15.rdp,
                    fontWeight: FontWeight.w600,
                    color: HexColor("#664314"),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    SpaceMyDressPageCtrl ctrl = context.read<SpaceMyDressPageCtrl>();
    AssetList? item1;
    AssetList? item2;
    widget.data?.assetList?.forEach((element) {
      if (element.assetType == BalanceAssetType.ASSET_TYPE_GEMSTONE) {
        item1 = element;
      }
      // 成长豆不需要判断
      if (element.assetType == BalanceAssetType.ASSET_TYPE_BEANS) {
        item2 = element;
      }
    });
    
    int count = widget.data?.assetList?.length ?? 0;
    if (count <= 0) return Container();
    if (item1 == null && item2 == null) return Container();
    if (item1 != null && item2 != null) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: context.dimensions.smallSpacing.rdp),
        constraints: BoxConstraints(
          minWidth: 196.rdp,
          minHeight: 50.rdp,
        ),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: context.appColors.jColorBrown2, // 设置背景色
          borderRadius: BorderRadius.circular(25.rdp), // 设置圆角
          border: Border.all(
            color: context.appColors.jColorBrown3, // 设置边框颜色
            width: 1.rdp, // 设置边框宽度
          ),
        ),
        child: IntrinsicWidth(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.only(left: 4.rdp, top: 4.rdp, right: 2.rdp, bottom: 4.rdp),
                  child: _buildItemWidget(item1?.icon ?? "", (item1?.balance ?? 0).toString(), ctrl.payMethodType == PayMethodType.gemstone, PayMethodType.gemstone)
              ),
              SizedBox(width: context.dimensions.minimumSpacing),
              Container(
                  padding: EdgeInsets.only(left: 2.rdp, top: 4.rdp, right: 4.rdp, bottom: 4.rdp),
                  child: _buildItemWidget(item2?.icon ?? "", (item2?.balance ?? 0).toString(), ctrl.payMethodType == PayMethodType.beans, PayMethodType.beans)
              ),
            ],
          ),
        ),
      );
    } else {
      if (item1 != null) {
        return _buildItemWidget(item1?.icon ?? "", (item1?.balance ?? 0).toString(), true, PayMethodType.gemstone);
      } else {
        return _buildItemWidget(item2?.icon ?? "", (item2?.balance ?? 0).toString(), true, PayMethodType.beans);
      }
    }
  }
}
