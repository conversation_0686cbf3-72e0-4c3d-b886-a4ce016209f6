import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/pull_refresh.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_currentskin_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/widget/space_home_goods_listcell_widget.dart';
import '../../../common/host_env/host_env.dart';
import '../../../static/img.dart';
import '../../../utils/file_util.dart';
import '../dress_controller.dart';
import '../state.dart';

class SpaceMallGoodsGridviewWidget extends StatefulWidget {
  final SpaceMyDressPageState state;
  final CustomCategoryButton customCategoryButton;
  const SpaceMallGoodsGridviewWidget({
    Key? key,
    required this.state,
    required this.customCategoryButton,
  }) : super(key: key);

  @override
  SpaceMallGoodsGridviewWidgetState createState() =>
      SpaceMallGoodsGridviewWidgetState();
}

class SpaceMallGoodsGridviewWidgetState
    extends State<SpaceMallGoodsGridviewWidget>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late RefreshController _refreshController;
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController();
  }

  @override
  void dispose() {
    // 释放所有 RefreshController
    _refreshController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant SpaceMallGoodsGridviewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (mounted == false) return;
    SpaceMyDressPageCtrl ctrl = context.read<SpaceMyDressPageCtrl>();
    if (widget.customCategoryButton.resetRefreshToolStatus) {
      _refreshController.resetNoData();
      for (var element in ctrl.state.customSpaceMallResourceData?.categoryButtons ?? []) {
        if (element.categoryButton?.categoryId == widget.customCategoryButton.categoryButton?.categoryId) {
          element.resetRefreshToolStatus = false;
          return;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Padding(
        padding:
            EdgeInsets.symmetric(horizontal: isPadPortrait() ? 40.rdp : 20.rdp),
        child: PullOrRefresh(
            enablePullDown: false,
            onLoading: () async {
              bool completed = await context
                  .read<SpaceMyDressPageCtrl>()
                  .pullUpRefresh(
                      _refreshController,
                      widget.customCategoryButton.categoryButton?.categoryId ??
                          0,
                      refreshpageNum,
                      widget.customCategoryButton.pageNum);
              if (completed) {
                setState(() {});
              }
            },
            refreshController: _refreshController,
            child: CustomScrollView(
              slivers: [
                if (widget.customCategoryButton.categoryButton
                            ?.reachBuyThreshold !=
                        null &&
                    widget.customCategoryButton.categoryButton?.buyGoodsTips
                            ?.isNotEmpty ==
                        true)
                  _buildTopTipsWidget(context),
                _buildGridWidget(context),
              ],
            )));
  }

  SliverGrid _buildGridWidget(BuildContext context) {
    return SliverGrid(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 106.0 / 153.0,
        mainAxisSpacing: 10.rdp,
        crossAxisSpacing: 10.rdp,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, itemIndex) {
          CustomGoodsSkin? skinData = widget
              .customCategoryButton.customSpaceMallGoodsData?.skins?[itemIndex];
          return GestureDetector(
              onTap: () {
                // 点击事件处理逻辑
                print('Item $itemIndex clicked');

                if (widget.customCategoryButton.customSpaceMallGoodsData
                        ?.seletedSkinId ==
                    skinData?.goodsSkin?.goodId) {
                  return;
                }

                //更新任务装扮皮肤信息
                context
                    .read<SpaceMyDressPageCtrl>()
                    .updateUserCurrentSkinInfo(CustomCurrentUserDressUp(
                      currenTdressUp: CurrentUserDressUps(
                          dressUpId: skinData?.goodsSkin?.goodId ?? 0,
                          dressUpCategoryId:
                              skinData?.goodsSkin?.dressUpCategory ?? 0,
                          dressShowUrl: skinData?.goodsSkin?.dressShowUrl ?? '',
                          resourcesZipUrl:
                              skinData?.goodsSkin?.resourcesZipUrl ?? '',
                          status: skinData?.goodsSkin?.status,
                          price: skinData?.goodsSkin?.price,
                          children:
                              skinData?.goodsSkin?.children?.map((goodsChild) {
                            if (goodsChild == null) return null;
                            return CurrentUserDressUps(
                              dressUpId: goodsChild.goodId ?? 0,
                              dressUpCategoryId:
                                  goodsChild.dressUpCategory ?? 0,
                              dressShowUrl: goodsChild.dressShowUrl ?? '',
                              resourcesZipUrl: goodsChild.resourcesZipUrl ?? '',
                              status: goodsChild.status,
                              price: goodsChild.price,
                            );
                          }).toList()),
                      status: skinData?.status,
                      price: skinData?.goodsSkin?.price,
                    ));
                setState(() {
                  widget.customCategoryButton.customSpaceMallGoodsData
                      ?.seletedSkinId = skinData?.goodsSkin?.goodId ?? 0;
                });
              },
              child: SpaceMallGoodsListCellWidget(
                isSelected: widget.customCategoryButton.customSpaceMallGoodsData
                            ?.seletedSkinId ==
                        skinData?.goodsSkin?.goodId
                    ? true
                    : false,
                skin: skinData,
                resourceData: widget.state.customSpaceMallResourceData,
              ));
        },
        childCount: widget
                .customCategoryButton.customSpaceMallGoodsData?.skins?.length ??
            0,
      ),
    );
  }

  SliverToBoxAdapter _buildTopTipsWidget(BuildContext context) {
    final enable =
        widget.customCategoryButton.categoryButton?.reachBuyThreshold ?? false;
    final assetName = enable
        ? AssetsImg.MY_DRESS_MY_DRESS_ENABLE_BUY_TIP_ICON
        : AssetsImg.MY_DRESS_MY_DRESS_DISABLE_BUY_TIP_ICON;
    final mediumSpacing = context.dimensions.mediumSpacing;
    final smallSpacing = context.dimensions.smallSpacing;
    final largeCornerRadius = context.dimensions.largeCornerRadius;
    return SliverToBoxAdapter(
      child: Container(
        height: 42.rdp,
        margin: EdgeInsets.only(bottom: mediumSpacing.rdp),
        padding: EdgeInsets.symmetric(horizontal: mediumSpacing.rdp, vertical: smallSpacing.rdp),
        decoration: BoxDecoration(
          color: context.appColors.jColorBrown1,
          borderRadius: BorderRadius.circular(largeCornerRadius.rdp),
          image: enable ? DecorationImage(
            image: AssetImage(
              AssetsImg.MY_DRESS_MY_DRESS_ENABLE_BUY_TIP_BG,
              package: RunEnv.package,
            ),
            fit: BoxFit.fill,
          ) : null,
        ),
        child: Row(
          children: [
            ImageAssetWeb(
              assetName: assetName,
              width: 26.rdp,
              height: 26.rdp,
              fit: BoxFit.cover,
              package: RunEnv.package,
            ),
            SizedBox(width: smallSpacing.rdp),
            Expanded(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Text(
                  widget.customCategoryButton.categoryButton?.buyGoodsTips ??
                      '',
                  style: context.textstyles.bodyText.pf
                      .copyWith(color: context.appColors.jColorBrown6),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
