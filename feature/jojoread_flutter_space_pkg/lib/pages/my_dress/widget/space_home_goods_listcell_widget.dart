import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/cached_network_image.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/dress_controller.dart';

import '../../../common/config/config.dart';
import '../../../static/img.dart';
import '../state.dart';

class SpaceMallGoodsListCellWidget extends StatefulWidget {
  final CustomGoodsSkin? skin;
  final bool isSelected;
  final CustomSpaceMallResourceData? resourceData;

  const SpaceMallGoodsListCellWidget(
      {Key? key,
       required this.isSelected,
       required this.skin,
       this.resourceData})
      : super(key: key);

  @override
  SpaceMallGoodsListCellWidgetState createState() =>
      SpaceMallGoodsListCellWidgetState();
}

class SpaceMallGoodsListCellWidgetState
    extends State<SpaceMallGoodsListCellWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget getTagWidget() {
    int status = widget.skin?.status ?? 0;
    String assetName = '';
    switch (status) {
      case 1:
        assetName = AssetsImg.MY_DRESS_MY_DRESS_OWNED_ICON;
        break;
      case 2:
        assetName = AssetsImg.MY_DRESS_MY_DRESS_DRESS_UP_ICON;
        break;
    }
    PayMethodType payMethodType = PayMethodType.beans;
    if (context != null) {
      SpaceMyDressPageCtrl ctrl = context.read<SpaceMyDressPageCtrl>();
      payMethodType = ctrl.payMethodType;
    }

    if (status == 0) {
      return SizedBox(
            child: Row(
              children: [
                // const Spacer(),
                ImageAssetWeb(
                  assetName: payMethodType == PayMethodType.beans ? AssetsImg.MY_DRESS_MY_DRESS_CURRENCY_BEAN : AssetsImg.MY_DRESS_MY_DRESS_CURRENCY_GEM_STONE,
                  width: 18.rdp,
                  height: 18.rdp,
                  fit: BoxFit.contain,
                  package: Config.package,
                ),
                Text(
                 widget.skin?.goodsSkin?.price ?? "",
                  textAlign: TextAlign.right,
                  style: TextStyle(
                    fontSize: 12.rdp,
                    color: HexColor('#B2B2B2'),
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                )
              ],
            ),
          );
    } else if (status == 1) {
      return SizedBox(width: 18.rdp, height: 18.rdp,);
    } else {
      return ImageAssetWeb(
        assetName: assetName,
        width: 18.rdp,
        height: 18.rdp,
        fit: BoxFit.contain,
        package: Config.package,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    const selectedBoxName = AssetsImg.MY_DRESS_MY_DRESS_CELL_SELECTED_BOX;
    final radius = widget.isSelected ? 30.rdp : context.dimensions.largeCornerRadius.rdp;
    final circurlar = Radius.circular(radius);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(
          Radius.circular(12.0.rdp),
        ),
        color: Colors.transparent,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
              child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(
                      circurlar,
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.all(
                          circurlar,
                        ),
                        child: SizedBox(
                          height: double.infinity,
                          width: double.infinity,
                          child: ImageNetworkCached(
                            imageUrl: widget.resourceData
                                    ?.getBackgroundImageUrl(
                                        widget.skin?.goodsSkin?.dressUpLevel) ??
                                "",
                            placeholderWidget: _backgroundImagePlaceholder(),
                            errorWidget: _backgroundImagePlaceholder(),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      ClipRRect(
                        borderRadius: widget.isSelected
                            ? BorderRadius.all(
                                circurlar,
                              )
                            : BorderRadius.zero,
                        child: SizedBox(
                          width: double.infinity,
                          height: double.infinity,
                          child: ImageNetworkCached(
                            imageUrl:
                                widget.skin?.goodsSkin?.dressShowUrl ?? '',
                            fit: BoxFit.contain,
                            memCacheWidth: 120.rdp.toInt() *
                                MediaQuery.of(context)
                                    .devicePixelRatio
                                    .toInt(), // 设置最大宽度避免崩溃
                          ),
                        ),
                      ),
                      // 选中状态的覆盖图片
                      if (widget.isSelected)
                        Positioned.fill(
                          child: ImageAssetWeb(
                            assetName: selectedBoxName,
                            fit: BoxFit.fill,
                            package: Config.package,
                          ),
                        ),
                    ],
                  ))),
          Padding(
            padding: EdgeInsets.only(
                left: context.dimensions.smallSpacing.rdp,
                top: context.dimensions.minimumSpacing.rdp),
            child: SizedBox(
              height: 21.rdp,
              child: Text(
                widget.skin?.goodsSkin?.dressUpName ?? '',
                style: context.textstyles.remark.pf
                    .copyWith(color: context.appColors.titleTextColor),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: context.dimensions.smallSpacing.rdp),
            child: getTagWidget(),
          )
        ],
      ),
    );
  }

  ImageAssetWeb _backgroundImagePlaceholder() {
    return ImageAssetWeb(
                      assetName: AssetsImg.SPACE_MALL_GOODS_BG,
                      fit: BoxFit.contain,
                      package: Config.package,
                      borderRadius: 12.rdp,
                    );
  }
}
