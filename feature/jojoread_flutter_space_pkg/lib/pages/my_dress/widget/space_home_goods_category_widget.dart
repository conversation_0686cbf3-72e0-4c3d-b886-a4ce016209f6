import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';

import '../../../common/config/config.dart';
import '../../../static/img.dart';
import '../../../utils/file_util.dart';
import '../state.dart';

class SpaceMallGoodsCategoryWidget extends StatefulWidget {
  final CustomCategoryButton categoryButton;
  final bool isSelected;
  final bool hasAddedToCart;
  const SpaceMallGoodsCategoryWidget(
      {Key? key,
      required this.isSelected,
      required this.categoryButton,
      required this.hasAddedToCart,})
      : super(key: key);

  @override
  SpaceMallGoodsCategoryWidgetState createState() =>
      SpaceMallGoodsCategoryWidgetState();
}

class SpaceMallGoodsCategoryWidgetState
    extends State<SpaceMallGoodsCategoryWidget> {
  // SpaceMallResourceData spaceMallResourceData = const SpaceMallResourceData();
  // List<CategoryButton>? categoryButtons = [CategoryButton(iconUrl)];

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textStyle = isPadPortrait() ?  context.textstyles.headingLargeEmphasis.pf : context.textstyles.bodyTextEmphasis.pf;
    final width = widget.isSelected ? 94.rdp : 52.rdp;
    return Stack(
      children: [
        Container(
          width: width,
          height: isPadPortrait() ? 60.rdp : 40.rdp,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(
                Radius.circular(context.dimensions.largeCornerRadius.rdp),
              ),
            border: widget.isSelected
                ? Border.all(
                    color: context.appColors.jColorBrown3,
                    width: 1.rdp,
                  )
                : null,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ImageNetworkCached(
                imageUrl: widget.isSelected == true
                    ? (widget.categoryButton.categoryButton?.buttonSelectUrl ?? '')
                    : (widget.categoryButton.categoryButton?.buttonUnSelectUrl ??
                        ''),
                fit: BoxFit.contain,
                width: isPadPortrait() ? 36.rdp : 24.rdp,
                height: isPadPortrait() ? 36.rdp : 24.rdp,
                errorWidget: ImageAssetWeb(
                  assetName: AssetsImg.SPACE_MALL_HAT,
                  width: isPadPortrait() ? 36.rdp : 24.rdp,
                  height: isPadPortrait() ? 36.rdp : 24.rdp,
                  fit: BoxFit.contain,
                  package: Config.package,
                ),
              ),
              SizedBox(
                width: context.dimensions.minimumSpacing.rdp,
              ),
              Visibility(
                  visible: widget.isSelected,
                  child: Text(
                    widget.categoryButton.categoryButton?.buttonName ?? '',
                    style: textStyle.copyWith(color: context.appColors.jColorBrown5),
                    overflow: TextOverflow.ellipsis,
                  ))
            ],
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          child: Visibility(
            visible: widget.hasAddedToCart,
              child: Container(
            width: 16.rdp,
            height: 16.rdp,
            alignment: Alignment.topCenter,
            decoration: BoxDecoration(
                color: context.appColors.jColorOrange4,
                borderRadius: BorderRadius.circular(8.rdp)),
            child: Text(
              '1',
              style: context.textstyles.smallestText.mrB.copyWith(
                color: Colors.white,
                fontSize: 10.rdp,
                fontFamily: 'MohrRounded_Bold',
                package: "jojo_flutter_base",
              ),
            ),
          )),
        )
      ],
    );
  }
}
