import 'dart:io';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_skin_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/state.dart';
import 'package:jojoread_flutter_space_pkg/utils/file_util.dart';

/// 商城舞台widget
class SpaceShopStageWidget extends StatefulWidget {
  final ShopStagePersonData? stageData;
  final double bgHeight; //背景高度

  const SpaceShopStageWidget({
    required this.stageData,
    required this.bgHeight,
    Key? key,
  }) : super(key: key);

  @override
  SpaceShopInfoWidgetState createState() => SpaceShopInfoWidgetState();
}

class SpaceShopInfoWidgetState extends State<SpaceShopStageWidget> {
  final double containerWidth = screen.screenWidth;
  //人物动画控制器
  JoJoSpineAnimationController stageController = JoJoSpineAnimationController();

  var personScale = 1.0; // 人物缩放比例

  @override
  void initState() {
    super.initState();
    if (isPadPortrait()) {
      //pad 竖屏
      personScale = 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: containerWidth,
        height: widget.bgHeight,
        child: Stack(
          children: [
            //背景图
            if (widget.stageData?.bgImageFile?.isNotEmpty == true)
              Image.file(
                File(widget.stageData?.bgImageFile ?? ''),
                fit: BoxFit.fill,
                width: containerWidth,
                height: widget.bgHeight,
                errorBuilder: (BuildContext context, Object exception,
                    StackTrace? stackTrace) {
                  return Container(
                    color: HexColor('#FFF6E0'),
                  );
                },
              ),
            Positioned(
                bottom: 30.rdp, // 设置距离底部的距离
                left: 0,
                right: 0,
                child: SizedBox(
                  width: 198.rdp * personScale,
                  height: 231.rdp * personScale,
                  child: JoJoSpineSkinWidget(
                    widget.stageData?.spineData?.atlasFile ?? "",
                    widget.stageData?.spineData?.skelFile ?? "",
                    "shopStage",
                    controller: stageController,
                    skinList: widget.stageData?.spineData?.skinList,
                    loadSkinCallback: (status) {
                      if (status == SkinStatus.success) {
                        stageController.playAnimation(JoJoSpineAnimation(
                            animaitonName: "idle",
                            trackIndex: 0,
                            loop: true,
                            delay: 0));
                      }
                      if (status != SkinStatus.start) {
                        JoJoLoading.dismiss();
                      }
                    },
                  ),
                )),
          ],
        ));
  }
}
