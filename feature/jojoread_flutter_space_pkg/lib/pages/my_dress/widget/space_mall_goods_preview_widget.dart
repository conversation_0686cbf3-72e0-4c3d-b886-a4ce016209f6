import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/btn.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/widget/space_home_goods_category_widget.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/widget/space_mall_goods_confirm_buying_dialog.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/widget/space_mall_gridview_widget.dart';

import '../../../common/host_env/host_env.dart';
import '../../../generated/l10n.dart';
import '../../../static/svg.dart';
import '../../../utils/file_util.dart';
import '../../../static/audio.dart';
import '../../../static/img.dart';
import '../dress_controller.dart';
import '../state.dart';

class SpaceMallGoodsPreviewWidget extends StatefulWidget {
  final SpaceMyDressPageState state;
  final double? height;
  const SpaceMallGoodsPreviewWidget({
    Key? key,
    required this.height,
    required this.state,
  }) : super(key: key);

  @override
  SpaceMallGoodsPreviewWidgetState createState() =>
      SpaceMallGoodsPreviewWidgetState();
}

class SpaceMallGoodsPreviewWidgetState
    extends State<SpaceMallGoodsPreviewWidget>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  final PageController _pageController = PageController();
  int selectedIndex = 0;
  AudioPlayer? _audioPlayer;
  bool _isCartExpanded = false; // 购物车展开状态
  OverlayEntry? _overlayEntry; // 蒙层覆盖层

  // 购物车动画相关
  late AnimationController _cartAnimationController;
  late Animation<double> _cartOpacityAnimation;
  bool _hasShownCart = false; // 记录是否已经显示过购物车

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    _cartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    _cartOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cartAnimationController,
      curve: Curves.easeOut,
    ));
  }

  Future<void> _handleTabChange(int index) async {
    RunEnv.sensorsTrack('\$AppClick', {
      '\$element_name': '商品tab切换',
    });
    CustomCategoryButton? currentCategoryButton =
        widget.state.customSpaceMallResourceData?.categoryButtons?[index];
    _pageController.jumpToPage(index);

    setState(() {
      widget.state.customSpaceMallResourceData?.seletedCategoryId =
          currentCategoryButton?.categoryButton?.categoryId ?? 0;
    });

    SpaceMyDressPageCtrl _ctrl = context.read<SpaceMyDressPageCtrl>();
    _ctrl.currentPageIndex = index;
    bool isSuccess = await _ctrl.requestMallShopInfoData(
            false,
            false,
            null,
            currentCategoryButton?.categoryButton?.categoryId ?? 0,
            refreshpageNum,
            currentCategoryButton?.pageNum ?? 0);
    //等待接口请求完毕之后，刷新当前皮肤列表数据
    if (isSuccess) {
      setState(() {});
    }
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached ||
        state == AppLifecycleState.inactive) {
      if (_audioPlayer?.state == PlayerState.playing) {
        await _audioPlayer?.stop();
      }
    }
  }

  Future<void> _playLockTipAudio() async {
    try {
      // 停止之前的播放
      if (_audioPlayer?.state == PlayerState.playing) {
        await _audioPlayer?.stop();
      }

      _audioPlayer = AudioPlayer();
      AudioPlayer.global.setGlobalAudioContext(
        const AudioContext(
          iOS: AudioContextIOS(
            category: AVAudioSessionCategory.playback,
            options: [
              AVAudioSessionOptions.mixWithOthers,
            ],
          ),
        ),
      );

      String? package = RunEnv.package;
      String path = AssetsAudio.BUY_LOCK_TIP;
      String keyName = package == null ? path : 'packages/$package/$path';
      _audioPlayer!.audioCache.prefix = '';
      await _audioPlayer?.play(AssetSource(keyName));
    } catch (e) {
      l.d('装扮商城','播放音频失败: $e');
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _audioPlayer?.dispose();
    _cartAnimationController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _removeOverlay(); // 清理蒙层
    // 释放所有 RefreshController
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    CustomSpaceMallResourceData? customSpaceMallResourceData = widget.state.customSpaceMallResourceData;
    int totalPrice = widget.state.customSpaceHomeCurrentskinData?.totalPrice ?? 0;
    final customDressUps = widget.state.customSpaceHomeCurrentskinData?.customDressUps;

    if (totalPrice <= 0 && _hasShownCart) {
      // 当totalPrice变为0时，重置动画状态，为下次显示做准备
      _hasShownCart = false;
      _cartAnimationController.reset();
    }

    List<CustomCategoryButton>? categoryButtons =
        customSpaceMallResourceData?.categoryButtons ?? [];
    double tab_edge = isPadPortrait() ? 40.rdp : 20.rdp; //左右间距
    final mediumSpacing = context.dimensions.mediumSpacing.rdp;
    SpaceMyDressPageCtrl ctrl = context.read<SpaceMyDressPageCtrl>();

    final isSelectedGroup = ctrl.isSelectedUnReachGroupDress;
    final customCategoryButton = _currentCategoryButton(ctrl);
    final shouldShowReachTips = customCategoryButton?.shouldShowReachTips ?? false;
    final unReachBuyThreshold =
        shouldShowReachTips &&
            customCategoryButton?.categoryButton?.reachBuyThreshold == false;
    final purchaseSwitch = widget.state.customSpaceMallResourceData?.purchaseSwitch == 1;
    var hideBottomBtn = totalPrice > 0 && (unReachBuyThreshold || isSelectedGroup) && !purchaseSwitch;
    Color borderColor = context.appColors.jColorBrown3;
    final borderWidth = 1.rdp;
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24.rdp),
                topRight: Radius.circular(24.rdp),
              ),
              border: Border.all(color: borderColor, width: borderWidth),
              color: Colors.white,
            ),
          ),
        ),

        // 主要内容
        Padding(
          padding:EdgeInsets.only(left: borderWidth, top: borderWidth, right: borderWidth),
          child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24.rdp - borderWidth),
                topRight: Radius.circular(24.rdp - borderWidth),
              ),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  context.appColors.jColorBrown1.withOpacity(0.3),
                  Colors.white,
                  Colors.white,
                ],
                stops: [0.0, (40.rdp / (widget.height ?? 1)).clamp(0.0, 1.0), 1.0],
              ),
            ),
            height: widget.height,
            width: screen.screenWidth - 2 * borderWidth,
            child: Column(
              children: [
                SizedBox(height: mediumSpacing),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: tab_edge),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: categoryButtons.asMap().entries.map((entry) {
                      int index = entry.key;
                      CustomCategoryButton currentCategoryButton = entry.value;
                      return GestureDetector(
                        onTap: () {
                          if (selectedIndex != index) {
                            _handleTabChange(index);
                            selectedIndex = index;
                          }
                        },
                        child: SpaceMallGoodsCategoryWidget(
                          categoryButton: currentCategoryButton,
                          isSelected: customSpaceMallResourceData
                                  ?.seletedCategoryId ==
                              currentCategoryButton.categoryButton?.categoryId,
                          hasAddedToCart: customDressUps?.any((element) =>
                                  element.currenTdressUp?.dressUpCategoryId ==
                                  currentCategoryButton
                                      .categoryButton?.categoryId && element.status == 0) ??
                              false,
                        ),
                      );
                    }).toList(),
                  ),
                ),
                SizedBox(
                  height: mediumSpacing,
                ),
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount:
                        customSpaceMallResourceData?.categoryButtons?.length,
                    itemBuilder: (context, index) {
                      List<CustomCategoryButton> categoryButtons =
                          customSpaceMallResourceData?.categoryButtons ?? [];
                      CustomCategoryButton customCategoryButton =
                          categoryButtons[index];
                      return SpaceMallGoodsGridviewWidget(
                        //列表组件
                        customCategoryButton: customCategoryButton,
                        state: widget.state,
                      );
                    },
                  ),
                ),

              ],
            ),
          ),
        ),

        if (!hideBottomBtn)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Stack(
              children: [
                Container(
                  height: 75.rdp + MediaQuery.of(context).viewPadding.bottom,
                  width: screen.screenWidth,
                  decoration: BoxDecoration(
                    color: _isCartExpanded ? Colors.white : null,
                    gradient: _isCartExpanded ? null : LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white.withOpacity(0),
                        Colors.white,
                        Colors.white,
                      ],
                      stops: _isCartExpanded ? null : [0.0, (20 / (75.rdp + MediaQuery.of(context).viewPadding.bottom)), 1.0],
                    ),
                  ),
                ),
                _bottomBtnWidget(totalPrice, context, ctrl)
              ],
            ),
          ),
      ],
    );
  }

  Widget _bottomBtnWidget(
      int totalPrice, BuildContext context, SpaceMyDressPageCtrl ctrl) {
    if (totalPrice <= 0) {
        return _buildSaveAndBuyBtn(context, totalPrice, ctrl);
    }
    final purchaseSwitch = widget.state.customSpaceMallResourceData?.purchaseSwitch == 1;
    if (purchaseSwitch) {
      return _buildLockWidget(context);
    }
    return _buildSaveAndBuyBtn(context, totalPrice, ctrl);
  }

  Widget _buildLockWidget(BuildContext context) {
    final tab_edge = isPadPortrait() ? 40.rdp : 20.rdp; //左右间距
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: tab_edge),
      child: Center(
        child: GestureDetector(
            onTap: () {
              _playLockTipAudio();
              RunEnv.sensorsTrack(
                  '\$AppClick', {'\$element_name': '装扮商城_兑换限制按钮'});
            },
            child: Container(
              height: 44.rdp,
              decoration: BoxDecoration(
                color: context.appColors.jColorGray2,
                borderRadius: BorderRadius.circular(30.rdp),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ImageAssetWeb(
                    assetName: AssetsImg.MY_DRESS_BUY_LOCK,
                    width: 24.rdp,
                    height: 24.rdp,
                    fit: BoxFit.contain,
                    package: RunEnv.package,
                  ),
                   Text(
                      '兑换限制中',
                      style: context.textstyles.bodyTextLargeEmphasis.pf
                          .copyWith(color: context.appColors.auxiliaryTextColor),
                    )
                ],
              ),
            )),
      ),
    );
  }

  Widget _buildSaveAndBuyBtn(
      BuildContext context, int totalPrice, SpaceMyDressPageCtrl ctrl) {
    String? payMethodTypeStr =
        ctrl.payMethodType == PayMethodType.beans ? '成长豆' : '宝石';

    // 如果totalPrice > 0，显示新的购物车UI
    if (totalPrice > 0) {
      return _buildCartUI(context, totalPrice, payMethodTypeStr, ctrl);
    }

    // 否则显示原来的保存按钮
    return Padding(
        padding: EdgeInsets.only(top: 18.rdp),
        child: Center(
            child: JoJoBtn(
          height: 44.rdp,
          width: 280.rdp,
          text: '保存',
          color: context.appColors.mainColor,
          tapHandle: () async {
            //购买
            bool isSuccess = await context
                .read<SpaceMyDressPageCtrl>()
                .actionBuySkin(0, 0, 0, context);
            if (isSuccess) {
              // 请求我的资产接口，更新资产
              ctrl.state.balance = await ctrl.requestMyBalanceData();
              ctrl.refreshState();
            }
          },
        )));
  }

  CustomCategoryButton? _currentCategoryButton(SpaceMyDressPageCtrl ctrl) {
    final selectedIndex = ctrl.currentPageIndex;
    final categoryButtons =
        widget.state.customSpaceMallResourceData?.categoryButtons;
    if (categoryButtons != null && selectedIndex < categoryButtons.length) {
      return categoryButtons[selectedIndex];
    } else {
      return null;
    }
  }

  // 新的购物车UI
  Widget _buildCartUI(BuildContext context, int totalPrice, String payMethodTypeStr, SpaceMyDressPageCtrl ctrl) {
    final cartItems = _getCartItems(ctrl);
    final itemCount = cartItems.length;

    if (!_hasShownCart) {
      _hasShownCart = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _cartAnimationController.forward();
      });
    }

    return AnimatedBuilder(
      animation: _cartOpacityAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _cartOpacityAnimation.value,
          child: _buildCartBottomBar(context, totalPrice, payMethodTypeStr, itemCount, ctrl),
        );
      },
    );
  }

  void _showOverlay(BuildContext context, SpaceMyDressPageCtrl ctrl) {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => StatefulBuilder(
        builder: (context, overlaySetState) => _buildFullScreenOverlay(context, ctrl, overlaySetState),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isCartExpanded = true;
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _isCartExpanded = false;
    });
  }

  Widget _buildFullScreenOverlay(BuildContext context, SpaceMyDressPageCtrl ctrl, StateSetter overlaySetState) {
    final cartItems = _getCartItems(ctrl);
    final bottom = 75.rdp + MediaQuery.of(context).viewPadding.bottom;

    return Positioned(
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            bottom: bottom + 20.rdp,
            child: GestureDetector(
              onTap: () {
                _removeOverlay();
              },
              child: Container(
                color: Colors.black.withOpacity(0.5),
              ),
            ),
          ),
          Positioned(
            bottom: bottom - 2.rdp,
            left: 0,
            right: 0,
            child: _buildExpandedCartList(context, cartItems, ctrl, overlaySetState),
          ),
        ],
      ),
    );
  }

  // 获取购物车中的商品（status为0表示未购买，且有价格的商品）
  List<CustomCurrentUserDressUp> _getCartItems(SpaceMyDressPageCtrl ctrl) {
    final cartItems = ctrl.state.customSpaceHomeCurrentskinData?.customDressUps
        ?.where((item) => item.status == 0 && (item.price?.isNotEmpty ?? false) && item.price != '0')
        .toList() ?? [];

    final categoryButtons = widget.state.customSpaceMallResourceData?.categoryButtons ?? [];
    final categoryOrder = <int, int>{};

    for (int i = 0; i < categoryButtons.length; i++) {
      final categoryId = categoryButtons[i].categoryButton?.categoryId;
      if (categoryId != null) {
        categoryOrder[categoryId] = i;
      }
    }

    // 根据categoryId顺序排序
    cartItems.sort((a, b) {
      final categoryIdA = a.currenTdressUp?.dressUpCategoryId ?? -1;
      final categoryIdB = b.currenTdressUp?.dressUpCategoryId ?? -1;

      final orderA = categoryOrder[categoryIdA] ?? 999; // 未找到的放到最后
      final orderB = categoryOrder[categoryIdB] ?? 999;

      return orderA.compareTo(orderB);
    });

    return cartItems;
  }

  // 购物车底部栏
  Widget _buildCartBottomBar(BuildContext context, int totalPrice,
      String payMethodTypeStr, int itemCount, SpaceMyDressPageCtrl ctrl) {
    // 限制显示的数量在1-4之间
    int displayCount = itemCount.clamp(1, 4);
    final assetName = ctrl.payMethodType == PayMethodType.beans
        ? AssetsImg.MY_DRESS_MY_DRESS_CURRENCY_BEAN
        : AssetsImg.MY_DRESS_MY_DRESS_CURRENCY_GEM_STONE;

    return GestureDetector(
      onTap: () {
        if (_isCartExpanded) {
          _removeOverlay();
        } else {
          _showOverlay(context, ctrl);
        }
      },
      child: Container(
        margin: EdgeInsets.only(top: 18.rdp),
        padding: EdgeInsets.symmetric(horizontal: 20.rdp),
        child: Row(
          children: [
            Container(
              width: 60.rdp,
              height: 44.rdp,
              decoration: BoxDecoration(
                color: context.appColors.jColorYellow4,
                borderRadius: BorderRadius.circular(25.rdp),
              ),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Center(
                    child: ImageAssetWeb(
                      width: 24.rdp,
                      height: 24.rdp,
                      assetName: AssetsImg.MY_DRESS_DRESS_SHOP_SHOPPING_CART,
                      package: RunEnv.package,
                    ),
                  ),
                  if (itemCount > 0)
                    Positioned(
                      right: 0,
                      top: -6.rdp,
                      child: Container(
                        width: 20.rdp,
                        height: 20.rdp,
                        decoration: BoxDecoration(
                          color: context.appColors.jColorOrange4,
                          shape: BoxShape.circle,
                        ),
                        child: Align(
                          alignment: Alignment.topCenter,
                          child: SizedBox(
                            height: 18.rdp,
                            child: Text(
                              displayCount.toString(),
                              style:
                                  context.textstyles.smallestText.mrB.copyWith(
                                color: Colors.white,
                                fontFamily: 'MohrRounded_Bold',
                                package: "jojo_flutter_base",
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            SizedBox(width: context.dimensions.minimumSpacing),

            // 合计和结算按钮
            Expanded(
              child: SizedBox(
                height: 44.rdp,
                child: Row(
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(S.of(context).buyCarTotal,
                            style: context.textstyles.smallestText.pf.copyWith(
                                color: context.appColors.titleTextColor)),
                        Row(
                          children: [
                            ImageAssetWeb(
                              assetName: assetName,
                              width: 18.rdp,
                              height: 18.rdp,
                                fit: BoxFit.contain,
                                package: RunEnv.package,
                              ),
                              SizedBox(width: 2.rdp),
                              Text(
                                totalPrice.toString(),
                              style: context.textstyles.bodyText.mrB.copyWith(
                                color: context.appColors.jColorGray4,
                                fontFamily: 'MohrRounded_Bold',
                                package: "jojo_flutter_base",
                              ),
                            ),
                          ],
                          ),
                        ],
                      ),
                    SizedBox(
                      width: context.dimensions.mediumSpacing.rdp,
                    ),
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.opaque, // 阻止事件冒泡
                        onTap: () {
                          // 显示购买确认对话框
                          SpaceMallGoodsConfirmBuyingDialog.show(
                            totalPrice: totalPrice,
                            payMethodType: ctrl.payMethodType,
                            onConfirm: () async {
                              _removeOverlay();
                              // 结算逻辑
                              bool isSuccess = await context
                                  .read<SpaceMyDressPageCtrl>()
                                  .actionBuySkin(0, 0, 0, context);
                              if (isSuccess) {
                                ctrl.state.balance =
                                    await ctrl.requestMyBalanceData();
                                ctrl.refreshState();
                              }
                            },
                          );
                        },
                        child: Container(
                          height: 44.rdp,
                          decoration: BoxDecoration(
                            color: context.appColors.mainColor,
                            borderRadius: BorderRadius.circular(30.rdp),
                          ),
                          child: Center(
                            child: Text(
                              S.of(context).checkout,
                              style: context.textstyles.headingEmphasis.pf.copyWith(color: context.appColors.jColorYellow6),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 展开的购物车商品列表
  Widget _buildExpandedCartList(BuildContext context, List<CustomCurrentUserDressUp> cartItems, SpaceMyDressPageCtrl ctrl, StateSetter overlaySetState) {
    // 限制显示的商品数量，最多4个，最少1个
    final displayItems = cartItems.take(4).toList();
    if (displayItems.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp, top: 28.rdp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(32.rdp),
          topRight: Radius.circular(32.rdp),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: displayItems.map((item) => _buildCartItem(context, item, ctrl, overlaySetState)).toList(),
      ),
    );
  }

  // 单个购物车商品项
  Widget _buildCartItem(BuildContext context, CustomCurrentUserDressUp item, SpaceMyDressPageCtrl ctrl, StateSetter overlaySetState) {
    String itemName = _getItemName(item, ctrl);
    final assetName = ctrl.payMethodType == PayMethodType.beans ? AssetsImg.MY_DRESS_MY_DRESS_CURRENCY_BEAN : AssetsImg.MY_DRESS_MY_DRESS_CURRENCY_GEM_STONE;

    return SizedBox(
      height: 80.rdp,
      child: Row(
        children: [
          ImageNetworkCached(
            width: 80.rdp,
            height: 80.rdp,
            imageUrl: item.currenTdressUp?.dressShowUrl ?? '',
            fit: BoxFit.contain,
            borderRadius: 18.rdp,
            memCacheWidth: 120.rdp.toInt() *
                MediaQuery.of(context).devicePixelRatio.toInt(),
          ),
          SizedBox(width: context.dimensions.smallSpacing.rdp),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  itemName,
                  style: context.textstyles.bodyTextEmphasis.pf
                      .copyWith(color: context.appColors.titleTextColor),
                ),
                Row(
                  children: [
                    ImageAssetWeb(
                      assetName: assetName,
                      width: 18.rdp,
                      height: 18.rdp,
                      fit: BoxFit.contain,
                      package: RunEnv.package,
                    ),
                    SizedBox(width: 2.rdp,),
                    Text(item.price ?? '0',
                        style: context.textstyles.remarkEmphasis.mrB.copyWith(
                          color: context.appColors.auxiliaryTextColor,
                          fontFamily: 'MohrRounded_Bold',
                          package: "jojo_flutter_base",
                        )),
                  ],
                ),
              ],
            ),
          ),

          // 删除按钮
          GestureDetector(
            onTap: () {
              _removeItemFromCart(item, ctrl, overlaySetState);
            },
            child: SizedBox(
              width: 24.rdp,
              height: 24.rdp,
              child: SvgAssetWeb(
                assetName: AssetsSvg.DRESS_SHOP_BUY_CAR_DELETE,
                package: RunEnv.package,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getItemName(CustomCurrentUserDressUp item, SpaceMyDressPageCtrl ctrl) {
    final itemId = item.currenTdressUp?.dressUpId;
    if (itemId == null) return '';

    final categoryButtons = ctrl.state.customSpaceMallResourceData?.categoryButtons ?? [];
    for (final categoryButton in categoryButtons) {
      final skins = categoryButton.customSpaceMallGoodsData?.skins ?? [];
      for (final skin in skins) {
        if (skin.goodsSkin?.goodId == itemId) {
          return skin.goodsSkin?.dressUpName ?? '';
        }
        final children = skin.goodsSkin?.children ?? [];
        for (final child in children) {
          if (child?.goodId == itemId) {
            return child?.dressUpName ?? '';
          }
        }
      }
    }

    return '';
  }

  // 从购物车中删除商品
  void _removeItemFromCart(CustomCurrentUserDressUp item, SpaceMyDressPageCtrl ctrl, StateSetter overlaySetState) {
    ctrl.removeItemFromCart(item);
    overlaySetState(() {});
    final remainingItems = _getCartItems(ctrl);
    if (remainingItems.isEmpty) {
      _removeOverlay();
    }
  }
}
