import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojoread_flutter_space_pkg/static/img.dart';

import '../../../common/host_env/host_env.dart';
import '../../../generated/l10n.dart';
import '../state.dart';

/// 购买确认对话框
class SpaceMallGoodsConfirmBuyingDialog extends StatelessWidget {
  final int totalPrice;
  final PayMethodType payMethodType;
  final Function() onConfirm;

  const SpaceMallGoodsConfirmBuyingDialog({
    Key? key,
    required this.totalPrice,
    required this.payMethodType,
    required this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final String currencyName = payMethodType == PayMethodType.beans ? S.of(context).beans : S.of(context).gemstone;

    return Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.only(top: 28.rdp, bottom: 0, left: 20.rdp, right: 20.rdp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(32.rdp)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 图标
          ImageAssetWeb(
            assetName: AssetsImg.MY_DRESS_MY_DRESS_CHECKOUT_ICON,
            width: 71.rdp,
            height: 64.rdp,
            fit: BoxFit.contain,
            package: RunEnv.package,
          ),
          SizedBox(height: 20.rdp),
          // 标题
          Text(
            S.of(context).confirmBuying,
            style: context.textstyles.headingLargeEmphasis.pf.copyWith(
              color: context.appColors.titleTextColor,
            ),
          ),
          SizedBox(height: context.dimensions.minimumSpacing.rdp),
          Text(
              S.of(context).myDressCheckoutTip(totalPrice, currencyName),
              textAlign: TextAlign.center,
              style: context.textstyles.heading.pf.copyWith(
                color: context.appColors.textColor,
              ),
          ),
          Text(
              S.of(context).myDressCheckoutSubTip,
              textAlign: TextAlign.center,
              style: context.textstyles.heading.pf.copyWith(
                color: context.appColors.textColor,
              ),
            ),
          SizedBox(height: 36.rdp),
          // 确认按钮
          GestureDetector(
            onTap: () {
              SmartDialog.dismiss();
              onConfirm();
            },
            child: Container(
              width: 280.rdp,
              height: 44.rdp,
              decoration: BoxDecoration(
                color: context.appColors.mainColor,
                borderRadius: BorderRadius.circular(22.rdp),
              ),
              child: Center(
                child: Text(
                  S.of(context).confirm,
                  style: context.textstyles.bodyTextLargeEmphasis.pf.copyWith(
                    color: context.appColors.jColorYellow6,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: context.dimensions.smallSpacing.rdp),
          GestureDetector(
            onTap: () {
              SmartDialog.dismiss();
            },
            child: Container(
              color: Colors.white,
              width: 280.rdp,
              height: 44.rdp,
              child: Center(
                child: Text(
                  S.of(context).thinkAgain,
                  style: context.textstyles.bodyTextLarge.pf.copyWith(
                    color: context.appColors.textColor,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 16.rdp + MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  /// 显示购买确认对话框
  static void show({
    required int totalPrice,
    required PayMethodType payMethodType,
    required Function() onConfirm,
  }) {
    SmartDialog.show(
      alignment: Alignment.bottomCenter,
      builder: (_) => SpaceMallGoodsConfirmBuyingDialog(
        totalPrice: totalPrice,
        payMethodType: payMethodType,
        onConfirm: onConfirm,
      ),
      clickMaskDismiss: true,
      maskColor: Colors.black.withOpacity(0.7),
    );
  }
}
