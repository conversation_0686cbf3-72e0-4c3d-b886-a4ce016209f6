import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_skin_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/state.dart';
import 'package:jojoread_flutter_space_pkg/utils/file_util.dart';
import 'package:jojoread_flutter_space_pkg/utils/snap_shoot_util.dart';
import 'package:spine_flutter/spine_flutter.dart';
import '../../eventBus/space_home_custom_eventbus.dart';

/// 商城舞台widget
class SpaceShopSnapshootStageWidget extends StatefulWidget {
  final ShopStagePersonData? stageData;
  final double bgHeight; //背景高度
  final GlobalKey repaintBoundaryKey;

  const SpaceShopSnapshootStageWidget({
    required this.stageData,
    required this.bgHeight,
    required this.repaintBoundaryKey,
    Key? key,
  }) : super(key: key);

  @override
  SpaceShopSnapshootStageWidgetState createState() =>
      SpaceShopSnapshootStageWidgetState();
}

class SpaceShopSnapshootStageWidgetState
    extends State<SpaceShopSnapshootStageWidget> {
  final double containerWidth = screen.screenWidth;
  //人物动画控制器
  JoJoSpineAnimationController stageController = JoJoSpineAnimationController();

  var personScale = 1.0; // 人物缩放比例
  StreamSubscription? _ChangeSkinAnimation;
  bool isNeedLoad = false;
  MallSnapshootnEvent? shootEvent;

  @override
  void initState() {
    super.initState();
    Atlas.filterQuality = FilterQuality.high;
    if (isPadPortrait()) {
      //pad 竖屏
      personScale = 1.5;
    }
    _ChangeSkinAnimation =
        jojoEventBus.on<MallSnapshootnEvent>().listen((event) {
      shootEvent = event;
      setState(() {
        isNeedLoad = true;
      });
    });
  }

  @override
  void dispose() {
    _ChangeSkinAnimation?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: containerWidth,
        height: widget.bgHeight,
        child: Stack(
          children: [
            //背景图
            if (widget.stageData?.bgImageFile?.isNotEmpty == true)
              Image.file(
                File(widget.stageData?.bgImageFile ?? ''),
                fit: BoxFit.fill,
                width: containerWidth,
                height: widget.bgHeight,
                errorBuilder: (BuildContext context, Object exception,
                    StackTrace? stackTrace) {
                  return Container(
                    color: HexColor('#FFF6E0'),
                  );
                },
              ),
            Positioned(
                bottom: 30.rdp, // 设置距离底部的距离
                left: 0,
                right: 0,
                child: isNeedLoad
                    ? RepaintBoundary(
                        key: widget.repaintBoundaryKey,
                        child: SizedBox(
                          width: 198.rdp * personScale,
                          height: 231.rdp * personScale,
                          child: JoJoSpineSkinWidget(
                            key: UniqueKey(),
                            widget.stageData?.spineData?.atlasFile ?? "",
                            widget.stageData?.spineData?.skelFile ?? "",
                            "shopStage",
                            controller: stageController,
                            skinList: widget.stageData?.spineData?.skinList,
                            loadSkinCallback: (status) {
                              var mShootEvent = shootEvent;
                              if (status == SkinStatus.success &&
                                  isNeedLoad &&
                                  mShootEvent != null) {
                                List<AnimationData>? animationList =
                                    mShootEvent.animationList;
                                startSnapshoots(animationList, stageController,
                                        widget.repaintBoundaryKey)
                                    .then((listOfMaps) {
                                  mShootEvent.completer?.complete(listOfMaps);
                                }).catchError((e) {
                                  mShootEvent.completer?.completeError(e);
                                });
                                isNeedLoad = false;
                              }
                            },
                          ),
                        ),
                      )
                    : Container()),
          ],
        ));
  }
}
