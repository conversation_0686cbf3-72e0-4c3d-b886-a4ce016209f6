import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/state.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/model/my_dress_balance_data.dart';
import '../home_page/model/space_home_currentskin_data.dart';
import '../home_page/model/space_home_resource_data.dart';
import '../home_page/model/space_mall_goods_data.dart';
import '../home_page/model/space_mall_resource_data.dart';

// 支付方式
enum PayMethodType {
  beans, // 学豆
  gemstone, // 宝石
}

class SpaceMyDressPageState {
  PageStatus pageStatus;
  Exception? exception;
  CustomSpaceHomeCurrentskinData? customSpaceHomeCurrentskinData; // 当前人物形象身上皮肤
  SpaceHomeResourceData? spaceHomeResourceData; // 首页资源(请求默认皮肤)
  CustomSpaceMallResourceData?
      customSpaceMallResourceData; //自定义tabbar数据，新增当前选择tab
  ShopStagePersonData? stagePersonData; //舞台小人数据
  NewMallGuideSkinData? guideSkinData; //引导换肤弹窗数据
  LiabilitiesInfo? balance; // 我的资产信息
  bool? hasDefalutSkin; //是否打开捏脸功能
  GlobalKey repaintBoundaryKey; // 快照使用
  String? ossPath; //快照oss地址
  SpaceMyDressPageState(
      {required this.pageStatus,
      required this.customSpaceHomeCurrentskinData,
      required this.customSpaceMallResourceData,
      required this.balance,
      this.stagePersonData,
      this.spaceHomeResourceData,
      this.guideSkinData,
      this.hasDefalutSkin,
      this.ossPath,
      required this.repaintBoundaryKey});

  SpaceMyDressPageState copyWith() {
    return SpaceMyDressPageState(
      pageStatus: pageStatus,
      customSpaceHomeCurrentskinData: customSpaceHomeCurrentskinData,
      customSpaceMallResourceData: customSpaceMallResourceData,
      balance: balance,
      stagePersonData: stagePersonData,
      guideSkinData: guideSkinData,
      spaceHomeResourceData: spaceHomeResourceData,
      hasDefalutSkin: hasDefalutSkin,
      ossPath: ossPath,
      repaintBoundaryKey: repaintBoundaryKey,
    );
  }
}

class CustomSpaceHomeCurrentskinData {
  //用于本地修改当前状态使用，如切换皮肤上传等
  SpaceHomeCurrentskinData? spaceHomeCurrentskinData;
  int? userId;
  String? userName;

  int? totalPrice;
  List<CustomCurrentUserDressUp>? customDressUps;
  CustomSpaceHomeCurrentskinData({
    required this.spaceHomeCurrentskinData,
    required this.userId,
    required this.userName,
    required this.totalPrice,
    required this.customDressUps,
  });
  CustomSpaceHomeCurrentskinData copyWith() {
    return CustomSpaceHomeCurrentskinData(
      spaceHomeCurrentskinData: spaceHomeCurrentskinData,
      userId: userId,
      userName: userName,
      customDressUps: customDressUps,
      totalPrice: totalPrice,
    );
  }
}

/// 新手换肤弹窗
class NewMallGuideSkinData {
  StageSpineData? spineData;
  List<DefaultSkinList>? defaultSkinList;

  NewMallGuideSkinData({this.spineData, this.defaultSkinList});
}

class CustomCurrentUserDressUp {
  CurrentUserDressUps? currenTdressUp; // user-dress-ups接口的皮肤
  int? status; // （0：未购买，1：已购买(拥有)，2：已使用）
  String? price;

  CustomCurrentUserDressUp({
    required this.currenTdressUp,
    required this.status,
    required this.price,
  });
  CustomCurrentUserDressUp copyWith() {
    return CustomCurrentUserDressUp(
      currenTdressUp: currenTdressUp,
      status: status,
      price: price,
    );
  }
}

class CustomSpaceMallResourceData {
  String? memberBonesZipUrl;
  String? backGroundBonesZipUrl;
  List<AnimationData>? animationList;
  List<CustomCategoryButton>? categoryButtons;
  int seletedCategoryId;
  Map<String?, String?>? dressUpRarityBackImgConfig; 
  int? bestContinuousLearnDays;
  int? purchaseSwitch; // 虚拟商品购买限制开关(1开启限制购买，0关闭限制购买)

  CustomSpaceMallResourceData({
    required this.memberBonesZipUrl,
    required this.backGroundBonesZipUrl,
    required this.categoryButtons,
    required this.seletedCategoryId,
    this.animationList,
    this.dressUpRarityBackImgConfig,
    this.bestContinuousLearnDays,
    this.purchaseSwitch,
  });

  CustomSpaceMallResourceData copyWith() {
    return CustomSpaceMallResourceData(
        memberBonesZipUrl: memberBonesZipUrl,
        backGroundBonesZipUrl: backGroundBonesZipUrl,
        categoryButtons: categoryButtons,
        seletedCategoryId: seletedCategoryId,
        animationList: animationList,
        dressUpRarityBackImgConfig: dressUpRarityBackImgConfig,
        bestContinuousLearnDays: bestContinuousLearnDays,
        purchaseSwitch: purchaseSwitch); 
  }

  /// 根据 dressUpLevel 获取对应的背景图片 URL
  String? getBackgroundImageUrl(String? dressUpLevel) {
    if (dressUpLevel == null || dressUpRarityBackImgConfig == null) {
      return null;
    }
    return dressUpRarityBackImgConfig![dressUpLevel];
  }
}

class AnimationData {
  final String type;
  final String animationgName;
  AnimationData({
    required this.type,
    required this.animationgName,
  });
  AnimationData copyWith() {
    return AnimationData(
      type: type,
      animationgName: animationgName,
    );
  }

  factory AnimationData.fromJson(Map<String, dynamic> json) {
    return AnimationData(
      type: json['type'],
      animationgName: json['animationgName'],
    );
  }
}

class CustomCategoryButton {
  CategoryButton? categoryButton;
  CustomSpaceMallGoodsData? customSpaceMallGoodsData; //自定义商品数据，新增当前选择商品
  int pageNum;
  bool resetRefreshToolStatus;
  CustomCategoryButton({
    required this.categoryButton,
    required this.customSpaceMallGoodsData,
    required this.pageNum,
    required this.resetRefreshToolStatus,
  });
  CustomCategoryButton copyWith() {
    return CustomCategoryButton(
      customSpaceMallGoodsData: customSpaceMallGoodsData,
      categoryButton: categoryButton,
      pageNum: pageNum,
      resetRefreshToolStatus: resetRefreshToolStatus,
    );
  }

  bool get shouldShowReachTips => categoryButton?.reachBuyThreshold != null &&
        categoryButton?.buyGoodsTips != null && categoryButton?.buyGoodsTips?.isNotEmpty == true;
}

class CustomSpaceMallGoodsData {
  List<CustomGoodsSkin>? skins;
  int? total;
  int? size;
  int? page;
  int? seletedSkinId;

  CustomSpaceMallGoodsData({
    required this.skins,
    required this.seletedSkinId,
    required this.total,
    required this.size,
    required this.page,
  });
  CustomSpaceMallGoodsData copyWith() {
    return CustomSpaceMallGoodsData(
      skins: skins,
      seletedSkinId: seletedSkinId,
      total: seletedSkinId,
      size: size,
      page: page,
    );
  }
}

class CustomGoodsSkin {
  GoodsSkin? goodsSkin;
  int? status; //用于本地修改当前状态使用，初始化使用内部status值
  CustomGoodsSkin({
    required this.goodsSkin,
    required this.status,
  });

  bool get isGroup =>
      goodsSkin?.children?.any((child) => child != null) ?? false;

  CustomGoodsSkin copyWith() {
    return CustomGoodsSkin(
      goodsSkin: goodsSkin,
      status: status,
    );
  }
}

//舞台小人
class ShopStagePersonData {
  StageSpineData? spineData; // 人物模型
  String? bgImageFile; // 背景图
  String? defaultSkinZip; // 默认皮肤包
  String? capturedImagePath; // 添加状态变量来存储捕获的图片路径
  List<AnimationData>? animationList;

  ShopStagePersonData(
      {this.spineData,
      this.bgImageFile,
      this.defaultSkinZip,
      this.capturedImagePath,
      this.animationList});

  ShopStagePersonData copyWith() {
    return ShopStagePersonData(
        spineData: spineData,
        bgImageFile: bgImageFile,
        defaultSkinZip: defaultSkinZip,
        capturedImagePath: capturedImagePath,
        animationList: animationList);
  }
}
