// 根据运行环境导入不同的包，文件内部必须实现相同的api
import 'package:flutter/foundation.dart';
import 'package:jojo_flutter_base/config/env_web.dart';
import 'package:jojo_flutter_base/host_env/host_env.dart';
import 'host_env_io.dart' if (dart.library.html) 'host_env_html.dart';

mixin HostEnv on BaseHostEnv {
  // 统一处理本地开发和打包时包名问题
  String? get package;

  // 非正式环境返回true
  bool get isTestEnv => EnvWeb.ENV_NAME != 'PRO' || kDebugMode;
}

final HostEnv RunEnv = hostEnv;
