import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/host_env/host_env_io.dart';
import 'host_env.dart';
// import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

// 获取宿主环境类型，打包成app时会导入这个文件
class HostEnvIO extends BaseHostEnvIO with HostEnv {
  @override
  String? get package => BaseConfig.debug ? null : 'jojoread_flutter_space_pkg';
}

final hostEnv = HostEnvIO();
