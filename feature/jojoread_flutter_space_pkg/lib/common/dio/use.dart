import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/net/base_interceptor.dart';
import 'package:jojo_flutter_base/net/http_log_interceptor.dart';
import 'package:jojo_flutter_base/net/net_manager.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import '../host_env/host_env.dart';
import 'custom_response.dart';
import 'debug_header.dart';
import 'web_response.dart';

Dio _useDio(Dio dio) {
  List<BaseInterceptor> customInterceptors = [
    DebugHeaderInterceptor(),
    CustomRespInterceptor(),
  ];

  List<BaseInterceptor> customWebInterceptors = [
    WebRespInterceptor(),
  ];

  if (RunEnv.isWeb) {
    dio.interceptors.insertAll(
      1,
      customWebInterceptors,
    );
  }

  int index = dio.interceptors.length;

  try {
    index =
        dio.interceptors.indexWhere((element) => element is HttpLogInterceptor);
  } catch (e) {
    l.e('_useDio', '获取拦截器索引失败');
  }

  /// 插入到 HttpLogInterceptor 拦截器之前
  dio.interceptors.insertAll(
    index,
    customInterceptors,
  );

  return dio;
}

final pageDio = _useDio(netManager.clientDio!);
final pageDioNoCookie = _useDio(netManagerNoCookie.clientDio!);
