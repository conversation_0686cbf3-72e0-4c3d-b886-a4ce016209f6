import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/net/base_interceptor.dart';

/// 响应拦截器=>返回数据转换
class CustomRespInterceptor extends BaseInterceptor {
  CustomRespInterceptor() : super(tag: 'CustomRespInterceptor');

  @override
  Future onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) async {
    bool isSuccess = false;

    if (response.statusCode != null && response.statusCode != 200) {
      isSuccess = true;
    }

    if (response.data['code'] == 'SUCCESS') {
      isSuccess = true;
    }

    if (response.data['info'] == 'success') {
      isSuccess = true;
    }

    if (isSuccess) {
      handler.next(Response(
        requestOptions: response.requestOptions,
        data: response.data['data'],
      ));
      return;
    }

    if (response.statusCode != null && response.statusCode != 200) {
      handler.reject(DioException.badResponse(
        statusCode: response.statusCode ?? -201,
        requestOptions: response.requestOptions,
        response: response,
      ));
      return;
    }

    if (response.data['code'] != 'SUCCESS') {
      handler.reject(DioException.badResponse(
        statusCode: response.statusCode ?? -202,
        requestOptions: response.requestOptions,
        response: response,
      ));
      return;
    }

    handler.reject(DioException.badResponse(
      statusCode: response.statusCode ?? -203,
      requestOptions: response.requestOptions,
      response: response,
    ));
  }
}
