import 'package:jojo_flutter_base/base.dart';
import 'package:jojoread_flutter_space_pkg/pages/character_customization/page.dart';
import 'package:jojoread_flutter_space_pkg/pages/history_list/page.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/page.dart';

import 'pages/detail_list/page.dart';
import 'pages/my_dress/page.dart';

enum AppPage {
  // 多人学主页
  spaceHome("/space/home"),
  // 我的装扮
  spaceMyStyle("/space/mydress"),
  // 多人学历史战绩
  spaceHistory("/space/history"),
  // 多人学得分明细
  spaceTeamDetails("/space/teamscore"),
  // 创建默认个人形象
  spaceCharacterCustomization("/space/Character"),
  ;

  const AppPage(this.path);

  final String path;

  static final routes = AppPage.values
      .map((page) => JoJoRoute(path: page.path, builder: page.builder))
      .toList();
}

extension PageRoute on AppPage {
  GoRouterWidgetBuilder get builder {
    switch (this) {
      case AppPage.spaceHome:
        return (context, state) => const SpaceHomePageModel();
      case AppPage.spaceHistory:
        return (context, state) => const SpaceHistoryPageModel();
      case AppPage.spaceTeamDetails:
        return (context, state) => SpaceTeamDetailsPageModel(
              int.parse(state.queryParams['teamId'] ?? "0"),
            );
      case AppPage.spaceMyStyle:
        return (context, state) => const SpaceMyDressPageModel();
      case AppPage.spaceCharacterCustomization:
        return (context, state) => const SpaceCharacterPageModel();
    }
  }
}
