import 'dart:io';
import 'package:archive/archive.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_skin_widget.dart';
import 'package:jojo_flutter_base/utils/iterable_extension.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_currentskin_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/page.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/dress_controller.dart';
import 'package:path/path.dart' as path;

import '../pages/home_page/model/space_home_group_data.dart';

/// 解压zip包到zip包平级的目录下
Future<String> unzip(String filepath) async {
  final file = File(filepath);
  String filename = getFileNameWithoutExtension(file);
  Directory dir = file.parent;
  String dirPath = path.join(dir.path, filename);

  bool exist = await Directory(dirPath).exists();
  print("测试数据:$dirPath");
  if (exist) {
    l.i(tag, "文件夹已存在，正在删除: $dirPath");
    await Directory(dirPath).delete(recursive: true);
  }

  // Read the Zip file from disk.
  final bytes = file.readAsBytesSync();

  // Decode the Zip file
  final archive = ZipDecoder().decodeBytes(bytes);

  // Extract the contents of the Zip archive to disk.
  for (final file in archive) {
    final filename = file.name;
    final absolutePath = path.join(dirPath, filename);
    if (file.isFile) {
      final data = file.content as List<int>;
      File(absolutePath)
        ..createSync(recursive: true)
        ..writeAsBytesSync(data);
    } else {
      Directory(absolutePath).createSync(recursive: true);
    }
  }

  return dirPath;
}

/// 获取去掉后缀的文件名称
String getFileNameWithoutExtension(File file) {
  String fileName = file.uri.pathSegments.last;
  int lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex != -1) {
    return fileName.substring(0, lastDotIndex);
  } else {
    return fileName;
  }
}

/// 递归查找目标文件所在的目录 BFS算法
/// [rootPath] 根目录全路径地址
/// [target] 文件名称
Future<String?> findDirectory(String rootPath, String target) async {
  bool isDirectory = await FileSystemEntity.isDirectory(rootPath);
  if (isDirectory) {
    final list = Directory(rootPath).listSync();
    while (list.isNotEmpty) {
      FileSystemEntity entity = list.removeAt(0);
      String dirPath = entity.path;

      if (await FileSystemEntity.isDirectory(dirPath)) {
        String filepath = path.join(dirPath, target);
        bool hasTarget = await File(filepath).exists();
        if (hasTarget) {
          return dirPath;
        }

        final children = Directory(dirPath).listSync();
        list.addAll(children);
      }
    }
  }

  return null;
}

/// 在指定目录下查找具有特定后缀名的第一个文件
File? findFilesByExtension(String directoryPath, String extension) {
  // 创建 Directory 对象
  final directory = Directory(directoryPath);

  // 检查目录是否存在
  if (!directory.existsSync()) {
    throw Exception('目录不存在: $directoryPath');
  }

  // 列出目录中的所有文件和子目录
  final files = directory.listSync(recursive: true);

  // 过滤出具有特定后缀名的文件
  return files.whereType<File>().firstWhereOrNull((file) =>
      file.path.endsWith(extension) &&
      !path.basename(file.path).startsWith('.'));
}

/// 在指定目录中逐层查找具有特定后缀名的第一个文件
File? findFilesByExtensionByDic(String directoryPath, String extension) {
  // 创建 Directory 对象
  final directory = Directory(directoryPath);

  // 检查目录是否存在
  if (!directory.existsSync()) {
    l.e(logTag, '目录不存在: $directoryPath');
    return null;
  }

  // 初始化待处理的目录队列
  List<Directory> queue = [directory];

  while (queue.isNotEmpty) {
    // 从队列中取出当前目录
    final currentDir = queue.removeAt(0);

    // 列出当前目录中的所有文件和子目录
    final entities = currentDir.listSync();

    for (var entity in entities) {
      if (entity is File) {
        // 如果找到符合后缀名的文件，返回文件的路径
        if (entity.path.endsWith(extension) &&
            !path.basename(entity.path).startsWith('.')) {
          return entity; // 找到第一个符合条件的文件
        }
      } else if (entity is Directory) {
        // 如果是子目录，添加到队列中进行后续查找
        queue.add(entity);
      }
    }
  }

  return null; // 未找到符合条件的文件
}

//在指定目录下查找具有名称的文件
File? findFilesByName(String directoryPath, String fileName) {
  // 创建 Directory 对象
  final directory = Directory(directoryPath);

  // 检查目录是否存在
  if (!directory.existsSync()) {
    l.e(logTag, '目录不存在: $directoryPath');
    return null;
  }

  // 列出目录中的所有文件和子目录
  final files = directory.listSync(recursive: true);

  // 过滤出具有特定名称的文件
  return files
      .whereType<File>()
      .firstWhereOrNull((file) => path.basename(file.path) == fileName);
}

///在指定目录下查找具有特定后缀名的文件集合
List<File> findImagesByExtensions(
    String directoryPath, List<String> extensions) {
  // 创建 Directory 对象
  final directory = Directory(directoryPath);

  // 检查目录是否存在
  if (!directory.existsSync()) {
    l.e(logTag, '目录不存在: $directoryPath');
    return [];
  }

  // 列出目录中的所有文件和子目录
  final files = directory.listSync(recursive: true);

  // 过滤出具有特定后缀名的文件
  return files
      .whereType<File>()
      .where((file) =>
          extensions.any(
              (extension) => file.path.toLowerCase().endsWith(extension)) &&
          !path.basename(file.path).startsWith('.'))
      .toList();
}

///是否是pad的竖版
bool isPadPortrait() {
  return screen.screenSize.shortestSide >= 480 &&
      Orientation.portrait == screen.orientation;
}

//判断皮肤是否有变动
bool areSkinListsEqual(
    List<Skin>? listA, List<SkinModel>? listB, Map resources) {
  if (listA == null || listB == null) {
    return true;
  }
  // 检查列表长度是否相等
  if (listA.length != listB.length) {
    return true;
  }
  final zipSet = listB.map((b) => b.zipLocalFile).toSet();

  for (var a in listA) {
    if (!zipSet.contains(resources[a.resourcesZipUrl])) {
      return true;
    }
  }

  return false; // 如果长度相等且所有 URL 都在 zip 中
}

//判断皮肤是否有变动
bool areMineSkinListsEqual(
    List<CurrentUserDressUps>? listA, List<SkinModel>? listB, Map resources) {
  if (listA == null || listB == null) {
    return true;
  }
  // 检查列表长度是否相等
  if (listA.length != listB.length) {
    return true;
  }
  final zipSet = listB.map((b) => b.zipLocalFile).toSet();

  for (var a in listA) {
    if (!zipSet.contains(resources[a.resourcesZipUrl])) {
      return true;
    }
  }

  return false; // 如果长度相等且所有 URL 都在 zip 中
}

//判断皮肤是否有变动
bool areChangeSkinListsEqual(List<String>? listA, List<SkinModel>? listB) {
  if (listA == null || listB == null) {
    return false;
  }
  // 检查列表长度是否相等
  if (listA.length != listB.length) {
    return false;
  }
  final zipSet = listB.map((b) => b.zipLocalFile).toSet();

  for (var a in listA) {
    if (!zipSet.contains(a)) {
      return true;
    }
  }

  return false;
}

extension StringListExtension on List<String> {
  addIfNotEmpty(String? value) {
    if (value != null && value.isNotEmpty == true) {
      add(value);
    }
  }

  addListsIfNotEmpty(Iterable<String?> list) {
    for (var element in list) {
      addIfNotEmpty(element);
    }
  }
}
