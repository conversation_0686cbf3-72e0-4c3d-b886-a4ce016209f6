import 'dart:async';

import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:path_provider/path_provider.dart';
import 'package:jojo_flutter_base/utils/file_util.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:flutter/rendering.dart';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/cupertino.dart';

import '../pages/my_dress/state.dart';

Future<String?> capturePng(GlobalKey? repaintBoundaryKey,
    {String? type}) async {
  try {
    RenderRepaintBoundary boundary = repaintBoundaryKey?.currentContext!
        .findRenderObject() as RenderRepaintBoundary;
    // 使用更高的 pixelRatio 来提高图片清晰度 RepaintBoundary 的逻辑尺寸是 180x210
    //像素：pixelRatio: 1.0：生成的图像尺寸为 180x210 像素。
    //pixelRatio: 2.0：生成的图像尺寸为 360x420 像素。pixelRatio:
    //3.0：生成的图像尺寸为 540x630 像素。
    ui.Image image = await boundary.toImage(pixelRatio: 3.0);
    // ByteData? byteData =
    //     await image.toByteData(format: ui.ImageByteFormat.png);
    // Uint8List pngBytes = byteData!.buffer.asUint8List();

    // 获取原始图像的宽度和高度
    int originalWidth = image.width;
    int originalHeight = image.height;

    // 计算裁剪区域的宽度和高度，保持宽高比为 2:2.4
    double targetAspectRatio = 2 / 2.4;
    int cropHeight = originalHeight;
    int cropWidth = (cropHeight * targetAspectRatio).toInt();

    // 计算裁剪区域的左上角坐标，使其居中
    int left = (originalWidth - cropWidth) ~/ 2;
    int top = 0;

    // 裁剪图像
    ui.Image croppedImage =
        await _cropImage(image, left, top, cropWidth, cropHeight);

    // 将裁剪后的图像转换为字节数据
    ByteData? croppedByteData =
        await croppedImage.toByteData(format: ui.ImageByteFormat.png);
    Uint8List croppedPngBytes = croppedByteData!.buffer.asUint8List();

    // 获取应用文档目录
    final directory = await getApplicationDocumentsDirectory();
    // 创建或获取 space_spine_snapshoot 文件夹
    String dirpath = type == "daily"
        ? "space_spine_snapshoot_"
        : "space_spine_snapshoot_activity_"; //生成的截图分为日常和活动两种，日常的需要在本地使用一个小时时限的，活动的上传完毕之后不再使用
    final snapshootDir = Directory(
        '${directory.path}/$dirpath${BaseConfig.share.userInfo?.uid}');
    if (!await snapshootDir.exists()) {
      await snapshootDir.create(recursive: true);
      print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻Created directory: ${snapshootDir.path}');
    }
    // 生成唯一文件名
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final imagePath = '${snapshootDir.path}/spine_snapshoot_$timestamp.png';
    File imgFile = File(imagePath);

    // 扫描并删除旧的截图文件
    final files = snapshootDir.listSync().where(
        (entity) => entity is File && entity.path.contains('spine_snapshoot_'));
    for (var file in files) {
      await file.delete();
      print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻Deleted old image: ${file.path}');
    }

    // 保存新文件
    await imgFile.writeAsBytes(croppedPngBytes);
    print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻本地保存地址: $imagePath');
    return imagePath;
  } catch (e) {
    print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻Error capturing image: $e');
  }
  return null;
}

Future<ui.Image> _cropImage(
    ui.Image image, int left, int top, int width, int height) async {
  final recorder = ui.PictureRecorder();
  final canvas = ui.Canvas(recorder);
  final paint = ui.Paint();

  // 绘制裁剪区域
  canvas.drawImageRect(
    image,
    Rect.fromLTWH(
        left.toDouble(), top.toDouble(), width.toDouble(), height.toDouble()),
    Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
    paint,
  );

  final picture = recorder.endRecording();
  final img = await picture.toImage(width, height);
  return img;
}

//上传生成的快照图片
Future<String?> uploadCapturedImage(String? imagePath) async {
  try {
    var result = await uploadFileExt(
      imagePath ?? '',
      "baby-avatar",
      fileKeyPrefix: '/personal-image',
      progressListener: (p0) {
        print(
            '💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻上传进度: ${(p0 * 100).toStringAsFixed(2)}%');
      },
      failCallback: (msg) {
        print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻上传失败: $msg');
      },
    );
    if (result != null) {
      //上传成功
      String originalImage = result.objectStorageUrl ?? '';
      String httpImage = result.uploadPolicyItemList?[0].previewUrl ?? '';
      print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻上传成功: $originalImage');
      print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻上传成功: $httpImage');
      return originalImage;
    } else {
      //上传失败
      print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻上传失败');
      return '';
    }
  } catch (e) {
    print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻上传过程中发生错误: $e');
    return '';
  }
}

//开始截图并上传，截图会存在多张
Future<List<Map<String, dynamic>>> startSnapshoots(
    List<AnimationData>? animationList,
    JoJoSpineAnimationController controller,
    GlobalKey repaintBoundaryKey) async {
  List<Map<String, dynamic>> resultList = [];

  int animationLength = animationList?.length ?? 0;
  for (int i = 0; i < animationLength; i++) {
    String? animationName = animationList?[i].animationgName ?? "idle";
    String? type = animationList?[i].type;

    String? imagePath = await _playAnimationAndWaitForComplete(
        animationName, controller, repaintBoundaryKey, type);
    resultList.add({
      'type': type,
      'dressImg': imagePath,
    });
  }
  return resultList; // 返回 List<Map>
}

Future<String?> _playAnimationAndWaitForComplete(
    String animationName,
    JoJoSpineAnimationController controller,
    GlobalKey repaintBoundaryKey,
    String? animationType) async {
  Completer<void> completer = Completer();
  String? ossPath;
  try {
    controller.playAnimation(JoJoSpineAnimation(
      animaitonName: animationName,
      loop: false,
      trackIndex: 0,
      listener: (type) async {
        print("💃🏻💃🏻💃🏻💃🏻$type");
        if (type == AnimationEventType.complete) {
          try {
            String? imagePath =
                await capturePng(repaintBoundaryKey, type: animationType);
            ossPath = await uploadCapturedImage(imagePath);
          } catch (e) {
            print("捕获或上传图片失败: $e");
          } finally {
            completer.complete();
          }
        }
      },
    ));
  } catch (e) {
    print("播放动画失败: $e");
    completer.complete();
  }
  await completer.future;
  return ossPath;
}

void deleteSnapshotWhenError() async {
  // 获取应用文档目录
  final directory = await getApplicationDocumentsDirectory();
  // 创建或获取 space_spine_snapshoot 文件夹
  String dirpath = "space_spine_snapshoot_";
  final snapshootDir =
      Directory('${directory.path}/$dirpath${BaseConfig.share.userInfo?.uid}');
  if (await snapshootDir.exists()) {
    // 扫描并删除截图文件
    final files = snapshootDir.listSync().where(
        (entity) => entity is File && entity.path.contains('spine_snapshoot_'));
    for (var file in files) {
      await file.delete();
      print('💃🏻💃🏻💃🏻💃🏻💃🏻💃🏻Deleted old image: ${file.path}');
    }
  }
}
