import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojoread_flutter_space_pkg/page.dart';

import 'generated/l10n.dart';

// ignore: camel_case_types
class jojoread_flutter_space_pkg extends JoJoModule {
  @override
  List<JoJoRoute> get routes => AppPage.routes;

  @override
  List<LocalizationsDelegate<dynamic>> get localizationsDelegates =>
      const [S.delegate];

  @override
  Future<void> didRegister(args) async {}

  @override
  List<String>? get grayscaleKeys => [];

  @override
  void didUnregister(args) {}
}
