import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/jojo_flutter_run.dart';
import 'package:jojo_flutter_base/widgets/popup/popup.dart';
import 'package:jojoread_flutter_space_pkg/page.dart';

import 'common/bridge/mock_brige.dart';
import 'common/config/env_web.dart';
import 'common/host_env/host_env.dart';
import 'module.dart';

void main() {
  {
    jojoread_flutter_space_pkg module = jojoread_flutter_space_pkg();
    JoJoModuleManager.addModules([module]);

    jojoFlutterRun(() async {
      JoJoRouter.init(
          // initialLocation: '${AppPage.spaceTeamDetails.path}?teamId=1',
          // initialLocation: AppPage.spaceHome.path,
          // initialLocation: AppPage.spaceHistory.path,
          initialLocation: AppPage.spaceMyStyle.path,
          observers: [JoJoPopup.observer],
          routes: module.routes);

      if (!RunEnv.isWeb) {
        JoJoNativeBridge.registerMocker(JoJoBridgeCommonMocker());
      }
      await BaseConfig.share.init(RunEnv.isWeb ? EnvWeb.ENV_NAME : null);
    },
        packageName: 'jojo_flutter_space_pkg',
        debugMode: RunEnv.isTestEnv,
        onAppReady: () async {});
  }
}
