import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/base_namespace.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_history_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_group_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/home_page/model/space_home_pop_data.dart';
import 'package:jojoread_flutter_space_pkg/pages/my_dress/model/my_dress_balance_data.dart';
import '../common/dio/use.dart';
import '../pages/home_page/model/space_home_contributions_data.dart';
import '../pages/home_page/model/space_home_currentskin_data.dart';
import '../pages/home_page/model/space_home_resource_data.dart';
import '../pages/home_page/model/space_home_team_data.dart';
import '../pages/home_page/model/space_mall_goods_data.dart';
import '../pages/home_page/model/space_mall_resource_data.dart';
part 'space_home_api.g.dart';

@RestApi()
abstract class SpaceHomeApi {
  factory SpaceHomeApi(Dio dio, {String baseUrl}) = _SpaceHomeApi;
  // 用户所在战队
  @GET("/api/pagani/group-study/teams")
  Future<SpaceHomeTeamData> requestUserTeamsInfo();

  // 获取分组详情
  @GET("/api/pagani/group-study/teams/{team_id}")
  Future<SpaceHomeGroupData> requestSpaceHomeGroupInfo(
    @Path('team_id') int team_id,
  );

  // 获取首页资源
  @GET("/api/pagani/group-study/homepage-resources")
  Future<SpaceHomeResourceData> requestSpaceHomeResourceInfo();

  // 获取当前皮肤详情
  @GET("/api/pagani/group-study/user-dress-ups")
  Future<SpaceHomeCurrentskinData> requestCurrentSkinInfo();

  //装扮商城资源
  @GET("/api/pagani/group-study/mall-resources")
  Future<SpaceMallResourceData> requestMallResourceInfo();

  //得分明细
  @GET("/api/pagani/group-study/teams/{team_id}/contributions")
  Future<SpaceHomeContributionsData> requestTeamContributionsInfo(
    @Path('team_id') int team_id,
  );

  //成员购买装扮
  @POST("/api/pagani/group-study/dress-up-orders")
  Future<dynamic> requestBuySkinInfo(
    @Field('dressUpIds') List<int> skins,
  );

  //成员装扮保存
  @POST("/api/pagani/group-study/dress-ups")
  Future<dynamic> saveSkinInfo(
    @Field('dressUpIds') List<int> skins,
    @Field('dressImg') String dressImg,
    @Field('dressImgList') List<Map> dressImgList,
  );

  //切换资产类型接口
  @POST("/api/college/popup/callback")
  Future<dynamic> balanceSwitchCallBack(@Body() Map<String, dynamic> map);

  //装扮商城列表
  @GET("/api/pagani/group-study/shops")
  Future<SpaceMallGoodsData> requestMallShopInfo(
    @Query('categoryId') int categoryId,
    @Query('pageSize') int pageSize,
    @Query('pageNum') int pageNum,
  );

  //历史战绩详情
  @GET("/api/pagani/group-study/team-history-records")
  Future<SpaceHistoryData> requestHistoryPerformancesInfo();

  //结算弹窗
  @GET("/api/pagani/popups")
  Future<SpaceHomePopData> requestPopupsInfo(
    @Query('scene') String scene,
  );

  //获取资产信息
  @GET("/api/pagani/assets")
  Future<LiabilitiesInfo> getLiabilitiesInfo(
      @Query("classKey") String classKey, @Query("scene") String scene);
}

final spaceHomeApiService =
    SpaceHomeApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);
final spaceHomeApiServiceBase =
    SpaceHomeApi(pageDio, baseUrl: BaseAddress.baseApiPath);
