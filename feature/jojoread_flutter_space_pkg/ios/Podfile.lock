PODS:
  - audioplay<PERSON>_da<PERSON>win (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - FMDB (2.7.11):
    - FMDB/standard (= 2.7.11)
  - FMDB/standard (2.7.11)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sensors_analytics_flutter_plugin (2.2.0):
    - Flutter
    - SensorsAnalyticsSDK (>= 4.4.6)
  - SensorsAnalyticsSDK (4.8.2):
    - SensorsAnalyticsSDK/Core (= 4.8.2)
  - SensorsAnalyticsSDK/__Store (4.8.2)
  - SensorsAnalyticsSDK/AutoTrack (4.8.2):
    - SensorsAnalyticsSDK/Common
  - SensorsAnalyticsSDK/Base (4.8.2):
    - SensorsAnalyticsSDK/__Store
  - SensorsAnalyticsSDK/Common (4.8.2):
    - SensorsAnalyticsSDK/Base
  - SensorsAnalyticsSDK/Core (4.8.2):
    - SensorsAnalyticsSDK/Base
    - SensorsAnalyticsSDK/Common
    - SensorsAnalyticsSDK/Visualized
  - SensorsAnalyticsSDK/Visualized (4.8.2):
    - SensorsAnalyticsSDK/AutoTrack
  - Sentry/HybridSDK (7.31.5)
  - sentry_flutter (0.0.1):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 7.31.5)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - spine_flutter (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - TOCropViewController (2.6.1)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - sensors_analytics_flutter_plugin (from `.symlinks/plugins/sensors_analytics_flutter_plugin/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - spine_flutter (from `.symlinks/plugins/spine_flutter/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - FMDB
    - SensorsAnalyticsSDK
    - Sentry
    - TOCropViewController

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  sensors_analytics_flutter_plugin:
    :path: ".symlinks/plugins/sensors_analytics_flutter_plugin/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  spine_flutter:
    :path: ".symlinks/plugins/spine_flutter/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  FMDB: 57486c1117fd8e0e6b947b2f54c3f42bf8e57a4e
  image_cropper: a3291c624a953049bc6a02e1f8c8ceb162a24b25
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  package_info_plus: 6c92f08e1f853dc01228d6f553146438dafcd14e
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  sensors_analytics_flutter_plugin: 95bfb7435b259626ea6705cd2f4a12f116052e15
  SensorsAnalyticsSDK: 768b3e0c45f185e2bd10d82ea9dbac8fc4d353b1
  Sentry: 4c9babff9034785067c896fd580b1f7de44da020
  sentry_flutter: 1346a880b24c0240807b53b10cf50ddad40f504e
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  spine_flutter: 029584e4272785b636238b7ce1a475f85d9ada08
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  webview_flutter_wkwebview: 2e2d318f21a5e036e2c3f26171342e95908bd60a

PODFILE CHECKSUM: ef19549a9bc3046e7bb7d2fab4d021637c0c58a3

COCOAPODS: 1.15.2
